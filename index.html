<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>

    <!-- Modern fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-green: #7DD3AE;
            --primary-green-dark: #5BC192;
            --primary-green-light: #A8E6C8;
            --accent-purple: #8B5CF6;
            --accent-orange: #F97316;
            --text-primary: #0F172A;
            --text-secondary: #64748B;
            --text-muted: #94A3B8;
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8FAFC;
            --bg-accent: #F1F5F9;
            --border-light: #E2E8F0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--bg-secondary);
            overflow-x: hidden;
        }

        .container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .animate-scale-in {
            animation: scaleIn 0.6s ease-out forwards;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav {
            display: flex;
            gap: 40px;
            align-items: center;
        }

        .nav a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 16px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav a:hover {
            color: var(--primary-green);
        }

        .nav a::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-green);
            transition: width 0.3s ease;
        }

        .nav a:hover::after {
            width: 100%;
        }

        .language-switcher {
            display: flex;
            gap: 8px;
            margin-right: 20px;
        }

        .language-switcher a {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .language-switcher a.active {
            background: var(--primary-green);
            color: white;
        }

        .language-switcher a:not(.active) {
            color: var(--text-muted);
        }

        .language-switcher a:not(.active):hover {
            background: var(--bg-accent);
            color: var(--text-secondary);
        }

        .cta-button {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
            color: white;
            padding: 14px 28px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(125, 211, 174, 0.5);
        }

        /* Enhanced header animations */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 20px 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-light);
            transition: all 0.3s ease;
        }

        .header.scrolled {
            padding: 12px 0;
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-lg);
        }

        .logo {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 28px;
            font-weight: 700;
            color: var(--primary-green);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo svg {
            transition: transform 0.3s ease;
        }

        .logo:hover svg {
            transform: rotate(5deg);
        }

        /* Hero Section */
        .hero {
            padding: 140px 0 120px;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%237DD3AE' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            pointer-events: none;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .hero h1 {
            font-family: 'Space Grotesk', sans-serif;
            font-size: clamp(48px, 8vw, 80px);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 24px;
            color: var(--text-primary);
        }

        .hero .highlight {
            position: relative;
            display: inline-block;
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero .highlight::after {
            content: '';
            position: absolute;
            bottom: 8px;
            left: -8px;
            right: -8px;
            height: 12px;
            background: var(--primary-green-light);
            opacity: 0.3;
            border-radius: 6px;
            z-index: -1;
            animation: highlightPulse 3s ease-in-out infinite;
        }

        @keyframes highlightPulse {
            0%, 100% { opacity: 0.3; transform: scaleX(1); }
            50% { opacity: 0.5; transform: scaleX(1.05); }
        }

        .hero p {
            font-size: 22px;
            color: var(--text-secondary);
            max-width: 700px;
            margin: 0 auto 48px;
            line-height: 1.7;
        }

        .hero-cta {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
            color: white;
            padding: 18px 36px;
            border-radius: 16px;
            text-decoration: none;
            font-weight: 600;
            font-size: 18px;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .hero-cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .hero-cta:hover::before {
            left: 100%;
        }

        .hero-cta:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 25px 50px rgba(125, 211, 174, 0.5);
        }

        .hero-cta svg {
            transition: transform 0.3s ease;
        }

        .hero-cta:hover svg {
            transform: translateX(4px);
        }

        .hero-visual {
            position: absolute;
            top: 50%;
            right: -200px;
            transform: translateY(-50%);
            width: 600px;
            height: 600px;
            opacity: 0.08;
            pointer-events: none;
        }

        /* Enhanced floating animations */
        @keyframes floatSlow {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(5deg); }
        }

        @keyframes floatMedium {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(-3deg); }
        }

        @keyframes floatFast {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(2deg); }
        }

        .float-slow { animation: floatSlow 8s ease-in-out infinite; }
        .float-medium { animation: floatMedium 6s ease-in-out infinite; }
        .float-fast { animation: floatFast 4s ease-in-out infinite; }

        /* Services Section - Horizontal Scroll */
        .services {
            padding: 120px 0;
            background: var(--bg-primary);
            position: relative;
            overflow: hidden;
        }

        .section-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .section-title {
            font-family: 'Space Grotesk', sans-serif;
            font-size: clamp(36px, 6vw, 56px);
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        .section-subtitle {
            font-size: 20px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.7;
        }

        .services-container {
            position: relative;
            overflow: hidden;
        }

        .services-scroll {
            display: flex;
            gap: 40px;
            padding: 20px 0;
            overflow-x: auto;
            scroll-behavior: smooth;
            scrollbar-width: none;
            -ms-overflow-style: none;
            padding-left: max(24px, calc((100vw - 1280px) / 2));
            padding-right: max(24px, calc((100vw - 1280px) / 2));
        }

        .services-scroll::-webkit-scrollbar {
            display: none;
        }

        .services-scroll.no-scroll {
            overflow-x: hidden;
        }

        .service-card {
            background: var(--bg-primary);
            padding: 48px 36px;
            border-radius: 24px;
            border: 1px solid var(--border-light);
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            min-width: 380px;
            flex-shrink: 0;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-green) 0%, var(--accent-purple) 100%);
            transform: scaleX(0);
            transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-origin: left;
        }

        .service-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
            z-index: 0;
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-card:hover::after {
            opacity: 0.02;
        }

        .service-card:hover {
            transform: translateY(-16px) scale(1.02);
            box-shadow: 0 30px 60px rgba(125, 211, 174, 0.15);
            border-color: var(--primary-green-light);
        }

        .service-card > * {
            position: relative;
            z-index: 1;
        }

        .service-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
            border-radius: 16px;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-md);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .service-card:hover .service-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 15px 30px rgba(125, 211, 174, 0.4);
        }

        .service-icon svg {
            transition: transform 0.3s ease;
        }

        .service-card:hover .service-icon svg {
            transform: scale(1.1);
        }

        .service-card h3 {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .service-card p {
            color: var(--text-secondary);
            line-height: 1.7;
            font-size: 16px;
        }

        /* Process Section */
        .process {
            padding: 120px 0;
            background: var(--bg-secondary);
            position: relative;
        }

        .process-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 48px;
            margin-top: 80px;
        }

        .process-step {
            text-align: center;
            position: relative;
        }

        .process-number {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-family: 'Space Grotesk', sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .process-step h3 {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .process-step p {
            color: var(--text-secondary);
            line-height: 1.7;
        }

        /* CTA Section */
        .cta-section {
            padding: 120px 0;
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
            position: relative;
            overflow: hidden;
        }

        .cta-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            pointer-events: none;
        }

        .cta-content {
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .cta-section h2 {
            font-family: 'Space Grotesk', sans-serif;
            font-size: clamp(36px, 6vw, 56px);
            font-weight: 700;
            margin-bottom: 24px;
            color: white;
        }

        .cta-section p {
            font-size: 20px;
            margin-bottom: 48px;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            color: white;
            line-height: 1.7;
        }

        .cta-section .cta-button {
            background: white;
            color: var(--primary-green);
            font-size: 18px;
            padding: 18px 36px;
            box-shadow: var(--shadow-lg);
        }

        .cta-section .cta-button:hover {
            background: var(--bg-secondary);
            transform: translateY(-3px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        /* Footer */
        .footer {
            padding: 64px 0 32px;
            background: var(--text-primary);
            color: white;
            text-align: center;
        }

        .footer p {
            color: var(--text-muted);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav {
                display: none;
            }

            .hero h1 {
                font-size: 40px;
            }

            .hero p {
                font-size: 18px;
            }

            .section-title {
                font-size: 36px;
            }

            .services-grid,
            .process-grid {
                grid-template-columns: 1fr;
                gap: 32px;
            }

            .container {
                padding: 0 16px;
            }

            .hero {
                padding: 100px 0 80px;
            }

            .services,
            .process,
            .cta-section {
                padding: 80px 0;
            }
        }
    </style>

    <title>GibbonLab: Web & Mobile Design and Development for Global Businesses & Founders – Scalable, High-Quality, No Hassle</title>
    <meta name="description" content="Need high-quality design and development work without the hassle? GibbonLab delivers top-tier design and development services with no hiring drama or delays. Flexible, subscription-based solutions tailored to your business needs—pause or start anytime!"/>

    <link rel="icon" href="favicon.ico" type="image/x-icon" sizes="32x32"/>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">
                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <circle cx="16" cy="16" r="16" fill="url(#logoGradient)"/>
                        <path d="M12 10h8v2h-6v4h5v2h-5v4h6v2h-8V10z" fill="white"/>
                        <defs>
                            <linearGradient id="logoGradient" x1="0" y1="0" x2="32" y2="32">
                                <stop stop-color="#7DD3AE"/>
                                <stop offset="1" stop-color="#5BC192"/>
                            </linearGradient>
                        </defs>
                    </svg>
                    GibbonLab
                </a>
                <nav class="nav">
                    <div class="language-switcher">
                        <a href="#" class="active">FR</a>
                        <a href="#">EN</a>
                    </div>
                    <a href="#services">Nos Services</a>
                    <a href="#expertise">Notre Expertise</a>
                    <a href="#process">Notre Processus</a>
                    <a href="#contact">Contact</a>
                    <a href="#contact" class="cta-button">Démarrer un projet</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content animate-fade-in-up">
                <h1>Nous créons des <span class="highlight">applications exceptionnelles</span> qui transforment vos idées en succès</h1>
                <p>De la conception au développement, nous accompagnons les entreprises ambitieuses dans la création d'expériences digitales mémorables. Design élégant, code robuste, résultats mesurables.</p>
                <a href="#contact" class="hero-cta">
                    Démarrer votre projet
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M4.167 10h11.666M10 4.167L15.833 10 10 15.833" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </a>
            </div>
        </div>

        <!-- Beautiful elaborate floating SVGs -->
        <div class="hero-visual">
            <svg viewBox="0 0 800 800" fill="none" style="position: absolute; top: -100px; right: -300px; width: 800px; height: 800px;">
                <defs>
                    <linearGradient id="heroGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#7DD3AE" stop-opacity="0.12"/>
                        <stop offset="100%" stop-color="#5BC192" stop-opacity="0.06"/>
                    </linearGradient>
                    <linearGradient id="heroGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#8B5CF6" stop-opacity="0.1"/>
                        <stop offset="100%" stop-color="#F97316" stop-opacity="0.05"/>
                    </linearGradient>
                    <linearGradient id="heroGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#06B6D4" stop-opacity="0.08"/>
                        <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.04"/>
                    </linearGradient>
                </defs>

                <!-- Large floating circles -->
                <circle cx="400" cy="200" r="150" fill="url(#heroGradient1)" class="float-slow"/>
                <circle cx="600" cy="400" r="100" fill="url(#heroGradient2)" class="float-medium"/>
                <circle cx="200" cy="500" r="80" fill="url(#heroGradient3)" class="float-fast"/>

                <!-- Geometric shapes -->
                <rect x="300" y="450" width="120" height="120" rx="25" fill="url(#heroGradient1)" transform="rotate(25 360 510)" class="float-medium"/>
                <polygon points="650,150 720,200 650,250 580,200" fill="url(#heroGradient2)" class="float-slow"/>
                <path d="M100,300 Q200,250 300,300 T500,300" stroke="url(#heroGradient3)" stroke-width="3" fill="none" opacity="0.6" class="float-fast"/>

                <!-- Tech-inspired elements -->
                <g class="float-slow">
                    <rect x="500" y="100" width="8" height="8" fill="url(#heroGradient1)"/>
                    <rect x="520" y="110" width="8" height="8" fill="url(#heroGradient1)"/>
                    <rect x="540" y="120" width="8" height="8" fill="url(#heroGradient1)"/>
                    <rect x="560" y="130" width="8" height="8" fill="url(#heroGradient1)"/>
                </g>

                <!-- Hexagonal pattern -->
                <g class="float-medium" opacity="0.4">
                    <polygon points="150,100 175,87 200,100 200,125 175,138 150,125" fill="url(#heroGradient2)"/>
                    <polygon points="200,125 225,112 250,125 250,150 225,163 200,150" fill="url(#heroGradient2)"/>
                    <polygon points="100,150 125,137 150,150 150,175 125,188 100,175" fill="url(#heroGradient2)"/>
                </g>

                <!-- Connecting lines -->
                <g opacity="0.3" class="float-fast">
                    <line x1="400" y1="200" x2="600" y2="400" stroke="url(#heroGradient1)" stroke-width="2"/>
                    <line x1="600" y1="400" x2="200" y2="500" stroke="url(#heroGradient2)" stroke-width="2"/>
                    <line x1="200" y1="500" x2="360" y2="510" stroke="url(#heroGradient3)" stroke-width="2"/>
                </g>
            </svg>
        </div>

        <!-- Additional decorative elements -->
        <div style="position: absolute; top: 20%; left: -150px; opacity: 0.06; pointer-events: none;" class="float-slow">
            <svg width="300" height="300" viewBox="0 0 300 300" fill="none">
                <defs>
                    <linearGradient id="leftGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#7DD3AE"/>
                        <stop offset="100%" stop-color="#5BC192"/>
                    </linearGradient>
                </defs>
                <circle cx="150" cy="150" r="100" fill="url(#leftGradient)" opacity="0.8"/>
                <rect x="100" y="100" width="100" height="100" rx="20" fill="url(#leftGradient)" opacity="0.6" transform="rotate(45 150 150)"/>
            </svg>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Nos Services</h2>
                <p class="section-subtitle">Nous façonnons l'excellence en développement web, applications mobiles et design digital. Chaque projet est une œuvre d'art technique.</p>
            </div>
        </div>

        <div class="services-container">
            <div class="services-scroll" id="servicesScroll">
                <div class="service-card animate-scale-in">
                    <div class="service-icon">
                        <svg width="28" height="28" fill="white" viewBox="0 0 24 24">
                            <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM4 6h16v10H4V6zm2 2v2h2V8H6zm0 4v2h2v-2H6zm4-4v2h8V8h-8zm0 4v2h8v-2h-8z"/>
                        </svg>
                    </div>
                    <h3>Développement Web</h3>
                    <p>Applications web modernes et performantes. React, Vue.js, Node.js - nous maîtrisons les technologies de pointe pour créer des expériences utilisateur exceptionnelles et des architectures scalables.</p>
                </div>

                <div class="service-card animate-scale-in">
                    <div class="service-icon">
                        <svg width="28" height="28" fill="white" viewBox="0 0 24 24">
                            <path d="M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z"/>
                        </svg>
                    </div>
                    <h3>Applications Mobile</h3>
                    <p>Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie à vos idées sur tous les écrans.</p>
                </div>

                <div class="service-card animate-scale-in">
                    <div class="service-icon">
                        <svg width="28" height="28" fill="white" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <h3>Design & UX/UI</h3>
                    <p>Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous créons des interfaces intuitives qui racontent l'histoire de votre marque.</p>
                </div>

                <div class="service-card animate-scale-in">
                    <div class="service-icon">
                        <svg width="28" height="28" fill="white" viewBox="0 0 24 24">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                    </div>
                    <h3>Branding Digital</h3>
                    <p>Identités visuelles mémorables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque pour tous les supports digitaux.</p>
                </div>

                <div class="service-card animate-scale-in">
                    <div class="service-icon">
                        <svg width="28" height="28" fill="white" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <h3>Maintenance & Support</h3>
                    <p>Accompagnement technique continu pour faire évoluer vos projets. Monitoring, mises à jour, optimisations - nous veillons sur vos applications comme sur nos propres créations.</p>
                </div>

                <div class="service-card animate-scale-in">
                    <div class="service-icon">
                        <svg width="28" height="28" fill="white" viewBox="0 0 24 24">
                            <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19v2h-1.5v1h-13V4H3V2h1.5C5.33 2 6 2.67 6 3.5S5.33 5 4.5 5H18c.55 0 1-.45 1-1s-.45-1-1-1z"/>
                        </svg>
                    </div>
                    <h3>Consulting Technique</h3>
                    <p>Expertise stratégique pour orienter vos décisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions pour votre business.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Process Section -->
    <section id="process" class="process">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Notre Processus</h2>
                <p class="section-subtitle">Une méthodologie éprouvée qui transforme vos idées en solutions digitales performantes, étape par étape.</p>
            </div>

            <div class="process-grid">
                <div class="process-step animate-fade-in-up">
                    <div class="process-number">01</div>
                    <h3>Découverte & Stratégie</h3>
                    <p>Nous analysons vos besoins, votre marché et vos objectifs pour définir la stratégie digitale optimale. Ateliers collaboratifs, recherche utilisateur et définition du MVP.</p>
                </div>

                <div class="process-step animate-fade-in-up">
                    <div class="process-number">02</div>
                    <h3>Design & Prototypage</h3>
                    <p>Création de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et itérations pour valider l'expérience avant le développement.</p>
                </div>

                <div class="process-step animate-fade-in-up">
                    <div class="process-number">03</div>
                    <h3>Développement Agile</h3>
                    <p>Développement en sprints avec livraisons régulières. Code clean, tests automatisés et intégration continue pour une qualité irréprochable.</p>
                </div>

                <div class="process-step animate-fade-in-up">
                    <div class="process-number">04</div>
                    <h3>Lancement & Optimisation</h3>
                    <p>Déploiement sécurisé, formation de vos équipes et monitoring des performances. Optimisations continues basées sur les données d'usage.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section id="contact" class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Prêt à transformer votre vision en réalité ?</h2>
                <p>Discutons de votre projet et découvrons ensemble comment nous pouvons créer quelque chose d'exceptionnel. Consultation gratuite, devis personnalisé sous 48h.</p>
                <a href="mailto:<EMAIL>" class="cta-button">
                    Démarrer votre projet
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M4.167 10h11.666M10 4.167L15.833 10 10 15.833" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </a>
            </div>
        </div>

        <!-- Beautiful CTA Background SVG -->
        <div style="position: absolute; top: 20%; right: -100px; opacity: 0.1; pointer-events: none;">
            <svg width="400" height="400" viewBox="0 0 400 400" fill="none">
                <defs>
                    <linearGradient id="ctaGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="white" stop-opacity="0.2"/>
                        <stop offset="100%" stop-color="white" stop-opacity="0.05"/>
                    </linearGradient>
                </defs>
                <circle cx="200" cy="150" r="80" fill="url(#ctaGradient1)"/>
                <circle cx="300" cy="250" r="60" fill="url(#ctaGradient1)"/>
                <rect x="100" y="200" width="80" height="80" rx="15" fill="url(#ctaGradient1)" transform="rotate(25 140 240)"/>
                <polygon points="320,100 360,140 320,180 280,140" fill="url(#ctaGradient1)"/>
            </svg>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>© 2024 GibbonLab. Créé avec passion à Paris. Tous droits réservés.</p>
        </div>
    </footer>

    <script>
        // Enhanced scroll animations with stagger effect
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0) scale(1)';
                    }, index * 100); // Stagger animation
                }
            });
        }, observerOptions);

        // Observe all animated elements with enhanced setup
        document.querySelectorAll('.animate-fade-in-up, .animate-scale-in').forEach((el, index) => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px) scale(0.95)';
            el.style.transition = 'all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            observer.observe(el);
        });

        // Header scroll effect
        const header = document.querySelector('.header');
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;

            if (currentScrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }

            // Hide header on scroll down, show on scroll up
            if (currentScrollY > lastScrollY && currentScrollY > 200) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }

            lastScrollY = currentScrollY;
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const headerHeight = header.offsetHeight;
                    const targetPosition = target.offsetTop - headerHeight - 20;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Parallax effect for hero background elements
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.hero-visual, .float-slow, .float-medium, .float-fast');

            parallaxElements.forEach((element, index) => {
                const speed = 0.1 + (index * 0.05);
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });

        // Enhanced service card interactions
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-16px) scale(1.02) rotateY(5deg)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1) rotateY(0deg)';
            });
        });

        // Add loading animation
        window.addEventListener('load', () => {
            document.body.style.opacity = '1';
            document.body.style.transform = 'translateY(0)';
        });

        // Initialize body animation
        document.body.style.opacity = '0';
        document.body.style.transform = 'translateY(20px)';
        document.body.style.transition = 'all 0.6s ease-out';
    </script>
</body>
</html>
