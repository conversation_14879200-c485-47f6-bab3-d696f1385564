/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary: #3B82F6; /* Modern blue */
    --secondary: #7DD3AE; /* Pastel green (replacing purple) */
    --accent: #06B6D4; /* Bright cyan */
    --tertiary: #10B981; /* Success green */
    --yellow: #F59E0B; /* Amber */
    --coral: #EF4444; /* Modern red */
    --dark: #0F172A; /* Slate dark */
    --light: #FFFFFF;
    --gray-100: #F8FAFC;
    --gray-200: #E2E8F0;
    --gray-300: #CBD5E1;
    --blue-50: #EFF6FF;
    --blue-100: #DBEAFE;
    --blue-200: #BFDBFE;
    --blue-300: #93C5FD;
    --green-100: #E0F5EB; /* Light pastel green (replacing purple-100) */
    --green-200: #C1EBD9; /* Medium pastel green (replacing purple-200) */
    --cyan-100: #CFFAFE;
    --cyan-200: #A5F3FC;
    --green-300: #7DD3AE; /* Main pastel green */
    --green-400: #5BC192; /* Darker pastel green */
    --amber-100: #FEF3C7;
    --amber-200: #FDE68A;
    --red-100: #FEE2E2;
    --red-200: #FECACA;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--dark);
    background: var(--light);
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 100;
    border-bottom: 1px solid var(--gray-200);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo-text {
    font-size: 24px;
    font-weight: 800;
    color: var(--dark);
    background: linear-gradient(135deg, var(--tertiary) 0%, var(--secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-links {
    display: flex;
    gap: 32px;
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: var(--dark);
    font-weight: 500;
    font-size: 16px;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: var(--primary);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 24px;
    height: 3px;
    background: var(--dark);
    border-radius: 2px;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(139, 92, 246, 0.05) 50%, rgba(6, 182, 212, 0.03) 100%);
    padding-top: 70px;
}

.hero-container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.shape {
    position: absolute;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-title {
    font-size: 64px;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 24px;
    color: var(--dark);
}

.gradient-text {
    background: linear-gradient(135deg, var(--tertiary) 0%, var(--secondary) 50%, var(--green-400) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 20px;
    color: #6B7280;
    margin-bottom: 40px;
    line-height: 1.5;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 32px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--tertiary) 0%, var(--secondary) 100%);
    color: var(--light);
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(16, 185, 129, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--accent) 0%, var(--tertiary) 100%);
    color: var(--light);
    box-shadow: 0 4px 20px rgba(6, 182, 212, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(6, 182, 212, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
}

.btn-outline:hover {
    background: var(--primary);
    color: var(--light);
}

.btn-large {
    padding: 20px 40px;
    font-size: 18px;
}

/* Hero Creature */
.hero-creature {
    position: relative;
    animation: creatureBreathe 4s ease-in-out infinite;
}

@keyframes creatureBreathe {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.creature-body {
    animation: bodyPulse 3s ease-in-out infinite alternate;
}

@keyframes bodyPulse {
    from { fill: url(#creatureGradient); }
    to { fill: url(#creatureGradient); }
}

.creature-eye {
    animation: blink 4s ease-in-out infinite;
}

@keyframes blink {
    0%, 90%, 100% { r: 20; }
    95% { r: 2; }
}

.creature-hand {
    animation: wave 2s ease-in-out infinite alternate;
}

.hand-left {
    transform-origin: center;
    animation-delay: 0s;
}

.hand-right {
    transform-origin: center;
    animation-delay: 1s;
}

@keyframes wave {
    from { transform: translateY(0px); }
    to { transform: translateY(-10px); }
}

/* Section Styles */
section {
    padding: 100px 0;
}

.section-title {
    font-size: 48px;
    font-weight: 800;
    text-align: center;
    margin-bottom: 60px;
    color: var(--dark);
}

/* Services Grid */
.services {
    background: var(--green-50);
    position: relative;
}

.decorative-pattern {
    margin-bottom: 40px;
    overflow: hidden;
}

.decorative-pattern svg {
    width: 100%;
    height: auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.service-card {
    background: var(--light);
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
    border-color: var(--primary);
}

.service-icon {
    margin-bottom: 24px;
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.service-card h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--dark);
}

.service-card p {
    color: #6B7280;
    font-size: 16px;
    line-height: 1.6;
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h2 {
    font-size: 48px;
    font-weight: 800;
    margin-bottom: 24px;
    color: var(--dark);
    line-height: 1.2;
}

.about-text p {
    font-size: 18px;
    color: #6B7280;
    margin-bottom: 16px;
    line-height: 1.6;
}

.spring-path {
    animation: springBounce 3s ease-in-out infinite;
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: drawSpring 2s ease-out forwards, springBounce 3s ease-in-out infinite 2s;
}

@keyframes drawSpring {
    to { stroke-dashoffset: 0; }
}

@keyframes springBounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.spring-creature {
    animation: creatureBob 2s ease-in-out infinite;
}

@keyframes creatureBob {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

/* How it Works */
.how-it-works {
    background: var(--teal-100);
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.step {
    text-align: center;
    padding: 40px 20px;
}

.step-icon {
    margin-bottom: 24px;
    animation: stepPulse 2s ease-in-out infinite;
}

@keyframes stepPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.step h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--dark);
}

.step p {
    color: #6B7280;
    font-size: 16px;
    line-height: 1.6;
}

/* Pricing */
.pricing {
    background: var(--green-50);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    max-width: 900px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--light);
    border-radius: 24px;
    padding: 40px 32px;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid var(--gray-200);
    text-align: center;
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
    border-color: var(--primary);
}

.pricing-card.featured {
    border-color: var(--tertiary);
    background: linear-gradient(135deg, var(--blue-50) 0%, var(--green-100) 100%);
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, var(--tertiary) 0%, var(--secondary) 100%);
    color: white;
    padding: 8px 24px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.pricing-card h3 {
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 16px;
    color: var(--dark);
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 16px;
}

.currency {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary);
}

.amount {
    font-size: 56px;
    font-weight: 900;
    color: var(--dark);
    margin: 0 4px;
}

.period {
    font-size: 18px;
    color: #6B7280;
}

.plan-description {
    color: #6B7280;
    font-size: 16px;
    margin-bottom: 32px;
}

.features {
    list-style: none;
    margin-bottom: 40px;
    text-align: left;
}

.features li {
    padding: 8px 0;
    font-size: 16px;
    color: var(--dark);
    position: relative;
    padding-left: 24px;
}

.features li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--secondary);
    font-weight: bold;
}

/* CTA Section */
.cta {
    background: linear-gradient(135deg, var(--tertiary) 0%, var(--secondary) 100%);
    color: var(--light);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.cta .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.cta-content h2 {
    font-size: 48px;
    font-weight: 800;
    margin-bottom: 24px;
    line-height: 1.2;
}

.cta-content p {
    font-size: 20px;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-creature-body {
    animation: ctaFloat 4s ease-in-out infinite;
}

@keyframes ctaFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

.floating-stars {
    animation: starsRotate 10s linear infinite;
}

@keyframes starsRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.floating-stars path {
    animation: starTwinkle 2s ease-in-out infinite alternate;
}

@keyframes starTwinkle {
    from { opacity: 0.7; }
    to { opacity: 1; }
}

/* Footer */
.footer {
    background: var(--dark);
    color: white;
    padding: 60px 0 40px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.footer-links {
    display: flex;
    gap: 32px;
}

.footer-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: var(--primary);
}

.footer-bottom {
    text-align: center;
    padding-top: 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 40px;
    }
    
    .hero-title {
        font-size: 48px;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .cta .container {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-title {
        font-size: 36px;
    }
    
    .section-title {
        font-size: 36px;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-card.featured {
        transform: none;
    }
    
    .steps {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 24px;
        text-align: center;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 16px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }
    
    .hero-title {
        font-size: 28px;
    }
    
    .hero-subtitle {
        font-size: 16px;
    }
    
    .btn {
        padding: 12px 24px;
        font-size: 14px;
        min-width: 120px;
    }
    
    .service-card {
        padding: 30px 20px;
    }
    
    .pricing-card {
        padding: 40px 20px;
    }
    
    .amount {
        font-size: 40px;
    }
} 