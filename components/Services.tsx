'use client'

import { useEffect, useRef } from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'
import { Monitor, Smartphone, Palette, Globe, Settings, Lightbulb } from 'lucide-react'

const services = [
  {
    icon: Monitor,
    title: "Développement Web",
    description: "Applications web modernes et performantes. React, Vue.js, Node.js - nous maîtrisons les technologies de pointe pour créer des expériences utilisateur exceptionnelles et des architectures scalables."
  },
  {
    icon: Smartphone,
    title: "Applications Mobile",
    description: "Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie à vos idées sur tous les écrans."
  },
  {
    icon: Palette,
    title: "Design & UX/UI",
    description: "Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous créons des interfaces intuitives qui racontent l'histoire de votre marque."
  },
  {
    icon: Globe,
    title: "Branding Digital",
    description: "Identités visuelles mémorables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque pour tous les supports digitaux."
  },
  {
    icon: Settings,
    title: "Maintenance & Support",
    description: "Accompagnement technique continu pour faire évoluer vos projets. Monitoring, mises à jour, optimisations - nous veillons sur vos applications comme sur nos propres créations."
  },
  {
    icon: Lightbulb,
    title: "Consulting Technique",
    description: "Expertise stratégique pour orienter vos décisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions pour votre business."
  }
]

const Services = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })

  // Transform scroll progress to horizontal movement
  const x = useTransform(scrollYProgress, [0, 1], ["0%", "-50%"])

  return (
    <section ref={containerRef} id="services" className="relative overflow-hidden">
      {/* Fixed height container for scroll-triggered animation */}
      <div className="h-[300vh] relative">
        {/* Sticky container that holds the horizontally moving content */}
        <div className="sticky top-0 h-screen flex items-center overflow-hidden bg-bg-primary">
          <div className="container mx-auto px-6 mb-16 absolute top-24 left-1/2 transform -translate-x-1/2 z-10">
            <motion.div
              className="text-center max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="font-display text-4xl md:text-6xl font-bold mb-6 text-text-primary">
                Nos Services
              </h2>
              <p className="text-xl text-text-secondary leading-relaxed">
                Nous façonnons l'excellence en développement web, applications mobiles et design digital. Chaque projet est une œuvre d'art technique.
              </p>
            </motion.div>
          </div>

          {/* Horizontally moving content */}
          <motion.div
            style={{ x }}
            className="flex gap-8 px-6 md:px-12 items-center h-full"
          >
            {services.map((service, index) => {
              const Icon = service.icon
              return (
                <motion.div
                  key={index}
                  className="flex-shrink-0 w-96 bg-white p-8 rounded-3xl border border-border-light transition-all duration-500 hover:scale-105 hover:shadow-xl hover:shadow-primary-green/10 group relative overflow-hidden"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  {/* Gradient border on hover */}
                  <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-green to-accent-purple transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />

                  {/* Background gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-green/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-2xl flex items-center justify-center mb-6 transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 shadow-lg">
                      <Icon className="w-8 h-8 text-white" />
                    </div>

                    <h3 className="font-display text-2xl font-semibold mb-4 text-text-primary group-hover:text-primary-green transition-colors duration-300">
                      {service.title}
                    </h3>

                    <p className="text-text-secondary leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                </motion.div>
              )
            })}
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Services
