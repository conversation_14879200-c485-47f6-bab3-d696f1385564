'use client'

import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'
import Link from 'next/link'

const Hero = () => {
  return (
    <section className="scroll-section relative flex items-center justify-center overflow-hidden bg-gradient-to-br from-bg-primary to-bg-secondary">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.03]">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%237DD3AE' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      {/* Floating SVG Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/4 right-0 w-96 h-96 opacity-10"
          animate={{
            y: [-20, 20, -20],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <svg viewBox="0 0 400 400" fill="none" className="w-full h-full">
            <defs>
              <linearGradient id="heroGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#7DD3AE" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#5BC192" stopOpacity="0.4" />
              </linearGradient>
            </defs>
            <circle cx="200" cy="150" r="80" fill="url(#heroGradient1)" />
            <rect x="150" y="250" width="100" height="100" rx="20" fill="url(#heroGradient1)" transform="rotate(25 200 300)" />
          </svg>
        </motion.div>

        <motion.div
          className="absolute top-1/3 left-0 w-64 h-64 opacity-8"
          animate={{
            y: [20, -20, 20],
            rotate: [0, -3, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <svg viewBox="0 0 200 200" fill="none" className="w-full h-full">
            <defs>
              <linearGradient id="heroGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.6" />
                <stop offset="100%" stopColor="#F97316" stopOpacity="0.3" />
              </linearGradient>
            </defs>
            <circle cx="100" cy="100" r="60" fill="url(#heroGradient2)" />
          </svg>
        </motion.div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          className="text-center max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.h1
            className="font-display text-5xl md:text-7xl lg:text-8xl font-extrabold leading-tight mb-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Nous créons des{' '}
            <span className="highlight-effect gradient-text">
              applications exceptionnelles
            </span>{' '}
            qui transforment vos idées en succès
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto mb-12 leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            De la conception au développement, nous accompagnons les entreprises ambitieuses dans la création d'expériences digitales mémorables. Design élégant, code robuste, résultats mesurables.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <Link
              href="#contact"
              className="shimmer-effect inline-flex items-center gap-3 bg-gradient-to-r from-primary-green to-primary-green-dark text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary-green/25 group"
            >
              Démarrer votre projet
              <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Hero
