'use client'

import { motion } from 'framer-motion'
import { ArrowRight, Linkedin, Github, Twitter } from 'lucide-react'
import Link from 'next/link'

const CTAFooter = () => {
  return (
    <section id="contact" className="scroll-section relative bg-gradient-to-br from-primary-green to-primary-green-dark overflow-hidden flex flex-col">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      {/* Floating Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/4 right-0 w-96 h-96 opacity-10"
          animate={{
            y: [-30, 30, -30],
            rotate: [0, 10, 0],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <svg viewBox="0 0 400 400" fill="none" className="w-full h-full">
            <circle cx="200" cy="150" r="80" fill="white" />
            <rect x="150" y="250" width="100" height="100" rx="20" fill="white" transform="rotate(25 200 300)" />
          </svg>
        </motion.div>

        <motion.div
          className="absolute bottom-1/4 left-0 w-64 h-64 opacity-8"
          animate={{
            y: [20, -20, 20],
            rotate: [0, -5, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <svg viewBox="0 0 200 200" fill="none" className="w-full h-full">
            <polygon points="100,20 180,180 20,180" fill="white" />
          </svg>
        </motion.div>
      </div>

      {/* CTA Section */}
      <div className="relative z-10 flex-1 flex items-center justify-center py-12">
        <div className="container mx-auto px-6">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="font-display text-4xl md:text-6xl font-bold mb-6 text-white">
              Prêt à transformer votre vision en réalité ?
            </h2>
            <p className="text-xl text-white/90 max-w-3xl mx-auto mb-12 leading-relaxed">
              Discutons de votre projet et découvrons ensemble comment nous pouvons créer quelque chose d'exceptionnel. Consultation gratuite, devis personnalisé sous 48h.
            </p>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="mailto:<EMAIL>"
                className="shimmer-effect inline-flex items-center gap-3 bg-white text-primary-green px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 hover:shadow-xl hover:shadow-black/20 group"
              >
                Démarrer votre projet
                <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Footer Section */}
      <div className="relative z-10 border-t border-white/10 pt-16 pb-8">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-12">
            {/* Brand Section */}
            <motion.div
              className="max-w-md"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                  <span className="text-primary-green font-bold text-sm">G</span>
                </div>
                <span className="font-display text-2xl font-bold text-white">
                  GibbonLab
                </span>
              </div>
              <p className="text-white/80 leading-relaxed">
                Créateurs d'expériences digitales exceptionnelles. Nous transformons vos idées en solutions techniques performantes et esthétiques.
              </p>
            </motion.div>

            {/* Links Section */}
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 gap-8"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div>
                <h4 className="font-display text-lg font-semibold text-white mb-4">
                  Services
                </h4>
                <ul className="space-y-3">
                  {['Développement Web', 'Applications Mobile', 'Design & UX/UI', 'Branding Digital'].map((service) => (
                    <li key={service}>
                      <Link href="#services" className="text-white/70 hover:text-white transition-colors">
                        {service}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-display text-lg font-semibold text-white mb-4">
                  Contact
                </h4>
                <ul className="space-y-3">
                  <li>
                    <Link href="mailto:<EMAIL>" className="text-white/70 hover:text-white transition-colors">
                      <EMAIL>
                    </Link>
                  </li>
                  <li>
                    <Link href="tel:+33123456789" className="text-white/70 hover:text-white transition-colors">
                      +33 1 23 45 67 89
                    </Link>
                  </li>
                  <li className="text-white/70">
                    Paris, France
                  </li>
                </ul>
              </div>
            </motion.div>
          </div>

          {/* Bottom Section */}
          <motion.div
            className="flex flex-col sm:flex-row justify-between items-center pt-8 border-t border-white/10"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <p className="text-white/60 text-sm mb-4 sm:mb-0">
              © 2024 GibbonLab. Créé avec passion à Paris. Tous droits réservés.
            </p>

            <div className="flex items-center gap-4">
              {[
                { icon: Linkedin, href: '#' },
                { icon: Github, href: '#' },
                { icon: Twitter, href: '#' }
              ].map(({ icon: Icon, href }, index) => (
                <motion.a
                  key={index}
                  href={href}
                  className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-white/70 hover:text-white hover:bg-white/20 transition-all duration-300"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default CTAFooter
