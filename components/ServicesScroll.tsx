'use client'

import { useScrollAnimation } from '@/hooks/useScrollAnimation'
import { Monitor, Smartphone, Palette, Globe, Settings, Lightbulb } from 'lucide-react'

const services = [
  {
    icon: Monitor,
    title: "Développement Web",
    description: "Applications web modernes et performantes. React, Vue.js, Node.js - nous maîtrisons les technologies de pointe pour créer des expériences utilisateur exceptionnelles et des architectures scalables."
  },
  {
    icon: Smartphone,
    title: "Applications Mobile",
    description: "Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie à vos idées sur tous les écrans."
  },
  {
    icon: Palette,
    title: "Design & UX/UI",
    description: "Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous créons des interfaces intuitives qui racontent l'histoire de votre marque."
  },
  {
    icon: Globe,
    title: "Branding Digital",
    description: "Identités visuelles mémorables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque pour tous les supports digitaux."
  },
  {
    icon: Settings,
    title: "Maintenance & Support",
    description: "Accompagnement technique continu pour faire évoluer vos projets. Monitoring, mises à jour, optimisations - nous veillons sur vos applications comme sur nos propres créations."
  },
  {
    icon: Lightbulb,
    title: "Consulting Technique",
    description: "Expertise stratégique pour orienter vos décisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions pour votre business."
  }
]

const ServicesScroll = () => {
  const titleAnimation = useScrollAnimation(0.3)

  return (
    <>
      {/* Services Introduction */}
      <section className="scroll-section relative flex items-center justify-center bg-bg-primary">
        <div className="container mx-auto px-6 text-center">
          <div
            ref={titleAnimation.ref}
            className={`scroll-fade-in ${titleAnimation.isVisible ? 'visible' : ''}`}
          >
            <h2 className="font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary">
              Nos{' '}
              <span className="gradient-text">Services</span>
            </h2>
            <p className="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
              Nous façonnons l'excellence en développement web, applications mobiles et design digital. Chaque projet est une œuvre d'art technique.
            </p>
          </div>
        </div>
      </section>

      {/* Service Sections */}
      {services.map((service, index) => {
        const Icon = service.icon
        const contentAnimation = useScrollAnimation(0.2)
        const visualAnimation = useScrollAnimation(0.3)

        return (
          <section
            key={index}
            className="scroll-section relative flex items-center justify-center bg-bg-secondary"
          >
            <div className="container mx-auto px-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div
                  ref={contentAnimation.ref}
                  className={`text-center lg:text-left scroll-slide-left ${contentAnimation.isVisible ? 'visible' : ''}`}
                >
                  <div className="w-20 h-20 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-2xl flex items-center justify-center mb-8 mx-auto lg:mx-0 shadow-lg transition-transform duration-300 hover:scale-110 hover:rotate-3">
                    <Icon className="w-10 h-10 text-white" />
                  </div>

                  <h3 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary">
                    {service.title}
                  </h3>

                  <p className="text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0">
                    {service.description}
                  </p>
                </div>

                <div
                  ref={visualAnimation.ref}
                  className={`flex justify-center lg:justify-end scroll-slide-right ${visualAnimation.isVisible ? 'visible' : ''}`}
                >
                  <div className="relative group">
                    <div className="w-80 h-80 bg-gradient-to-br from-primary-green/10 to-primary-green-dark/10 rounded-3xl flex items-center justify-center border border-primary-green/20 transition-all duration-500 group-hover:scale-105 group-hover:shadow-xl group-hover:shadow-primary-green/10">
                      <Icon className="w-32 h-32 text-primary-green opacity-30 transition-all duration-500 group-hover:opacity-50 group-hover:scale-110" />
                    </div>

                    {/* Floating decorative elements */}
                    <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary-green rounded-full opacity-20 animate-float-slow"></div>
                    <div className="absolute -bottom-6 -left-6 w-6 h-6 bg-accent-purple rounded-full opacity-20 animate-float-medium"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )
      })}
    </>
  )
}

export default ServicesScroll
