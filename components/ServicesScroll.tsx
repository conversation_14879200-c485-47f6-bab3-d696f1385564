'use client'

import { Monitor, Smartphone, Palette, Globe, Settings, Lightbulb } from 'lucide-react'

const services = [
  {
    icon: Monitor,
    title: "Développement Web",
    description: "Applications web modernes et performantes. React, Vue.js, Node.js - nous maîtrisons les technologies de pointe pour créer des expériences utilisateur exceptionnelles."
  },
  {
    icon: Smartphone,
    title: "Applications Mobile",
    description: "Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie à vos idées sur tous les écrans."
  },
  {
    icon: Palette,
    title: "Design & UX/UI",
    description: "Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous créons des interfaces intuitives."
  },
  {
    icon: Globe,
    title: "Branding Digital",
    description: "Identités visuelles mémorables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque."
  },
  {
    icon: Settings,
    title: "Maintenance & Support",
    description: "Accompagnement technique continu pour faire évoluer vos projets. Monitoring, mises à jour, optimisations - nous veillons sur vos applications."
  },
  {
    icon: Lightbulb,
    title: "Consulting Technique",
    description: "Expertise stratégique pour orienter vos décisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions."
  }
]

// SVG Circle Component (like morningside.ai)
const ServiceCircle = ({ icon: Icon, isActive }: { icon: any, isActive: boolean }) => (
  <div className="w-full flex justify-center">
    <svg viewBox="0 0 294 294" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-[35vw] h-[35vw] lg:w-[25vw] lg:h-[25vw] max-w-[400px] max-h-[400px]">
      <g opacity={isActive ? "1" : "0.3"} className="transition-opacity duration-700">
        <g filter="url(#a)">
          <path fill="url(#b)" d="M294 147C294 65.814 228.186 0 147 0S0 65.814 0 147s65.814 147 147 147 147-65.814 147-147Z"></path>
        </g>
        <path stroke="url(#c)" strokeWidth="2.5" d="M292.75 147C292.75 66.504 227.495 1.25 147 1.25 66.504 1.25 1.25 66.504 1.25 147c0 80.495 65.254 145.75 145.75 145.75 80.495 0 145.75-65.255 145.75-145.75Z"></path>
      </g>
      <g filter="url(#d)" opacity={isActive ? "1" : "0.5"} className="transition-opacity duration-700">
        <path fill="url(#e)" d="M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z"></path>
      </g>
      <path stroke="url(#f)" strokeWidth="2.5" d="M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z" opacity={isActive ? "1" : "0.5"} className="transition-opacity duration-700"></path>

      {/* Icon in center */}
      <foreignObject x="122" y="122" width="50" height="50">
        <div className="w-full h-full flex items-center justify-center">
          <Icon className={`w-8 h-8 transition-all duration-700 ${isActive ? 'text-primary-green opacity-100' : 'text-primary-green opacity-30'}`} />
        </div>
      </foreignObject>

      <defs>
        <linearGradient id="b" x1="147" x2="147" y1="0" y2="294" gradientUnits="userSpaceOnUse">
          <stop stopColor="#fff"></stop>
          <stop offset="0.6" stopColor="#4CAA7D" stopOpacity="0.1"></stop>
          <stop offset="1" stopColor="#E1FFF1" stopOpacity="0.5"></stop>
        </linearGradient>
        <linearGradient id="c" x1="147" x2="147" y1="0" y2="294" gradientUnits="userSpaceOnUse">
          <stop stopColor="#FDFFFE"></stop>
          <stop offset="0.4" stopColor="#549876"></stop>
          <stop offset="1" stopColor="#fff"></stop>
        </linearGradient>
        <linearGradient id="e" x1="146" x2="146" y1="66" y2="228" gradientUnits="userSpaceOnUse">
          <stop stopColor="#fff"></stop>
          <stop offset="0.6" stopColor="#4CAA7D" stopOpacity="0.1"></stop>
          <stop offset="1" stopColor="#E1FFF1" stopOpacity="0.5"></stop>
        </linearGradient>
        <linearGradient id="f" x1="146" x2="146" y1="66" y2="228" gradientUnits="userSpaceOnUse">
          <stop stopColor="#FDFFFE"></stop>
          <stop offset="0.4" stopColor="#549876"></stop>
          <stop offset="1" stopColor="#fff"></stop>
        </linearGradient>
        <filter id="a" width="294" height="318.088" x="0" y="-24.088" colorInterpolationFilters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
          <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix>
          <feOffset dy="-24.088"></feOffset>
          <feGaussianBlur stdDeviation="18.066"></feGaussianBlur>
          <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite>
          <feColorMatrix values="0 0 0 0 0.536175 0 0 0 0 0.741662 0 0 0 0 0.638918 0 0 0 0.7 0"></feColorMatrix>
          <feBlend in2="shape" result="effect1_innerShadow_0_1"></feBlend>
        </filter>
        <filter id="d" width="162" height="186.088" x="65" y="41.912" colorInterpolationFilters="sRGB" filterUnits="userSpaceOnUse">
          <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
          <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
          <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix>
          <feOffset dy="-24.088"></feOffset>
          <feGaussianBlur stdDeviation="18.066"></feGaussianBlur>
          <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite>
          <feColorMatrix values="0 0 0 0 0.536175 0 0 0 0 0.741662 0 0 0 0 0.638918 0 0 0 0.7 0"></feColorMatrix>
          <feBlend in2="shape" result="effect1_innerShadow_0_1"></feBlend>
        </filter>
      </defs>
    </svg>
  </div>
)

const ServicesScroll = () => {
  return (
    <>
      {/* Services Introduction */}
      <section className="scroll-section relative flex items-center justify-center bg-bg-primary">
        <div className="container mx-auto px-6 text-center">
          <h2 className="font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary">
            Nous façonnons l'excellence en{' '}
            <span className="gradient-text">développement web</span>,{' '}
            <span className="gradient-text">applications mobiles</span> et{' '}
            <span className="gradient-text">design digital</span>.
          </h2>
          <p className="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
            Découvrez nos 3 domaines d'expertise à travers notre parcours de transformation digitale.
          </p>
        </div>
      </section>

      {/* Individual Service Sections */}
      {services.map((service, index) => {
        const Icon = service.icon
        return (
          <section
            key={index}
            className="scroll-section relative flex items-center justify-center text-white"
            style={{ backgroundColor: '#1C1B1C' }}
          >
            {/* SVG Circle */}
            <ServiceCircle icon={Icon} isActive={true} />

            {/* Content overlay */}
            <div className="fixed w-11/12 lg:w-full bottom-5 md:bottom-10 lg:bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-4 text-center pointer-events-none z-50">
              <h3 className="text-4xl lg:text-6xl font-light text-white">
                {service.title}
              </h3>
              <p className="text-base lg:text-lg lg:w-6/12 text-gray-400 px-1 lg:px-2">
                {service.description}
              </p>
            </div>

            {/* Side navigation */}
            <div className="absolute left-2 lg:left-8 top-1/2 -translate-y-1/2 flex flex-col gap-2 pointer-events-auto z-50">
              {services.map((_, i) => (
                <button
                  key={i}
                  className={`text-left transition-colors duration-300 text-sm ${
                    i === index ? 'text-white' : 'text-gray-500'
                  }`}
                  style={{ fontFamily: 'monospace' }}
                >
                  {service.title.toUpperCase().replace(/[^A-Z]/g, '').slice(0, 8)}
                </button>
              ))}
            </div>
          </section>
        )
      })}
    </>
  )
}

export default ServicesScroll
