'use client'

import { useEffect, useRef, useState } from 'react'
import { Monitor, Smartphone, Palette, Globe, Settings, Lightbulb } from 'lucide-react'

const services = [
  {
    icon: Monitor,
    title: "Développement Web",
    description: "Applications web modernes et performantes. React, Vue.js, Node.js - nous maîtrisons les technologies de pointe pour créer des expériences utilisateur exceptionnelles et des architectures scalables."
  },
  {
    icon: Smartphone,
    title: "Applications Mobile",
    description: "Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie à vos idées sur tous les écrans."
  },
  {
    icon: Palette,
    title: "Design & UX/UI",
    description: "Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous créons des interfaces intuitives qui racontent l'histoire de votre marque."
  },
  {
    icon: Globe,
    title: "Branding Digital",
    description: "Identités visuelles mémorables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque pour tous les supports digitaux."
  },
  {
    icon: Settings,
    title: "Maintenance & Support",
    description: "Accompagnement technique continu pour faire évoluer vos projets. Monitoring, mises à jour, optimisations - nous veillons sur vos applications comme sur nos propres créations."
  },
  {
    icon: Lightbulb,
    title: "Consulting Technique",
    description: "Expertise stratégique pour orienter vos décisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions pour votre business."
  }
]

const ServicesScroll = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [scrollProgress, setScrollProgress] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return

      const container = containerRef.current
      const containerTop = container.offsetTop
      const containerHeight = container.offsetHeight
      const windowHeight = window.innerHeight
      const scrollTop = window.scrollY

      // Calculate scroll progress through this section
      const startScroll = containerTop - windowHeight
      const endScroll = containerTop + containerHeight - windowHeight
      const progress = Math.max(0, Math.min(1, (scrollTop - startScroll) / (endScroll - startScroll)))

      setScrollProgress(progress)
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // Initial calculation

    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Calculate horizontal transform based on scroll progress
  const totalWidth = services.length * 100 // Each service takes 100vw
  const translateX = -(scrollProgress * (totalWidth - 100)) // Move from 0 to -(totalWidth - 100)

  return (
    <div ref={containerRef} className="relative" style={{ height: `${services.length * 100}vh` }}>
      {/* Fixed container for horizontal scrolling */}
      <div className="sticky top-0 h-screen overflow-hidden bg-bg-primary">
        {/* Services Introduction */}
        <div className="absolute inset-0 flex items-center justify-center z-10 bg-bg-primary/90 backdrop-blur-sm transition-opacity duration-500" style={{ opacity: scrollProgress < 0.1 ? 1 : 0 }}>
          <div className="container mx-auto px-6 text-center">
            <h2 className="font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary">
              Nos{' '}
              <span className="gradient-text">Services</span>
            </h2>
            <p className="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
              Découvrez nos expertises en faisant défiler. Chaque service est une spécialité que nous maîtrisons parfaitement.
            </p>
          </div>
        </div>

        {/* Horizontal scrolling services */}
        <div
          className="flex h-full transition-transform duration-100 ease-out"
          style={{
            transform: `translateX(${translateX}vw)`,
            width: `${totalWidth}vw`
          }}
        >
          {services.map((service, index) => {
            const Icon = service.icon
            const isActive = scrollProgress >= (index / services.length) && scrollProgress < ((index + 1) / services.length)

            return (
              <div
                key={index}
                className="w-screen h-full flex items-center justify-center bg-bg-secondary relative"
                style={{
                  backgroundColor: index % 2 === 0 ? 'var(--bg-secondary)' : 'var(--bg-primary)'
                }}
              >
                <div className="container mx-auto px-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                    <div className={`text-center lg:text-left transition-all duration-700 ${isActive ? 'opacity-100 translate-x-0' : 'opacity-50 translate-x-8'}`}>
                      <div className="w-20 h-20 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-2xl flex items-center justify-center mb-8 mx-auto lg:mx-0 shadow-lg transition-transform duration-300 hover:scale-110 hover:rotate-3">
                        <Icon className="w-10 h-10 text-white" />
                      </div>

                      <h3 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary">
                        {service.title}
                      </h3>

                      <p className="text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0">
                        {service.description}
                      </p>
                    </div>

                    <div className={`flex justify-center lg:justify-end transition-all duration-700 delay-200 ${isActive ? 'opacity-100 translate-x-0' : 'opacity-30 translate-x-12'}`}>
                      <div className="relative group">
                        <div className="w-80 h-80 bg-gradient-to-br from-primary-green/10 to-primary-green-dark/10 rounded-3xl flex items-center justify-center border border-primary-green/20 transition-all duration-500 group-hover:scale-105 group-hover:shadow-xl group-hover:shadow-primary-green/10">
                          <Icon className="w-32 h-32 text-primary-green opacity-30 transition-all duration-500 group-hover:opacity-50 group-hover:scale-110" />
                        </div>

                        {/* Floating decorative elements */}
                        <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary-green rounded-full opacity-20 animate-float-slow"></div>
                        <div className="absolute -bottom-6 -left-6 w-6 h-6 bg-accent-purple rounded-full opacity-20 animate-float-medium"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Progress indicator */}
                <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2">
                  {services.map((_, i) => (
                    <div
                      key={i}
                      className={`h-2 rounded-full transition-all duration-300 ${
                        i === index ? 'bg-primary-green w-8' : 'bg-text-muted w-2'
                      }`}
                    />
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default ServicesScroll
