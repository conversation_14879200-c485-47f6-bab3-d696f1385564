'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { Monitor, Smartphone, Palette, Globe, Settings, Lightbulb } from 'lucide-react'

const services = [
  {
    icon: Monitor,
    title: "Développement Web",
    description: "Applications web modernes et performantes. React, Vue.js, Node.js - nous maîtrisons les technologies de pointe pour créer des expériences utilisateur exceptionnelles et des architectures scalables."
  },
  {
    icon: Smartphone,
    title: "Applications Mobile",
    description: "Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie à vos idées sur tous les écrans."
  },
  {
    icon: Palette,
    title: "Design & UX/UI",
    description: "Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous créons des interfaces intuitives qui racontent l'histoire de votre marque."
  },
  {
    icon: Globe,
    title: "Branding Digital",
    description: "Identités visuelles mémorables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque pour tous les supports digitaux."
  },
  {
    icon: Settings,
    title: "Maintenance & Support",
    description: "Accompagnement technique continu pour faire évoluer vos projets. Monitoring, mises à jour, optimisations - nous veillons sur vos applications comme sur nos propres créations."
  },
  {
    icon: Lightbulb,
    title: "Consulting Technique",
    description: "Expertise stratégique pour orienter vos décisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions pour votre business."
  }
]

const ServicesScroll = () => {
  const [currentService, setCurrentService] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY
      const windowHeight = window.innerHeight
      const serviceIndex = Math.floor(scrollPosition / windowHeight) - 1
      
      if (serviceIndex >= 0 && serviceIndex < services.length) {
        setCurrentService(serviceIndex)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <>
      {/* Introduction Section */}
      <section className="snap-always snap-center min-h-screen relative flex items-center justify-center bg-bg-primary">
        <div className="container mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary">
              Nous façonnons l'excellence en{' '}
              <span className="gradient-text">développement web</span>,{' '}
              <span className="gradient-text">applications mobiles</span> et{' '}
              <span className="gradient-text">design digital</span>.
            </h2>
            <p className="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
              Découvrez nos services en faisant défiler vers le bas. Chaque projet est une œuvre d'art technique.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Service Discovery Sections */}
      {services.map((service, index) => {
        const Icon = service.icon
        return (
          <section
            key={index}
            className="snap-always snap-center min-h-screen relative flex items-center justify-center bg-bg-secondary"
          >
            <div className="container mx-auto px-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <motion.div
                  className="text-center lg:text-left"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <div className="w-20 h-20 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-2xl flex items-center justify-center mb-8 mx-auto lg:mx-0 shadow-lg">
                    <Icon className="w-10 h-10 text-white" />
                  </div>
                  
                  <h3 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary">
                    {service.title}
                  </h3>
                  
                  <p className="text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0">
                    {service.description}
                  </p>
                </motion.div>

                <motion.div
                  className="flex justify-center lg:justify-end"
                  initial={{ opacity: 0, x: 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  <div className="w-80 h-80 bg-gradient-to-br from-primary-green/10 to-primary-green-dark/10 rounded-3xl flex items-center justify-center border border-primary-green/20">
                    <Icon className="w-32 h-32 text-primary-green opacity-30" />
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Progress indicator */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2">
              {services.map((_, i) => (
                <div
                  key={i}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    i === index ? 'bg-primary-green w-8' : 'bg-text-muted'
                  }`}
                />
              ))}
            </div>
          </section>
        )
      })}
    </>
  )
}

export default ServicesScroll
