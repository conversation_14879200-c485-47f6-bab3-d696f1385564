'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { Search, Palette, Code, Rocket } from 'lucide-react'

const processSteps = [
  {
    number: "01",
    icon: Search,
    title: "Découverte & Stratégie",
    description: "Nous analysons vos besoins, votre marché et vos objectifs pour définir la stratégie digitale optimale. Ateliers collaboratifs, recherche utilisateur et définition du MVP."
  },
  {
    number: "02",
    icon: Palette,
    title: "Design & Prototypage",
    description: "Création de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et itérations pour valider l'expérience avant le développement."
  },
  {
    number: "03",
    icon: Code,
    title: "Développement Agile",
    description: "Développement en sprints avec livraisons régulières. Code clean, tests automatisés et intégration continue pour une qualité irréprochable."
  },
  {
    number: "04",
    icon: Rocket,
    title: "Lancement & Optimisation",
    description: "Déploiement sécurisé, formation de vos équipes et monitoring des performances. Optimisations continues basées sur les données d'usage."
  }
]

const ProcessScroll = () => {
  const [currentStep, setCurrentStep] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY
      const windowHeight = window.innerHeight
      const stepIndex = Math.floor(scrollPosition / windowHeight) - 7 // Adjust based on previous sections
      
      if (stepIndex >= 0 && stepIndex < processSteps.length) {
        setCurrentStep(stepIndex)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <>
      {/* Process Introduction Section */}
      <section className="snap-always snap-center min-h-screen relative flex items-center justify-center bg-bg-primary">
        <div className="container mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary">
              Notre{' '}
              <span className="gradient-text">Processus</span>
            </h2>
            <p className="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
              Une méthodologie éprouvée qui transforme vos idées en solutions digitales performantes, étape par étape.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Process Discovery Sections */}
      {processSteps.map((step, index) => {
        const Icon = step.icon
        return (
          <section
            key={index}
            className="snap-always snap-center min-h-screen relative flex items-center justify-center bg-bg-secondary"
          >
            <div className="container mx-auto px-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <motion.div
                  className="flex justify-center lg:justify-start order-2 lg:order-1"
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <div className="relative">
                    <div className="w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl">
                      <span className="font-display text-4xl font-bold text-white">
                        {step.number}
                      </span>
                    </div>
                    
                    <div className="absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light">
                      <Icon className="w-8 h-8 text-primary-green" />
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="text-center lg:text-left order-1 lg:order-2"
                  initial={{ opacity: 0, x: 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  <h3 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary">
                    {step.title}
                  </h3>
                  
                  <p className="text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0">
                    {step.description}
                  </p>
                </motion.div>
              </div>
            </div>

            {/* Progress indicator */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2">
              {processSteps.map((_, i) => (
                <div
                  key={i}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    i === index ? 'bg-primary-green w-8' : 'bg-text-muted'
                  }`}
                />
              ))}
            </div>
          </section>
        )
      })}
    </>
  )
}

export default ProcessScroll
