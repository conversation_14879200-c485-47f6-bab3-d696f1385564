'use client'

import { useEffect, useRef, useState } from 'react'
import { Search, Palette, Code, Rocket } from 'lucide-react'

const processSteps = [
  {
    number: "01",
    icon: Search,
    title: "Découverte & Stratégie",
    description: "Nous analysons vos besoins, votre marché et vos objectifs pour définir la stratégie digitale optimale. Ateliers collaboratifs, recherche utilisateur et définition du MVP."
  },
  {
    number: "02",
    icon: Palette,
    title: "Design & Prototypage",
    description: "Création de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et itérations pour valider l'expérience avant le développement."
  },
  {
    number: "03",
    icon: Code,
    title: "Développement Agile",
    description: "Développement en sprints avec livraisons régulières. Code clean, tests automatisés et intégration continue pour une qualité irréprochable."
  },
  {
    number: "04",
    icon: Rocket,
    title: "Lancement & Optimisation",
    description: "Déploiement sécurisé, formation de vos équipes et monitoring des performances. Optimisations continues basées sur les données d'usage."
  }
]

const ProcessScroll = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [scrollProgress, setScrollProgress] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return

      const container = containerRef.current
      const containerTop = container.offsetTop
      const containerHeight = container.offsetHeight
      const windowHeight = window.innerHeight
      const scrollTop = window.scrollY

      // Calculate scroll progress through this section
      const startScroll = containerTop - windowHeight
      const endScroll = containerTop + containerHeight - windowHeight
      const progress = Math.max(0, Math.min(1, (scrollTop - startScroll) / (endScroll - startScroll)))

      setScrollProgress(progress)
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // Initial calculation

    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Calculate horizontal transform based on scroll progress
  const totalWidth = processSteps.length * 100 // Each step takes 100vw
  const translateX = -(scrollProgress * (totalWidth - 100)) // Move from 0 to -(totalWidth - 100)

  return (
    <div ref={containerRef} className="relative" style={{ height: `${processSteps.length * 100}vh` }}>
      {/* Fixed container for horizontal scrolling */}
      <div className="sticky top-0 h-screen overflow-hidden bg-bg-primary">
        {/* Process Introduction */}
        <div className="absolute inset-0 flex items-center justify-center z-10 bg-bg-primary/90 backdrop-blur-sm transition-opacity duration-500" style={{ opacity: scrollProgress < 0.1 ? 1 : 0 }}>
          <div className="container mx-auto px-6 text-center">
            <h2 className="font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary">
              Notre{' '}
              <span className="gradient-text">Processus</span>
            </h2>
            <p className="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
              Découvrez notre méthodologie étape par étape. Chaque phase est pensée pour maximiser votre succès.
            </p>
          </div>
        </div>

        {/* Horizontal scrolling process steps */}
        <div
          className="flex h-full transition-transform duration-100 ease-out"
          style={{
            transform: `translateX(${translateX}vw)`,
            width: `${totalWidth}vw`
          }}
        >
          {processSteps.map((step, index) => {
            const Icon = step.icon
            const isActive = scrollProgress >= (index / processSteps.length) && scrollProgress < ((index + 1) / processSteps.length)

            return (
              <div
                key={index}
                className="w-screen h-full flex items-center justify-center relative"
                style={{
                  backgroundColor: index % 2 === 0 ? 'var(--bg-secondary)' : 'var(--bg-primary)'
                }}
              >
                <div className="container mx-auto px-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                    <div className={`flex justify-center lg:justify-start order-2 lg:order-1 transition-all duration-700 ${isActive ? 'opacity-100 translate-x-0' : 'opacity-50 translate-x-8'}`}>
                      <div className="relative group">
                        <div className="w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl transition-all duration-500 group-hover:scale-110 group-hover:shadow-2xl group-hover:shadow-primary-green/20">
                          <span className="font-display text-4xl font-bold text-white transition-all duration-300 group-hover:scale-110">
                            {step.number}
                          </span>
                        </div>

                        <div className="absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light transition-all duration-300 group-hover:scale-110 group-hover:rotate-12">
                          <Icon className="w-8 h-8 text-primary-green" />
                        </div>

                        {/* Floating decorative elements */}
                        <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-accent-purple rounded-full opacity-20 animate-float-slow"></div>
                        <div className="absolute -top-8 left-8 w-4 h-4 bg-primary-green rounded-full opacity-30 animate-float-medium"></div>
                      </div>
                    </div>

                    <div className={`text-center lg:text-left order-1 lg:order-2 transition-all duration-700 delay-200 ${isActive ? 'opacity-100 translate-x-0' : 'opacity-30 translate-x-12'}`}>
                      <h3 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary">
                        {step.title}
                      </h3>

                      <p className="text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0">
                        {step.description}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Progress indicator */}
                <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2">
                  {processSteps.map((_, i) => (
                    <div
                      key={i}
                      className={`h-2 rounded-full transition-all duration-300 ${
                        i === index ? 'bg-primary-green w-8' : 'bg-text-muted w-2'
                      }`}
                    />
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default ProcessScroll
