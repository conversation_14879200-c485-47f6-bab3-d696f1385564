'use client'

import { useScrollAnimation } from '@/hooks/useScrollAnimation'
import { Search, Palette, Code, Rocket } from 'lucide-react'

const processSteps = [
  {
    number: "01",
    icon: Search,
    title: "Découverte & Stratégie",
    description: "Nous analysons vos besoins, votre marché et vos objectifs pour définir la stratégie digitale optimale. Ateliers collaboratifs, recherche utilisateur et définition du MVP."
  },
  {
    number: "02",
    icon: Palette,
    title: "Design & Prototypage",
    description: "Création de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et itérations pour valider l'expérience avant le développement."
  },
  {
    number: "03",
    icon: Code,
    title: "Développement Agile",
    description: "Développement en sprints avec livraisons régulières. Code clean, tests automatisés et intégration continue pour une qualité irréprochable."
  },
  {
    number: "04",
    icon: Rocket,
    title: "Lancement & Optimisation",
    description: "Déploiement sécurisé, formation de vos équipes et monitoring des performances. Optimisations continues basées sur les données d'usage."
  }
]

const ProcessScroll = () => {
  const titleAnimation = useScrollAnimation(0.3)

  return (
    <>
      {/* Process Introduction Section */}
      <section className="scroll-section relative flex items-center justify-center bg-bg-primary">
        <div className="container mx-auto px-6 text-center">
          <div
            ref={titleAnimation.ref}
            className={`scroll-fade-in ${titleAnimation.isVisible ? 'visible' : ''}`}
          >
            <h2 className="font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary">
              Notre{' '}
              <span className="gradient-text">Processus</span>
            </h2>
            <p className="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
              Une méthodologie éprouvée qui transforme vos idées en solutions digitales performantes, étape par étape.
            </p>
          </div>
        </div>
      </section>

      {/* Process Discovery Sections */}
      {processSteps.map((step, index) => {
        const Icon = step.icon
        const visualAnimation = useScrollAnimation(0.2)
        const contentAnimation = useScrollAnimation(0.3)

        return (
          <section
            key={index}
            className="scroll-section relative flex items-center justify-center bg-bg-secondary"
          >
            <div className="container mx-auto px-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div
                  ref={visualAnimation.ref}
                  className={`flex justify-center lg:justify-start order-2 lg:order-1 scroll-slide-left ${visualAnimation.isVisible ? 'visible' : ''}`}
                >
                  <div className="relative group">
                    <div className="w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl transition-all duration-500 group-hover:scale-110 group-hover:shadow-2xl group-hover:shadow-primary-green/20">
                      <span className="font-display text-4xl font-bold text-white transition-all duration-300 group-hover:scale-110">
                        {step.number}
                      </span>
                    </div>

                    <div className="absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light transition-all duration-300 group-hover:scale-110 group-hover:rotate-12">
                      <Icon className="w-8 h-8 text-primary-green" />
                    </div>

                    {/* Floating decorative elements */}
                    <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-accent-purple rounded-full opacity-20 animate-float-slow"></div>
                    <div className="absolute -top-8 left-8 w-4 h-4 bg-primary-green rounded-full opacity-30 animate-float-medium"></div>
                  </div>
                </div>

                <div
                  ref={contentAnimation.ref}
                  className={`text-center lg:text-left order-1 lg:order-2 scroll-slide-right ${contentAnimation.isVisible ? 'visible' : ''}`}
                >
                  <h3 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary">
                    {step.title}
                  </h3>

                  <p className="text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0">
                    {step.description}
                  </p>
                </div>
              </div>
            </div>
          </section>
        )
      })}
    </>
  )
}

export default ProcessScroll
