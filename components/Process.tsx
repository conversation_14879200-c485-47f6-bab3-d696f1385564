'use client'

import { useRef } from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'
import { Search, Palette, Code, Rocket } from 'lucide-react'

const processSteps = [
  {
    number: "01",
    icon: Search,
    title: "Découverte & Stratégie",
    description: "Nous analysons vos besoins, votre marché et vos objectifs pour définir la stratégie digitale optimale. Ateliers collaboratifs, recherche utilisateur et définition du MVP."
  },
  {
    number: "02",
    icon: Palette,
    title: "Design & Prototypage",
    description: "Création de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et itérations pour valider l'expérience avant le développement."
  },
  {
    number: "03",
    icon: Code,
    title: "Développement Agile",
    description: "Développement en sprints avec livraisons régulières. Code clean, tests automatisés et intégration continue pour une qualité irréprochable."
  },
  {
    number: "04",
    icon: Rocket,
    title: "Lancement & Optimisation",
    description: "Déploiement sécurisé, formation de vos équipes et monitoring des performances. Optimisations continues basées sur les données d'usage."
  }
]

const Process = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })

  // Transform scroll progress to horizontal movement (reverse direction)
  const x = useTransform(scrollYProgress, [0, 1], ["0%", "30%"])

  return (
    <section ref={containerRef} id="process" className="relative overflow-hidden">
      {/* Fixed height container for scroll-triggered animation */}
      <div className="h-[300vh] relative">
        {/* Sticky container that holds the horizontally moving content */}
        <div className="sticky top-0 h-screen flex items-center overflow-hidden bg-bg-secondary">
          <div className="container mx-auto px-6 mb-16 absolute top-24 left-1/2 transform -translate-x-1/2 z-10">
            <motion.div
              className="text-center max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="font-display text-4xl md:text-6xl font-bold mb-6 text-text-primary">
                Notre Processus
              </h2>
              <p className="text-xl text-text-secondary leading-relaxed">
                Une méthodologie éprouvée qui transforme vos idées en solutions digitales performantes, étape par étape.
              </p>
            </motion.div>
          </div>

          {/* Horizontally moving content */}
          <motion.div
            style={{ x }}
            className="flex gap-12 px-6 md:px-12 items-center h-full"
          >
            {processSteps.map((step, index) => {
              const Icon = step.icon
              return (
                <motion.div
                  key={index}
                  className="flex-shrink-0 w-80 text-center group"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                >
                  <div className="relative mb-8">
                    <motion.div
                      className="w-24 h-24 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:shadow-xl group-hover:shadow-primary-green/25 transition-all duration-300"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                      <span className="font-display text-3xl font-bold text-white">
                        {step.number}
                      </span>
                    </motion.div>

                    <motion.div
                      className="absolute -top-2 -right-2 w-14 h-14 bg-white rounded-full flex items-center justify-center shadow-md border border-border-light group-hover:scale-110 transition-transform duration-300"
                      whileHover={{ rotate: -5 }}
                    >
                      <Icon className="w-7 h-7 text-primary-green" />
                    </motion.div>
                  </div>

                  <h3 className="font-display text-2xl font-semibold mb-4 text-text-primary group-hover:text-primary-green transition-colors duration-300">
                    {step.title}
                  </h3>

                  <p className="text-text-secondary leading-relaxed">
                    {step.description}
                  </p>
                </motion.div>
              )
            })}
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Process
