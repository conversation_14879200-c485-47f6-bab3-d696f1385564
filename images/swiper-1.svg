<svg viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0FDFA"/>
      <stop offset="100%" style="stop-color:#ECFDF5"/>
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF"/>
      <stop offset="100%" style="stop-color:#F0FDFA"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <dropShadow dx="0" dy="8" stdDeviation="16" flood-color="#065F46" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Phone Frame -->
  <rect x="60" y="40" width="280" height="520" rx="40" fill="#2DD4BF" filter="url(#shadow)"/>
  <rect x="70" y="50" width="260" height="500" rx="32" fill="url(#screenGradient)"/>
  
  <!-- Status Bar -->
  <rect x="90" y="70" width="220" height="24" fill="none"/>
  <circle cx="100" cy="82" r="3" fill="#10B981"/>
  <circle cx="112" cy="82" r="3" fill="#34D399"/>
  <circle cx="124" cy="82" r="3" fill="#6EE7B7"/>
  <text x="290" y="85" text-anchor="end" font-family="system-ui" font-size="12" fill="#065F46">9:41</text>
  
  <!-- Header -->
  <text x="200" y="120" text-anchor="middle" font-family="system-ui" font-size="20" font-weight="600" fill="#065F46">Design Studio</text>
  
  <!-- Profile Section -->
  <circle cx="200" cy="170" r="30" fill="#34D399"/>
  <circle cx="200" cy="170" r="25" fill="#FFFFFF"/>
  <circle cx="200" cy="165" r="8" fill="#10B981"/>
  <path d="M185 185 Q200 195 215 185" stroke="#10B981" stroke-width="2" fill="none"/>
  
  <text x="200" y="220" text-anchor="middle" font-family="system-ui" font-size="16" font-weight="500" fill="#065F46">Sarah Wilson</text>
  <text x="200" y="240" text-anchor="middle" font-family="system-ui" font-size="12" fill="#6B7280">UI/UX Designer</text>
  
  <!-- Stats Cards -->
  <rect x="90" y="270" width="65" height="50" rx="12" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="122" y="290" text-anchor="middle" font-family="system-ui" font-size="16" font-weight="600" fill="#10B981">24</text>
  <text x="122" y="305" text-anchor="middle" font-family="system-ui" font-size="10" fill="#6B7280">Projects</text>
  
  <rect x="167" y="270" width="65" height="50" rx="12" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="200" y="290" text-anchor="middle" font-family="system-ui" font-size="16" font-weight="600" fill="#10B981">12</text>
  <text x="200" y="305" text-anchor="middle" font-family="system-ui" font-size="10" fill="#6B7280">Clients</text>
  
  <rect x="244" y="270" width="65" height="50" rx="12" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="277" y="290" text-anchor="middle" font-family="system-ui" font-size="16" font-weight="600" fill="#10B981">5.0</text>
  <text x="277" y="305" text-anchor="middle" font-family="system-ui" font-size="10" fill="#6B7280">Rating</text>
  
  <!-- Project Cards -->
  <rect x="90" y="350" width="220" height="80" rx="16" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <rect x="105" y="365" width="50" height="50" rx="8" fill="#2DD4BF"/>
  <rect x="115" y="375" width="30" height="3" rx="1.5" fill="#FFFFFF"/>
  <rect x="115" y="382" width="20" height="3" rx="1.5" fill="#FFFFFF"/>
  <rect x="115" y="389" width="25" height="3" rx="1.5" fill="#FFFFFF"/>
  
  <text x="170" y="380" font-family="system-ui" font-size="14" font-weight="500" fill="#065F46">Mobile App Design</text>
  <text x="170" y="395" font-family="system-ui" font-size="11" fill="#6B7280">E-commerce Platform</text>
  <circle cx="285" cy="385" r="8" fill="#34D399"/>
  <path d="M281 385 L284 388 L290 382" stroke="#FFFFFF" stroke-width="2" fill="none"/>
  
  <rect x="90" y="445" width="220" height="80" rx="16" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <rect x="105" y="460" width="50" height="50" rx="8" fill="#10B981"/>
  <circle cx="130" cy="475" r="6" fill="#FFFFFF"/>
  <circle cx="130" cy="490" r="6" fill="#FFFFFF"/>
  <circle cx="130" cy="505" r="6" fill="#FFFFFF"/>
  
  <text x="170" y="475" font-family="system-ui" font-size="14" font-weight="500" fill="#065F46">Web Dashboard</text>
  <text x="170" y="490" font-family="system-ui" font-size="11" fill="#6B7280">Analytics Platform</text>
  <circle cx="285" cy="480" r="8" fill="#FCD34D"/>
  <circle cx="285" cy="480" r="3" fill="#FFFFFF"/>
  
  <!-- Floating Elements -->
  <circle cx="350" cy="150" r="4" fill="#34D399" opacity="0.6"/>
  <circle cx="50" cy="200" r="3" fill="#6EE7B7" opacity="0.8"/>
  <path d="M40 400 L50 405 L40 410 L45 405 Z" fill="#2DD4BF" opacity="0.5"/>
  <path d="M360 450 L370 445 L375 455 L365 450 Z" fill="#10B981" opacity="0.6"/>
</svg> 