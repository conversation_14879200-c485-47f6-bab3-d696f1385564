<svg viewBox="0 0 800 500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0FDFA"/>
      <stop offset="100%" style="stop-color:#ECFDF5"/>
    </linearGradient>
    <linearGradient id="cardBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF"/>
      <stop offset="100%" style="stop-color:#F0FDFA"/>
    </linearGradient>
    <linearGradient id="buttonGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#34D399"/>
      <stop offset="100%" style="stop-color:#10B981"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <dropShadow dx="0" dy="4" stdDeviation="8" flood-color="#065F46" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Desktop Frame -->
  <rect x="50" y="30" width="700" height="440" rx="16" fill="#1F2937" filter="url(#shadow)"/>
  <rect x="60" y="40" width="680" height="420" rx="8" fill="url(#heroBg)"/>
  
  <!-- Window Header -->
  <rect x="60" y="40" width="680" height="30" rx="8" fill="#F3F4F6"/>
  <circle cx="80" cy="55" r="5" fill="#EF4444"/>
  <circle cx="100" cy="55" r="5" fill="#F59E0B"/>
  <circle cx="120" cy="55" r="5" fill="#10B981"/>
  <text x="400" y="59" text-anchor="middle" font-family="system-ui" font-size="12" font-weight="500" fill="#374151">darling.boutique - Fashion & Lifestyle</text>
  
  <!-- Navigation Header -->
  <rect x="60" y="70" width="680" height="50" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="90" y="96" font-family="serif" font-size="20" font-weight="400" fill="#065F46">Darling</text>
  
  <!-- Navigation Menu -->
  <text x="250" y="92" font-family="system-ui" font-size="12" fill="#065F46">Collections</text>
  <text x="330" y="92" font-family="system-ui" font-size="12" fill="#065F46">New Arrivals</text>
  <text x="420" y="92" font-family="system-ui" font-size="12" fill="#065F46">Sale</text>
  <text x="470" y="92" font-family="system-ui" font-size="12" fill="#065F46">About</text>
  
  <!-- Search & Cart -->
  <circle cx="580" cy="95" r="12" fill="#F0FDFA" stroke="#2DD4BF" stroke-width="1"/>
  <circle cx="580" cy="91" r="4" fill="none" stroke="#10B981" stroke-width="1"/>
  <line x1="583" y1="94" x2="586" y2="97" stroke="#10B981" stroke-width="1"/>
  
  <rect x="610" y="85" width="20" height="20" rx="4" fill="#34D399"/>
  <circle cx="615" cy="91" r="2" fill="#FFFFFF"/>
  <circle cx="625" cy="91" r="2" fill="#FFFFFF"/>
  <rect x="613" y="93" width="14" height="8" rx="2" fill="#FFFFFF"/>
  <circle cx="635" cy="90" r="6" fill="#EF4444"/>
  <text x="635" y="93" text-anchor="middle" font-family="system-ui" font-size="8" fill="#FFFFFF">2</text>
  
  <!-- Hero Section -->
  <rect x="80" y="140" width="320" height="180" rx="12" fill="url(#buttonGradient)" filter="url(#shadow)"/>
  <text x="110" y="175" font-family="serif" font-size="24" font-weight="400" fill="#FFFFFF">Summer</text>
  <text x="110" y="205" font-family="serif" font-size="24" font-weight="400" fill="#FFFFFF">Collection</text>
  <text x="110" y="235" font-family="system-ui" font-size="12" fill="#F0FDFA">Discover our latest sustainable fashion pieces</text>
  <text x="110" y="250" font-family="system-ui" font-size="12" fill="#F0FDFA">crafted with love and attention to detail.</text>
  
  <rect x="110" y="270" width="100" height="30" rx="15" fill="#FFFFFF"/>
  <text x="160" y="288" text-anchor="middle" font-family="system-ui" font-size="11" font-weight="500" fill="#10B981">Shop Now</text>
  
  <!-- Featured Products -->
  <rect x="420" y="140" width="300" height="180" rx="12" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="440" y="165" font-family="system-ui" font-size="16" font-weight="500" fill="#065F46">Featured Products</text>
  
  <!-- Product Grid -->
  <rect x="440" y="180" width="80" height="100" rx="8" fill="#F0FDFA"/>
  <circle cx="480" cy="210" r="15" fill="#2DD4BF"/>
  <circle cx="480" cy="210" r="8" fill="#FFFFFF"/>
  <text x="440" y="295" font-family="system-ui" font-size="10" font-weight="500" fill="#065F46">Silk Dress</text>
  <text x="440" y="305" font-family="system-ui" font-size="8" fill="#6B7280">$189</text>
  
  <rect x="535" y="180" width="80" height="100" rx="8" fill="#F0FDFA"/>
  <rect x="555" y="200" width="40" height="25" rx="4" fill="#34D399"/>
  <rect x="560" y="205" width="30" height="3" rx="1.5" fill="#FFFFFF"/>
  <rect x="560" y="210" width="20" height="3" rx="1.5" fill="#FFFFFF"/>
  <rect x="560" y="215" width="25" height="3" rx="1.5" fill="#FFFFFF"/>
  <text x="535" y="295" font-family="system-ui" font-size="10" font-weight="500" fill="#065F46">Cotton Tee</text>
  <text x="535" y="305" font-family="system-ui" font-size="8" fill="#6B7280">$45</text>
  
  <rect x="630" y="180" width="80" height="100" rx="8" fill="#F0FDFA"/>
  <rect x="650" y="195" width="40" height="40" rx="20" fill="#10B981"/>
  <circle cx="670" cy="215" r="8" fill="#FFFFFF"/>
  <text x="630" y="295" font-family="system-ui" font-size="10" font-weight="500" fill="#065F46">Summer Hat</text>
  <text x="630" y="305" font-family="system-ui" font-size="8" fill="#6B7280">$75</text>
  
  <!-- Product Categories -->
  <rect x="80" y="340" width="640" height="100" fill="transparent"/>
  <text x="80" y="365" font-family="system-ui" font-size="18" font-weight="500" fill="#065F46">Shop by Category</text>
  
  <!-- Category Cards -->
  <rect x="80" y="380" width="120" height="50" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <circle cx="110" cy="395" r="8" fill="#2DD4BF"/>
  <path d="M105 395 L110 390 L115 395 L110 400 Z" fill="#FFFFFF"/>
  <text x="130" y="398" font-family="system-ui" font-size="11" font-weight="500" fill="#065F46">Dresses</text>
  <text x="130" y="410" font-family="system-ui" font-size="8" fill="#6B7280">24 items</text>
  
  <rect x="220" y="380" width="120" height="50" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <circle cx="250" cy="395" r="8" fill="#10B981"/>
  <rect x="245" y="390" width="10" height="10" rx="2" fill="#FFFFFF"/>
  <text x="270" y="398" font-family="system-ui" font-size="11" font-weight="500" fill="#065F46">Tops</text>
  <text x="270" y="410" font-family="system-ui" font-size="8" fill="#6B7280">18 items</text>
  
  <rect x="360" y="380" width="120" height="50" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <circle cx="390" cy="395" r="8" fill="#34D399"/>
  <circle cx="390" cy="395" r="3" fill="#FFFFFF"/>
  <text x="410" y="398" font-family="system-ui" font-size="11" font-weight="500" fill="#065F46">Accessories</text>
  <text x="410" y="410" font-family="system-ui" font-size="8" fill="#6B7280">32 items</text>
  
  <rect x="500" y="380" width="120" height="50" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <circle cx="530" cy="395" r="8" fill="#6EE7B7"/>
  <rect x="526" y="391" width="8" height="8" rx="1" fill="#FFFFFF"/>
  <text x="550" y="398" font-family="system-ui" font-size="11" font-weight="500" fill="#065F46">Shoes</text>
  <text x="550" y="410" font-family="system-ui" font-size="8" fill="#6B7280">15 items</text>
  
  <rect x="640" y="380" width="80" height="50" rx="8" fill="url(#buttonGradient)" filter="url(#shadow)"/>
  <text x="680" y="408" text-anchor="middle" font-family="system-ui" font-size="11" font-weight="500" fill="#FFFFFF">View All</text>
  
  <!-- Decorative Elements -->
  <circle cx="750" cy="100" r="3" fill="#6EE7B7" opacity="0.7"/>
  <circle cx="20" cy="200" r="2" fill="#34D399" opacity="0.6"/>
  <path d="M780 300 L785 295 L790 305 L785 300 Z" fill="#2DD4BF" opacity="0.5"/>
  <circle cx="30" cy="400" r="4" fill="#10B981" opacity="0.6"/>
</svg> 