<svg viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0FDFA"/>
      <stop offset="100%" style="stop-color:#ECFDF5"/>
    </linearGradient>
    <linearGradient id="productCardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF"/>
      <stop offset="100%" style="stop-color:#F0FDFA"/>
    </linearGradient>
    <linearGradient id="buttonGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#34D399"/>
      <stop offset="100%" style="stop-color:#10B981"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <dropShadow dx="0" dy="4" stdDeviation="8" flood-color="#065F46" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Browser Window -->
  <rect x="50" y="20" width="500" height="360" rx="12" fill="#E5E7EB" filter="url(#shadow)"/>
  <rect x="50" y="20" width="500" height="30" rx="12" fill="#F3F4F6"/>
  <rect x="55" y="40" width="490" height="330" fill="url(#bgGradient)"/>
  
  <!-- Browser Controls -->
  <circle cx="70" cy="35" r="4" fill="#EF4444"/>
  <circle cx="85" cy="35" r="4" fill="#F59E0B"/>
  <circle cx="100" cy="35" r="4" fill="#10B981"/>
  
  <!-- Address Bar -->
  <rect x="130" y="27" width="200" height="16" rx="8" fill="#FFFFFF"/>
  <text x="140" y="37" font-family="system-ui" font-size="8" fill="#6B7280">shop.designstore.com</text>
  
  <!-- Header Navigation -->
  <rect x="70" y="60" width="460" height="40" rx="8" fill="url(#productCardGradient)" filter="url(#shadow)"/>
  <text x="90" y="78" font-family="system-ui" font-size="16" font-weight="600" fill="#065F46">DesignStore</text>
  <text x="200" y="78" font-family="system-ui" font-size="11" fill="#6B7280">Products</text>
  <text x="250" y="78" font-family="system-ui" font-size="11" fill="#6B7280">Categories</text>
  <text x="310" y="78" font-family="system-ui" font-size="11" fill="#6B7280">About</text>
  
  <!-- Shopping Cart Icon -->
  <rect x="480" y="70" width="20" height="20" rx="4" fill="#34D399"/>
  <circle cx="485" cy="76" r="2" fill="#FFFFFF"/>
  <circle cx="495" cy="76" r="2" fill="#FFFFFF"/>
  <rect x="483" y="78" width="14" height="8" rx="2" fill="#FFFFFF"/>
  <circle cx="505" cy="75" r="6" fill="#EF4444"/>
  <text x="505" y="78" text-anchor="middle" font-family="system-ui" font-size="8" fill="#FFFFFF">3</text>
  
  <!-- Hero Section -->
  <rect x="70" y="120" width="460" height="80" rx="8" fill="url(#buttonGradient)" filter="url(#shadow)"/>
  <text x="90" y="145" font-family="system-ui" font-size="20" font-weight="600" fill="#FFFFFF">Summer Design Collection</text>
  <text x="90" y="165" font-family="system-ui" font-size="12" fill="#F0FDFA">Get 30% off on all premium design templates</text>
  <rect x="90" y="175" width="80" height="20" rx="10" fill="#FFFFFF"/>
  <text x="130" y="186" text-anchor="middle" font-family="system-ui" font-size="10" font-weight="500" fill="#10B981">Shop Now</text>
  
  <!-- Product Grid -->
  <!-- Product 1 -->
  <rect x="70" y="220" width="140" height="140" rx="8" fill="url(#productCardGradient)" filter="url(#shadow)"/>
  <rect x="85" y="235" width="110" height="70" rx="6" fill="#2DD4BF"/>
  <rect x="95" y="245" width="20" height="20" rx="4" fill="#FFFFFF"/>
  <rect x="120" y="245" width="30" height="4" rx="2" fill="#FFFFFF"/>
  <rect x="120" y="252" width="50" height="4" rx="2" fill="#FFFFFF"/>
  <rect x="120" y="259" width="25" height="4" rx="2" fill="#FFFFFF"/>
  <circle cx="175" cy="285" r="8" fill="#34D399"/>
  <path d="M171 285 L174 288 L180 282" stroke="#FFFFFF" stroke-width="2" fill="none"/>
  
  <text x="85" y="320" font-family="system-ui" font-size="12" font-weight="500" fill="#065F46">UI Kit Pro</text>
  <text x="85" y="335" font-family="system-ui" font-size="10" fill="#6B7280">Complete design system</text>
  <text x="85" y="350" font-family="system-ui" font-size="14" font-weight="600" fill="#10B981">$29</text>
  
  <!-- Product 2 -->
  <rect x="230" y="220" width="140" height="140" rx="8" fill="url(#productCardGradient)" filter="url(#shadow)"/>
  <rect x="245" y="235" width="110" height="70" rx="6" fill="#10B981"/>
  <circle cx="270" cy="260" r="8" fill="#FFFFFF"/>
  <circle cx="290" cy="260" r="8" fill="#FFFFFF"/>
  <circle cx="310" cy="260" r="8" fill="#FFFFFF"/>
  <rect x="260" y="275" width="60" height="3" rx="1.5" fill="#FFFFFF"/>
  <rect x="260" y="282" width="45" height="3" rx="1.5" fill="#FFFFFF"/>
  <rect x="260" y="289" width="40" height="3" rx="1.5" fill="#FFFFFF"/>
  
  <text x="245" y="320" font-family="system-ui" font-size="12" font-weight="500" fill="#065F46">Icon Set</text>
  <text x="245" y="335" font-family="system-ui" font-size="10" fill="#6B7280">500+ vector icons</text>
  <text x="245" y="350" font-family="system-ui" font-size="14" font-weight="600" fill="#10B981">$19</text>
  
  <!-- Product 3 -->
  <rect x="390" y="220" width="140" height="140" rx="8" fill="url(#productCardGradient)" filter="url(#shadow)"/>
  <rect x="405" y="235" width="110" height="70" rx="6" fill="#6EE7B7"/>
  <rect x="415" y="250" width="90" height="20" rx="4" fill="#FFFFFF"/>
  <rect x="420" y="255" width="15" height="10" rx="2" fill="#34D399"/>
  <rect x="440" y="255" width="25" height="3" rx="1.5" fill="#10B981"/>
  <rect x="440" y="260" width="35" height="3" rx="1.5" fill="#10B981"/>
  <rect x="415" y="275" width="90" height="15" rx="4" fill="#FFFFFF"/>
  <circle cx="425" cy="282" r="4" fill="#2DD4BF"/>
  
  <text x="405" y="320" font-family="system-ui" font-size="12" font-weight="500" fill="#065F46">Templates</text>
  <text x="405" y="335" font-family="system-ui" font-size="10" fill="#6B7280">Landing page designs</text>
  <text x="405" y="350" font-family="system-ui" font-size="14" font-weight="600" fill="#10B981">$39</text>
  
  <!-- Floating Elements -->
  <circle cx="560" cy="80" r="3" fill="#6EE7B7" opacity="0.7"/>
  <circle cx="30" cy="160" r="2" fill="#34D399" opacity="0.6"/>
  <path d="M580 300 L585 295 L590 305 L585 300 Z" fill="#2DD4BF" opacity="0.5"/>
  <path d="M20 280 L30 275 L25 285 Z" fill="#10B981" opacity="0.6"/>
  
  <!-- Shopping Indicator -->
  <circle cx="570" cy="340" r="4" fill="#34D399"/>
  <circle cx="580" cy="350" r="4" fill="#10B981"/>
  <circle cx="560" cy="360" r="4" fill="#6EE7B7"/>
</svg> 