<svg viewBox="0 0 218 218" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="appBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0FDFA"/>
      <stop offset="100%" style="stop-color:#ECFDF5"/>
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF"/>
      <stop offset="100%" style="stop-color:#F0FDFA"/>
    </linearGradient>
    <filter id="appShadow" x="-20%" y="-20%" width="140%" height="140%">
      <dropShadow dx="0" dy="4" stdDeviation="8" flood-color="#065F46" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- App Background -->
  <rect x="10" y="10" width="198" height="198" rx="24" fill="url(#appBg)" filter="url(#appShadow)"/>
  
  <!-- Header -->
  <rect x="20" y="25" width="178" height="35" rx="8" fill="url(#cardGradient)"/>
  <circle cx="35" cy="42" r="6" fill="#2DD4BF"/>
  <rect x="32" y="39" width="6" height="6" rx="1" fill="#FFFFFF"/>
  <text x="50" y="46" font-family="system-ui" font-size="12" font-weight="600" fill="#065F46">AutoFlow</text>
  
  <!-- Navigation dots -->
  <circle cx="175" cy="42" r="2" fill="#10B981"/>
  <circle cx="185" cy="42" r="2" fill="#34D399"/>
  
  <!-- Main automation flow -->
  <rect x="20" y="75" width="178" height="120" rx="12" fill="#FFFFFF" filter="url(#appShadow)"/>
  
  <!-- Flow Steps -->
  <circle cx="50" cy="105" r="12" fill="#2DD4BF"/>
  <text x="50" y="109" text-anchor="middle" font-family="system-ui" font-size="8" font-weight="600" fill="#FFFFFF">1</text>
  <text x="50" y="125" text-anchor="middle" font-family="system-ui" font-size="8" fill="#065F46">Trigger</text>
  
  <!-- Arrow -->
  <path d="M70 105 L90 105" stroke="#6EE7B7" stroke-width="3" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6EE7B7"/>
    </marker>
  </defs>
  
  <circle cx="110" cy="105" r="12" fill="#10B981"/>
  <rect x="105" y="100" width="10" height="10" rx="2" fill="#FFFFFF"/>
  <text x="110" y="125" text-anchor="middle" font-family="system-ui" font-size="8" fill="#065F46">Process</text>
  
  <!-- Arrow -->
  <path d="M130 105 L150 105" stroke="#6EE7B7" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <circle cx="170" cy="105" r="12" fill="#34D399"/>
  <path d="M166 105 L169 108 L175 102" stroke="#FFFFFF" stroke-width="2" fill="none"/>
  <text x="170" y="125" text-anchor="middle" font-family="system-ui" font-size="8" fill="#065F46">Complete</text>
  
  <!-- Status indicators -->
  <rect x="30" y="140" width="158" height="45" rx="8" fill="#F0FDFA"/>
  
  <!-- Active workflows -->
  <circle cx="45" cy="155" r="3" fill="#34D399"/>
  <text x="55" y="158" font-family="system-ui" font-size="9" fill="#065F46">24 Active Workflows</text>
  
  <circle cx="45" cy="170" r="3" fill="#2DD4BF"/>
  <text x="55" y="173" font-family="system-ui" font-size="9" fill="#065F46">187 Tasks Completed</text>
  
  <!-- Performance indicator -->
  <rect x="140" y="150" width="40" height="20" rx="10" fill="#34D399"/>
  <text x="160" y="162" text-anchor="middle" font-family="system-ui" font-size="8" font-weight="500" fill="#FFFFFF">98.7%</text>
  
  <!-- Floating elements -->
  <circle cx="185" cy="25" r="2" fill="#6EE7B7" opacity="0.6"/>
  <circle cx="25" cy="185" r="3" fill="#34D399" opacity="0.7"/>
  <path d="M200 180 L205 175 L210 185 L205 180 Z" fill="#2DD4BF" opacity="0.5"/>
</svg> 