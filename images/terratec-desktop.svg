<svg viewBox="0 0 800 500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="techBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0FDFA"/>
      <stop offset="100%" style="stop-color:#ECFDF5"/>
    </linearGradient>
    <linearGradient id="cardBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF"/>
      <stop offset="100%" style="stop-color:#F0FDFA"/>
    </linearGradient>
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#2DD4BF"/>
      <stop offset="100%" style="stop-color:#10B981"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <dropShadow dx="0" dy="4" stdDeviation="8" flood-color="#065F46" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Desktop Frame -->
  <rect x="50" y="30" width="700" height="440" rx="16" fill="#1F2937" filter="url(#shadow)"/>
  <rect x="60" y="40" width="680" height="420" rx="8" fill="url(#techBg)"/>
  
  <!-- Window Header -->
  <rect x="60" y="40" width="680" height="30" rx="8" fill="#F3F4F6"/>
  <circle cx="80" cy="55" r="5" fill="#EF4444"/>
  <circle cx="100" cy="55" r="5" fill="#F59E0B"/>
  <circle cx="120" cy="55" r="5" fill="#10B981"/>
  <text x="400" y="59" text-anchor="middle" font-family="system-ui" font-size="12" font-weight="500" fill="#374151">terratec.io - Cloud Solutions</text>
  
  <!-- Navigation Header -->
  <rect x="60" y="70" width="680" height="50" fill="url(#cardBg)" filter="url(#shadow)"/>
  
  <!-- Logo -->
  <rect x="90" y="85" width="25" height="25" rx="6" fill="url(#primaryGradient)"/>
  <rect x="95" y="90" width="15" height="3" rx="1.5" fill="#FFFFFF"/>
  <rect x="95" y="95" width="10" height="3" rx="1.5" fill="#FFFFFF"/>
  <rect x="95" y="100" width="12" height="3" rx="1.5" fill="#FFFFFF"/>
  <text x="125" y="102" font-family="system-ui" font-size="16" font-weight="600" fill="#065F46">TerraTec</text>
  
  <!-- Navigation Menu -->
  <text x="250" y="92" font-family="system-ui" font-size="12" fill="#065F46">Solutions</text>
  <text x="320" y="92" font-family="system-ui" font-size="12" fill="#065F46">Products</text>
  <text x="390" y="92" font-family="system-ui" font-size="12" fill="#065F46">Pricing</text>
  <text x="450" y="92" font-family="system-ui" font-size="12" fill="#065F46">Resources</text>
  <text x="520" y="92" font-family="system-ui" font-size="12" fill="#065F46">About</text>
  
  <rect x="580" y="82" width="70" height="26" rx="13" fill="url(#primaryGradient)"/>
  <text x="615" y="98" text-anchor="middle" font-family="system-ui" font-size="11" font-weight="500" fill="#FFFFFF">Get Started</text>
  
  <rect x="660" y="82" width="60" height="26" rx="4" fill="transparent" stroke="#10B981" stroke-width="1"/>
  <text x="690" y="98" text-anchor="middle" font-family="system-ui" font-size="11" font-weight="500" fill="#10B981">Sign In</text>
  
  <!-- Hero Section -->
  <rect x="90" y="140" width="360" height="200" fill="transparent"/>
  <text x="90" y="175" font-family="system-ui" font-size="32" font-weight="700" fill="#065F46">Cloud Infrastructure</text>
  <text x="90" y="210" font-family="system-ui" font-size="32" font-weight="700" fill="#065F46">Made Simple</text>
  <text x="90" y="240" font-family="system-ui" font-size="14" fill="#6B7280">Deploy, scale, and manage your applications with our</text>
  <text x="90" y="260" font-family="system-ui" font-size="14" fill="#6B7280">enterprise-grade cloud platform. Built for developers.</text>
  
  <rect x="90" y="285" width="120" height="35" rx="18" fill="url(#primaryGradient)" filter="url(#shadow)"/>
  <text x="150" y="305" text-anchor="middle" font-family="system-ui" font-size="12" font-weight="500" fill="#FFFFFF">Start Free Trial</text>
  
  <rect x="220" y="285" width="120" height="35" rx="4" fill="transparent" stroke="#10B981" stroke-width="2"/>
  <text x="280" y="305" text-anchor="middle" font-family="system-ui" font-size="12" font-weight="500" fill="#10B981">View Demo</text>
  
  <!-- Terminal/Code Window -->
  <rect x="470" y="140" width="250" height="180" rx="8" fill="#1F2937" filter="url(#shadow)"/>
  <rect x="470" y="140" width="250" height="25" rx="8" fill="#374151"/>
  <circle cx="485" cy="152" r="3" fill="#EF4444"/>
  <circle cx="495" cy="152" r="3" fill="#F59E0B"/>
  <circle cx="505" cy="152" r="3" fill="#10B981"/>
  <text x="520" y="157" font-family="monospace" font-size="10" fill="#FFFFFF">terminal</text>
  
  <!-- Terminal Content -->
  <text x="485" y="185" font-family="monospace" font-size="10" fill="#34D399">$ terraform init</text>
  <text x="485" y="200" font-family="monospace" font-size="9" fill="#6B7280">Initializing provider plugins...</text>
  <text x="485" y="215" font-family="monospace" font-size="10" fill="#34D399">$ terraform plan</text>
  <text x="485" y="230" font-family="monospace" font-size="9" fill="#6B7280">Plan: 3 to add, 0 to change</text>
  <text x="485" y="245" font-family="monospace" font-size="10" fill="#34D399">$ terraform apply</text>
  <text x="485" y="260" font-family="monospace" font-size="9" fill="#2DD4BF">Apply complete! Resources: 3 added</text>
  <text x="485" y="275" font-family="monospace" font-size="10" fill="#34D399">$ █</text>
  
  <!-- Features Section -->
  <rect x="90" y="360" width="620" height="80" fill="transparent"/>
  <text x="90" y="385" font-family="system-ui" font-size="18" font-weight="600" fill="#065F46">Why Choose TerraTec?</text>
  
  <!-- Feature Cards -->
  <rect x="90" y="400" width="140" height="35" rx="6" fill="url(#cardBg)" filter="url(#shadow)"/>
  <circle cx="110" cy="417" r="6" fill="#2DD4BF"/>
  <path d="M106 417 L109 420 L115 414" stroke="#FFFFFF" stroke-width="2" fill="none"/>
  <text x="125" y="420" font-family="system-ui" font-size="11" font-weight="500" fill="#065F46">99.9% Uptime</text>
  
  <rect x="250" y="400" width="140" height="35" rx="6" fill="url(#cardBg)" filter="url(#shadow)"/>
  <circle cx="270" cy="417" r="6" fill="#10B981"/>
  <rect x="267" y="414" width="6" height="6" rx="1" fill="#FFFFFF"/>
  <text x="285" y="420" font-family="system-ui" font-size="11" font-weight="500" fill="#065F46">Auto Scaling</text>
  
  <rect x="410" y="400" width="140" height="35" rx="6" fill="url(#cardBg)" filter="url(#shadow)"/>
  <circle cx="430" cy="417" r="6" fill="#34D399"/>
  <circle cx="430" cy="414" r="2" fill="#FFFFFF"/>
  <circle cx="430" cy="420" r="2" fill="#FFFFFF"/>
  <text x="445" y="420" font-family="system-ui" font-size="11" font-weight="500" fill="#065F46">24/7 Support</text>
  
  <rect x="570" y="400" width="140" height="35" rx="6" fill="url(#cardBg)" filter="url(#shadow)"/>
  <circle cx="590" cy="417" r="6" fill="#6EE7B7"/>
  <path d="M587 417 C587 414 589 414 592 415 C593 416 593 418 590 418 Z" fill="#FFFFFF"/>
  <text x="605" y="420" font-family="system-ui" font-size="11" font-weight="500" fill="#065F46">Global CDN</text>
  
  <!-- Decorative Tech Elements -->
  <circle cx="750" cy="100" r="3" fill="#6EE7B7" opacity="0.7"/>
  <rect x="20" y="180" width="4" height="4" rx="1" fill="#34D399" opacity="0.6"/>
  <circle cx="780" cy="300" r="2" fill="#2DD4BF" opacity="0.8"/>
  <path d="M30 400 L35 395 L40 405 L35 400 Z" fill="#10B981" opacity="0.6"/>
  
  <!-- Floating Code Snippets -->
  <rect x="15" y="250" width="25" height="15" rx="2" fill="#1F2937" opacity="0.8"/>
  <rect x="18" y="253" width="8" height="1" rx="0.5" fill="#2DD4BF"/>
  <rect x="18" y="256" width="12" height="1" rx="0.5" fill="#34D399"/>
  <rect x="18" y="259" width="6" height="1" rx="0.5" fill="#10B981"/>
  
  <rect x="760" y="350" width="25" height="15" rx="2" fill="#1F2937" opacity="0.8"/>
  <rect x="763" y="353" width="10" height="1" rx="0.5" fill="#2DD4BF"/>
  <rect x="763" y="356" width="8" height="1" rx="0.5" fill="#34D399"/>
  <rect x="763" y="359" width="14" height="1" rx="0.5" fill="#10B981"/>
</svg> 