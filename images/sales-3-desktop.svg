<svg viewBox="0 0 800 500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="analyticsBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0FDFA"/>
      <stop offset="100%" style="stop-color:#ECFDF5"/>
    </linearGradient>
    <linearGradient id="cardBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF"/>
      <stop offset="100%" style="stop-color:#F0FDFA"/>
    </linearGradient>
    <linearGradient id="donutGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2DD4BF"/>
      <stop offset="50%" style="stop-color:#10B981"/>
      <stop offset="100%" style="stop-color:#34D399"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <dropShadow dx="0" dy="4" stdDeviation="8" flood-color="#065F46" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Desktop Frame -->
  <rect x="50" y="30" width="700" height="440" rx="16" fill="#1F2937" filter="url(#shadow)"/>
  <rect x="60" y="40" width="680" height="420" rx="8" fill="url(#analyticsBg)"/>
  
  <!-- Window Header -->
  <rect x="60" y="40" width="680" height="30" rx="8" fill="#F3F4F6"/>
  <circle cx="80" cy="55" r="5" fill="#EF4444"/>
  <circle cx="100" cy="55" r="5" fill="#F59E0B"/>
  <circle cx="120" cy="55" r="5" fill="#10B981"/>
  <text x="400" y="59" text-anchor="middle" font-family="system-ui" font-size="12" font-weight="500" fill="#374151">SalesAnalytics Pro - Advanced Reports</text>
  
  <!-- Header Navigation -->
  <rect x="60" y="70" width="680" height="45" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="90" y="96" font-family="system-ui" font-size="16" font-weight="600" fill="#065F46">Analytics Pro</text>
  
  <!-- Breadcrumb -->
  <text x="200" y="90" font-family="system-ui" font-size="10" fill="#6B7280">Dashboard</text>
  <text x="255" y="90" font-family="system-ui" font-size="10" fill="#6B7280">/</text>
  <text x="265" y="90" font-family="system-ui" font-size="10" fill="#10B981">Sales Reports</text>
  
  <!-- Date Filter -->
  <rect x="500" y="82" width="100" height="22" rx="4" fill="#F0FDFA" stroke="#2DD4BF" stroke-width="1"/>
  <text x="510" y="95" font-family="system-ui" font-size="10" fill="#065F46">Last 30 Days</text>
  <path d="M585 90 L590 93 L585 96" stroke="#10B981" stroke-width="1" fill="none"/>
  
  <rect x="610" y="82" width="60" height="22" rx="11" fill="#34D399"/>
  <text x="640" y="95" text-anchor="middle" font-family="system-ui" font-size="10" font-weight="500" fill="#FFFFFF">Export</text>
  
  <!-- Performance Overview -->
  <rect x="80" y="130" width="640" height="60" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="100" y="150" font-family="system-ui" font-size="16" font-weight="500" fill="#065F46">Performance Overview</text>
  <text x="100" y="165" font-family="system-ui" font-size="11" fill="#6B7280">Key metrics for the selected period</text>
  
  <!-- Metric Cards in Overview -->
  <rect x="100" y="175" width="80" height="10" rx="2" fill="#2DD4BF"/>
  <text x="185" y="178" font-family="system-ui" font-size="8" fill="#065F46">Revenue: $127K (+15%)</text>
  
  <rect x="300" y="175" width="80" height="10" rx="2" fill="#10B981"/>
  <text x="385" y="178" font-family="system-ui" font-size="8" fill="#065F46">Deals: 89 (+8%)</text>
  
  <rect x="500" y="175" width="80" height="10" rx="2" fill="#34D399"/>
  <text x="585" y="178" font-family="system-ui" font-size="8" fill="#065F46">Conversion: 12.4% (+2.1%)</text>
  
  <!-- Charts Section -->
  <!-- Revenue Breakdown Chart -->
  <rect x="80" y="210" width="300" height="180" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="100" y="235" font-family="system-ui" font-size="14" font-weight="500" fill="#065F46">Revenue by Source</text>
  
  <!-- Donut Chart -->
  <circle cx="230" cy="310" r="50" fill="none" stroke="#F0FDFA" stroke-width="20"/>
  <circle cx="230" cy="310" r="50" fill="none" stroke="#2DD4BF" stroke-width="20" 
          stroke-dasharray="94 314" stroke-dashoffset="0" transform="rotate(-90 230 310)"/>
  <circle cx="230" cy="310" r="50" fill="none" stroke="#10B981" stroke-width="20" 
          stroke-dasharray="63 314" stroke-dashoffset="-94" transform="rotate(-90 230 310)"/>
  <circle cx="230" cy="310" r="50" fill="none" stroke="#34D399" stroke-width="20" 
          stroke-dasharray="47 314" stroke-dashoffset="-157" transform="rotate(-90 230 310)"/>
  <circle cx="230" cy="310" r="50" fill="none" stroke="#6EE7B7" stroke-width="20" 
          stroke-dasharray="110 314" stroke-dashoffset="-204" transform="rotate(-90 230 310)"/>
  
  <!-- Legend -->
  <circle cx="110" cy="340" r="4" fill="#2DD4BF"/>
  <text x="120" y="344" font-family="system-ui" font-size="9" fill="#065F46">Direct Sales (30%)</text>
  <circle cx="110" cy="355" r="4" fill="#10B981"/>
  <text x="120" y="359" font-family="system-ui" font-size="9" fill="#065F46">Partners (20%)</text>
  <circle cx="110" cy="370" r="4" fill="#34D399"/>
  <text x="120" y="374" font-family="system-ui" font-size="9" fill="#065F46">Online (15%)</text>
  
  <!-- Sales Team Performance -->
  <rect x="400" y="210" width="320" height="180" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="420" y="235" font-family="system-ui" font-size="14" font-weight="500" fill="#065F46">Team Performance</text>
  
  <!-- Performance Bars -->
  <text x="420" y="255" font-family="system-ui" font-size="10" fill="#6B7280">Sarah Johnson</text>
  <rect x="420" y="260" width="120" height="12" rx="6" fill="#F0FDFA"/>
  <rect x="420" y="260" width="90" height="12" rx="6" fill="#2DD4BF"/>
  <text x="555" y="268" font-family="system-ui" font-size="9" fill="#065F46">$45K (Goal: $50K)</text>
  
  <text x="420" y="285" font-family="system-ui" font-size="10" fill="#6B7280">Mike Chen</text>
  <rect x="420" y="290" width="120" height="12" rx="6" fill="#F0FDFA"/>
  <rect x="420" y="290" width="108" height="12" rx="6" fill="#10B981"/>
  <text x="555" y="298" font-family="system-ui" font-size="9" fill="#065F46">$54K (Goal: $50K)</text>
  
  <text x="420" y="315" font-family="system-ui" font-size="10" fill="#6B7280">Emma Davis</text>
  <rect x="420" y="320" width="120" height="12" rx="6" fill="#F0FDFA"/>
  <rect x="420" y="320" width="75" height="12" rx="6" fill="#34D399"/>
  <text x="555" y="328" font-family="system-ui" font-size="9" fill="#065F46">$38K (Goal: $50K)</text>
  
  <text x="420" y="345" font-family="system-ui" font-size="10" fill="#6B7280">Alex Rodriguez</text>
  <rect x="420" y="350" width="120" height="12" rx="6" fill="#F0FDFA"/>
  <rect x="420" y="350" width="96" height="12" rx="6" fill="#6EE7B7"/>
  <text x="555" y="358" font-family="system-ui" font-size="9" fill="#065F46">$48K (Goal: $50K)</text>
  
  <!-- Recent Activity Feed -->
  <rect x="80" y="410" width="640" height="40" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="100" y="430" font-family="system-ui" font-size="12" font-weight="500" fill="#065F46">Recent Activity</text>
  
  <circle cx="120" cy="440" r="3" fill="#2DD4BF"/>
  <text x="130" y="443" font-family="system-ui" font-size="9" fill="#065F46">New deal created by Sarah Johnson - $15K potential</text>
  
  <circle cx="400" cy="440" r="3" fill="#10B981"/>
  <text x="410" y="443" font-family="system-ui" font-size="9" fill="#065F46">Mike Chen closed deal with TechCorp - $25K</text>
  
  <circle cx="620" cy="440" r="3" fill="#34D399"/>
  <text x="630" y="443" font-family="system-ui" font-size="9" fill="#065F46">5 min ago</text>
  
  <!-- Decorative Elements -->
  <circle cx="750" cy="100" r="3" fill="#6EE7B7" opacity="0.7"/>
  <circle cx="20" cy="250" r="2" fill="#34D399" opacity="0.6"/>
  <path d="M780 350 L785 345 L790 355 L785 350 Z" fill="#2DD4BF" opacity="0.5"/>
  <circle cx="30" cy="420" r="4" fill="#10B981" opacity="0.6"/>
  
  <!-- Floating Data Points -->
  <rect x="15" y="300" width="8" height="8" rx="2" fill="#2DD4BF" opacity="0.7"/>
  <rect x="770" y="180" width="6" height="6" rx="1" fill="#34D399" opacity="0.8"/>
  <circle cx="780" cy="280" r="2" fill="#10B981" opacity="0.9"/>
</svg> 