<svg viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="screenBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0FDFA"/>
      <stop offset="100%" style="stop-color:#ECFDF5"/>
    </linearGradient>
    <linearGradient id="cardBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF"/>
      <stop offset="100%" style="stop-color:#F0FDFA"/>
    </linearGradient>
    <linearGradient id="chartGradient" x1="0%" y1="100%" x2="0%" y2="0%">
      <stop offset="0%" style="stop-color:#2DD4BF" stop-opacity="0.3"/>
      <stop offset="100%" style="stop-color:#2DD4BF"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <dropShadow dx="0" dy="4" stdDeviation="8" flood-color="#065F46" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Browser Window -->
  <rect x="50" y="30" width="500" height="340" rx="12" fill="#E5E7EB" filter="url(#shadow)"/>
  <rect x="50" y="30" width="500" height="30" rx="12" fill="#F3F4F6"/>
  <rect x="55" y="50" width="490" height="310" fill="url(#screenBg)"/>
  
  <!-- Browser Controls -->
  <circle cx="70" cy="45" r="4" fill="#EF4444"/>
  <circle cx="85" cy="45" r="4" fill="#F59E0B"/>
  <circle cx="100" cy="45" r="4" fill="#10B981"/>
  
  <!-- Address Bar -->
  <rect x="130" y="37" width="200" height="16" rx="8" fill="#FFFFFF"/>
  <text x="140" y="47" font-family="system-ui" font-size="8" fill="#6B7280">dashboard.designstudio.com</text>
  
  <!-- Header -->
  <rect x="70" y="80" width="460" height="50" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="90" y="100" font-family="system-ui" font-size="18" font-weight="600" fill="#065F46">Analytics Dashboard</text>
  <text x="90" y="115" font-family="system-ui" font-size="11" fill="#6B7280">Track your design projects performance</text>
  
  <!-- Profile Icon -->
  <circle cx="490" cy="105" r="15" fill="#34D399"/>
  <circle cx="490" cy="100" r="5" fill="#FFFFFF"/>
  <path d="M482 115 Q490 120 498 115" stroke="#FFFFFF" stroke-width="1.5" fill="none"/>
  
  <!-- Stats Cards Row -->
  <rect x="70" y="150" width="100" height="60" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="85" y="170" font-family="system-ui" font-size="12" fill="#6B7280">Total Views</text>
  <text x="85" y="190" font-family="system-ui" font-size="20" font-weight="600" fill="#10B981">24.8K</text>
  <text x="85" y="200" font-family="system-ui" font-size="9" fill="#34D399">+12.5%</text>
  
  <rect x="185" y="150" width="100" height="60" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="200" y="170" font-family="system-ui" font-size="12" fill="#6B7280">Projects</text>
  <text x="200" y="190" font-family="system-ui" font-size="20" font-weight="600" fill="#10B981">47</text>
  <text x="200" y="200" font-family="system-ui" font-size="9" fill="#34D399">+8.2%</text>
  
  <rect x="300" y="150" width="100" height="60" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="315" y="170" font-family="system-ui" font-size="12" fill="#6B7280">Revenue</text>
  <text x="315" y="190" font-family="system-ui" font-size="20" font-weight="600" fill="#10B981">$12.4K</text>
  <text x="315" y="200" font-family="system-ui" font-size="9" fill="#34D399">+15.3%</text>
  
  <rect x="415" y="150" width="100" height="60" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="430" y="170" font-family="system-ui" font-size="12" fill="#6B7280">Clients</text>
  <text x="430" y="190" font-family="system-ui" font-size="20" font-weight="600" fill="#10B981">23</text>
  <text x="430" y="200" font-family="system-ui" font-size="9" fill="#34D399">+4.1%</text>
  
  <!-- Chart Section -->
  <rect x="70" y="230" width="280" height="120" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="85" y="250" font-family="system-ui" font-size="14" font-weight="500" fill="#065F46">Project Performance</text>
  
  <!-- Chart Area -->
  <polyline points="90,320 110,300 130,310 150,285 170,295 190,270 210,280 230,255 250,265 270,240 290,250 310,225 330,235"
            stroke="#2DD4BF" stroke-width="3" fill="none"/>
  <polygon points="90,320 110,300 130,310 150,285 170,295 190,270 210,280 230,255 250,265 270,240 290,250 310,225 330,235 330,330 90,330"
           fill="url(#chartGradient)"/>
  
  <!-- Chart Points -->
  <circle cx="130" cy="310" r="3" fill="#10B981"/>
  <circle cx="190" cy="270" r="3" fill="#10B981"/>
  <circle cx="250" cy="265" r="3" fill="#10B981"/>
  <circle cx="310" cy="225" r="3" fill="#10B981"/>
  
  <!-- Activity Feed -->
  <rect x="365" y="230" width="150" height="120" rx="8" fill="url(#cardBg)" filter="url(#shadow)"/>
  <text x="380" y="250" font-family="system-ui" font-size="14" font-weight="500" fill="#065F46">Recent Activity</text>
  
  <!-- Activity Items -->
  <circle cx="385" cy="270" r="4" fill="#34D399"/>
  <text x="395" y="273" font-family="system-ui" font-size="9" fill="#065F46">Project completed</text>
  <text x="395" y="283" font-family="system-ui" font-size="8" fill="#6B7280">2 hours ago</text>
  
  <circle cx="385" cy="295" r="4" fill="#2DD4BF"/>
  <text x="395" y="298" font-family="system-ui" font-size="9" fill="#065F46">New client signed</text>
  <text x="395" y="308" font-family="system-ui" font-size="8" fill="#6B7280">5 hours ago</text>
  
  <circle cx="385" cy="320" r="4" fill="#10B981"/>
  <text x="395" y="323" font-family="system-ui" font-size="9" fill="#065F46">Design approved</text>
  <text x="395" y="333" font-family="system-ui" font-size="8" fill="#6B7280">1 day ago</text>
  
  <!-- Decorative Elements -->
  <circle cx="560" cy="90" r="3" fill="#6EE7B7" opacity="0.7"/>
  <circle cx="40" cy="180" r="2" fill="#34D399" opacity="0.6"/>
  <path d="M580 350 L585 345 L590 355 L585 350 Z" fill="#2DD4BF" opacity="0.5"/>
  <path d="M25 320 L35 315 L30 325 Z" fill="#10B981" opacity="0.6"/>
</svg> 