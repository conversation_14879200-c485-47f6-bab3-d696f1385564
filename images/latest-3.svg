<svg viewBox="0 0 218 218" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="lifestyleBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0FDFA"/>
      <stop offset="100%" style="stop-color:#ECFDF5"/>
    </linearGradient>
    <linearGradient id="fashionCard" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF"/>
      <stop offset="100%" style="stop-color:#F0FDFA"/>
    </linearGradient>
    <filter id="lifestyleShadow" x="-20%" y="-20%" width="140%" height="140%">
      <dropShadow dx="0" dy="4" stdDeviation="8" flood-color="#065F46" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" width="198" height="198" rx="24" fill="url(#lifestyleBg)" filter="url(#lifestyleShadow)"/>
  
  <!-- Header -->
  <rect x="20" y="25" width="178" height="30" rx="8" fill="url(#fashionCard)"/>
  <text x="35" y="44" font-family="serif" font-size="14" font-weight="400" fill="#065F46">Bloom</text>
  
  <!-- Menu icon -->
  <rect x="175" y="35" width="15" height="2" rx="1" fill="#10B981"/>
  <rect x="175" y="39" width="15" height="2" rx="1" fill="#10B981"/>
  <rect x="175" y="43" width="15" height="2" rx="1" fill="#10B981"/>
  
  <!-- Hero section -->
  <rect x="20" y="65" width="178" height="80" rx="12" fill="#34D399" filter="url(#lifestyleShadow)"/>
  <text x="35" y="90" font-family="serif" font-size="16" font-weight="400" fill="#FFFFFF">Summer</text>
  <text x="35" y="110" font-family="serif" font-size="16" font-weight="400" fill="#FFFFFF">Collection</text>
  <text x="35" y="125" font-family="system-ui" font-size="8" fill="#F0FDFA">Natural beauty, sustainable fashion</text>
  
  <!-- CTA Button -->
  <rect x="35" y="130" width="50" height="12" rx="6" fill="#FFFFFF"/>
  <text x="60" y="138" text-anchor="middle" font-family="system-ui" font-size="7" font-weight="500" fill="#10B981">Shop Now</text>
  
  <!-- Decorative element -->
  <circle cx="160" cy="90" r="15" fill="#FFFFFF" opacity="0.2"/>
  <circle cx="160" cy="90" r="8" fill="#FFFFFF" opacity="0.3"/>
  <path d="M155 90 C155 87, 157 87, 160 88 C161 89, 161 91, 158 91 Z" fill="#10B981"/>
  
  <!-- Product grid -->
  <rect x="20" y="155" width="50" height="45" rx="8" fill="url(#fashionCard)" filter="url(#lifestyleShadow)"/>
  <circle cx="45" cy="170" r="8" fill="#2DD4BF"/>
  <circle cx="45" cy="170" r="4" fill="#FFFFFF"/>
  <text x="45" y="188" text-anchor="middle" font-family="system-ui" font-size="7" fill="#065F46">Dress</text>
  <text x="45" y="196" text-anchor="middle" font-family="system-ui" font-size="6" fill="#6B7280">$89</text>
  
  <rect x="80" y="155" width="50" height="45" rx="8" fill="url(#fashionCard)" filter="url(#lifestyleShadow)"/>
  <rect x="95" y="165" width="20" height="15" rx="3" fill="#10B981"/>
  <rect x="98" y="168" width="14" height="2" rx="1" fill="#FFFFFF"/>
  <rect x="98" y="172" width="10" height="2" rx="1" fill="#FFFFFF"/>
  <text x="105" y="188" text-anchor="middle" font-family="system-ui" font-size="7" fill="#065F46">Top</text>
  <text x="105" y="196" text-anchor="middle" font-family="system-ui" font-size="6" fill="#6B7280">$45</text>
  
  <rect x="140" y="155" width="50" height="45" rx="8" fill="url(#fashionCard)" filter="url(#lifestyleShadow)"/>
  <rect x="155" y="165" width="20" height="15" rx="10" fill="#6EE7B7"/>
  <circle cx="165" cy="172" r="3" fill="#FFFFFF"/>
  <text x="165" y="188" text-anchor="middle" font-family="system-ui" font-size="7" fill="#065F46">Hat</text>
  <text x="165" y="196" text-anchor="middle" font-family="system-ui" font-size="6" fill="#6B7280">$32</text>
  
  <!-- Floating elements -->
  <circle cx="190" cy="30" r="2" fill="#6EE7B7" opacity="0.6"/>
  <circle cx="30" cy="180" r="3" fill="#34D399" opacity="0.7"/>
  <path d="M200 200 L205 195 L210 205 L205 200 Z" fill="#2DD4BF" opacity="0.5"/>
  
  <!-- Decorative leaves -->
  <path d="M25 70 Q30 65, 35 70 Q30 75, 25 70 Z" fill="#6EE7B7" opacity="0.4"/>
  <path d="M185 140 Q190 135, 195 140 Q190 145, 185 140 Z" fill="#34D399" opacity="0.4"/>
</svg> 