(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{3057:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var i=t(5155);let n=()=>(0,i.jsx)("div",{className:"min-h-screen bg-black text-white p-6 md:p-10 lg:p-12",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6",children:"Terms of Service"}),(0,i.jsxs)("div",{className:"space-y-6 text-base leading-relaxed",children:[(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"1. Introduction"}),(0,i.jsx)("p",{className:"mb-6",children:'Welcome to MorningsideAI. These Terms of Service ("Terms") govern your access to and use of our website and services. By accessing or using the service, you agree to be bound by these Terms.'})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"2. Services Provided"}),(0,i.jsx)("p",{className:"mb-6",children:"MorningsideAI provides AI strategy, Autonomous Agent Development, Enterprise Consulting, Chatbot Development and these services are subject to the terms and conditions outlined in this document."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"3. Changes to Terms"}),(0,i.jsx)("p",{className:"mb-6",children:"We reserve the right to modify these Terms at any time. We will notify users of any changes by posting the new Terms on this site. Your continued use of the service after such changes constitutes your agreement to the new Terms."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"4. Account Registration and Use"}),(0,i.jsx)("p",{className:"mb-6",children:"To access certain features of our service, you may be required to create an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"5. Privacy Policy"}),(0,i.jsx)("p",{className:"mb-6",children:"Our Privacy Policy, which describes how we handle personal data, is available on our website and forms part of these Terms."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"6. User Conduct"}),(0,i.jsx)("p",{className:"mb-6",children:"You agree to use the service only for lawful purposes and not to use the service for any illegal or unauthorized purpose."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"7. Intellectual Property"}),(0,i.jsx)("p",{className:"mb-6",children:"All intellectual property rights in the service and its content are the exclusive property of MorningsideAI or its licensors."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"8. Third-Party Services"}),(0,i.jsx)("p",{className:"mb-6",children:"Our service may contain links to third-party websites or services that are not owned or controlled by MorningsideAI. We have no control over, and assume no responsibility for, the content or practices of any third-party websites or services."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"9. Termination"}),(0,i.jsx)("p",{className:"mb-6",children:"We may terminate or suspend access to our service immediately, without prior notice or liability, for any reason, including without limitation if you breach the Terms."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"10. Governing Law"}),(0,i.jsx)("p",{className:"mb-6",children:"These Terms shall be governed by and construed in accordance with the laws of New Zealand, without regard to its conflict of law provisions."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"11. Changes to Service"}),(0,i.jsx)("p",{className:"mb-6",children:"We reserve the right to withdraw or amend our service, and any service or material we provide via the service, in our sole discretion without notice."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"12. Disclaimer and Limitation of Liability"}),(0,i.jsx)("p",{className:"mb-6",children:'The service and its content are provided "as is" without warranty of any kind. In no event will MorningsideAI, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the service.'})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-3",children:"13. Contact Us"}),(0,i.jsxs)("p",{className:"mb-6",children:["For any questions about these Terms, please contact us using the following information:",(0,i.jsx)("br",{}),"Email address: <EMAIL>",(0,i.jsx)("br",{}),"Postal address: 1 Albert Street, Auckland, 1010, New Zealand"]})]})]})]})})},8405:(e,s,t)=>{Promise.resolve().then(t.bind(t,3057))}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(8405)),_N_E=e.O()}]);