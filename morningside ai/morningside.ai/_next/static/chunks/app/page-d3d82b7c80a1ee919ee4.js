(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3672:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>F});var o=s(5155),r=s(2115),n=s(4900),l=s(786),a=s(8519),i=s(912),p=s(824),c=s(9237),d=s(7094),f=s(7448),x=s(4501),u=s(9677),h=s(9088),g=s(802);s(1324),g.Ay.registerPlugin(h.u);let y=[{title:"Introduction",index:0,targetId:null},{title:"IDENTIFY",index:1,targetId:"snappy-32"},{title:"EDUCATE",index:2,targetId:"snappy-33"},{title:"DEVELOP",index:3,targetId:"snappy-34"}];function j(e){let{isMobile:t,isTablet:s}=e,n=(0,r.useRef)(null),l=(0,r.useRef)(null),a=(0,r.useRef)(null),i=(0,r.useRef)(null),[p,c]=(0,r.useState)(null);return(0,r.useEffect)(()=>{var e;let o=document.querySelector("#page-wrapper"),r=l.current,p=n.current,d=document.getElementById("outerCircle"),f=document.getElementById("innerCircle"),x=document.getElementById("innerCircleHighlight"),u=i.current;if(!r||!p||!o||!d||!f||!x)return;g.Ay.set(p,{autoAlpha:0}),g.Ay.set(r,{y:0,scale:1}),h.u.create({trigger:"#snappy-31",start:"top center",end:"top center",scroller:o,onEnter:()=>{g.Ay.to(p,{autoAlpha:1,duration:.8,ease:"none"})}}),g.Ay.timeline({scrollTrigger:{trigger:"#snappy-31",start:"top 90%",endTrigger:"#snappy-32",end:"top top",scrub:!0,scroller:o}}).fromTo(r,{bottom:t?"-75vh":s?"-52rem":"-48rem",scale:t||s?1.2:1},{bottom:t?"32vh":s?"36vh":"14vh",scale:t?.55:s?.6:.4,ease:"none"}),g.Ay.timeline({scrollTrigger:{trigger:"#snappy-32",start:"top 90%",endTrigger:"#snappy-32",end:"top top",scrub:!0,scroller:o}}).fromTo(u,{opacity:0,duration:.2,ease:"none"},{opacity:1,duration:.2,ease:"none"});let y=document.querySelectorAll(".smallSphere"),j=Array.from(y)[3],m=j.cloneNode(!0);m.classList.add("clonedSphere"),null==(e=j.parentElement)||e.appendChild(m),g.Ay.set(m,{scale:t?.7:s?.6:1,opacity:0,yPercent:0});let C=g.Ay.timeline({scrollTrigger:{trigger:"#snappy-32",start:"top top",endTrigger:"#snappy-33",end:"+=100%",scrub:!0,scroller:o}});C.to([d,x],{opacity:0,ease:"none",duration:.2}).to([f],{opacity:0,duration:.2,ease:"none"}).to(a.current,{opacity:1,duration:.2,ease:"none",onStart:()=>{var e;let t=document.getElementById("innerCircle"),s=a.current;if(!t||!s)return;let o=t.getBoundingClientRect(),r=null==(e=n.current)?void 0:e.getBoundingClientRect();if(!r)return;let l=o.top-r.top;g.Ay.set(s,{position:"absolute",top:l+"px",left:"50%",xPercent:-50,yPercent:-50})}});let F=Math.floor(y.length/2),w=t?78:s?140:155,A=t?.85:s?.7:1;C.to(y,{x:e=>(e-F)*w,y:0,xPercent:-50,yPercent:0,scale:A,duration:.4,delay:.1,ease:"none"}).to(m,{x:t||s?(1-F)*w:(3-F)*w,y:0,xPercent:-50,yPercent:0,marginTop:"0rem",scale:A,duration:.1,ease:"none"});let O=g.Ay.timeline({scrollTrigger:{trigger:"#snappy-33",start:"center 60%",endTrigger:"#snappy-34",end:"top 10%",scrub:!0,scroller:o}});document.querySelectorAll(".smallSphere .morph-shape"),document.querySelectorAll(".smallSphere .morph-shape-highlight"),y.forEach(e=>{O.to(e,{opacity:1,duration:.3,ease:"power1.out",scrollTrigger:{trigger:"#snappy-34",start:"top bottom",end:"top center",scrub:!0,scroller:o}})}),g.Ay.timeline({scrollTrigger:{trigger:"#snappy-33",start:"bottom bottom",endTrigger:"#snappy-34",end:"top top",scrub:.5,scroller:o}}),O.to({},{duration:.2}),O.to(a.current,{gap:t||s?"0.1rem":"0px",opacity:1,duration:.2,delay:.2,ease:"power1.inOut"});let S=g.Ay.timeline();if(y.forEach((e,o)=>{let r=e.getBoundingClientRect().height,n=t?r/3:s?r/2.8:r/3,l=-1;1===o&&(l=1),3===o&&(l=-3),4===o&&(l=-1),S.to(e,{y:l*n,marginTop:"0rem",ease:"power2.inOut",duration:1.6},"<")}),O.add(S),m){let e=m.getBoundingClientRect().height,o=t?e/1.9:s?e/1.5:e/3;O.fromTo(m,{ease:"power2.inOut",y:o,opacity:0,duration:1.6},{y:o,ease:"power2.inOut",opacity:1,duration:1.6})}h.u.create({trigger:"#snappy-4",start:"top center",end:"top bottom",scroller:o,onLeave:()=>{g.Ay.to(p,{autoAlpha:0,duration:.1,ease:"power2.out"})}}),h.u.create({trigger:"#snappy-34",start:"top center",end:"bottom center",scroller:o,onEnterBack:()=>{g.Ay.to(p,{autoAlpha:1,duration:.05,ease:"power2.out"})}}),[{trigger:"#snappy-31",labelIndex:null},{trigger:"#snappy-32",labelIndex:1},{trigger:"#snappy-33",labelIndex:2},{trigger:"#snappy-34",labelIndex:3}].forEach(e=>{let{trigger:t,labelIndex:s}=e;h.u.create({trigger:t,start:"top bottom",end:"bottom bottom",scroller:o,onEnter:()=>c(s),onEnterBack:()=>c(s),onLeave:()=>c(null),onLeaveBack:()=>c(null)})})},[t,s]),(0,r.useEffect)(()=>{let e=()=>{l.current&&g.Ay.set(l.current,{x:0,xPercent:-50}),h.u.refresh()};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,o.jsxs)("div",{ref:n,className:"fixed top-0 left-0 w-full h-full pointer-events-none z-50 opacity-0",id:"masterAnimationWrapper",children:[(0,o.jsxs)("svg",{ref:l,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 294 294",fill:"none",className:"absolute left-1/2 -translate-x-1/2 max-w-screen w-[31rem] md:w-[31rem] lg:w-[42rem] aspect-square",children:[(0,o.jsxs)("g",{opacity:.8,id:"outerCircle",children:[(0,o.jsx)("g",{filter:"url(#a)",children:(0,o.jsx)("path",{fill:"url(#b)",d:"M294 147C294 65.814 228.186 0 147 0S0 65.814 0 147s65.814 147 147 147 147-65.814 147-147Z"})}),(0,o.jsx)("path",{stroke:"url(#c)",strokeWidth:2.5,d:"M292.75 147C292.75 66.504 227.495 1.25 147 1.25 66.504 1.25 1.25 66.504 1.25 147c0 80.495 65.254 145.75 145.75 145.75 80.495 0 145.75-65.255 145.75-145.75Z"})]}),(0,o.jsx)("g",{filter:"url(#d)",id:"innerCircle",children:(0,o.jsx)("path",{fill:"url(#e)",d:"M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z"})}),(0,o.jsx)("path",{id:"innerCircleHighlight",stroke:"url(#f)",strokeWidth:2.5,d:"M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{id:"b",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"c",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("linearGradient",{id:"e",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"f",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("filter",{id:"a",width:294,height:318.088,x:0,y:-24.088,colorInterpolationFilters:"sRGB",filterUnits:"userSpaceOnUse",children:[(0,o.jsx)("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),(0,o.jsx)("feBlend",{in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,o.jsx)("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),(0,o.jsx)("feOffset",{dy:-24.088}),(0,o.jsx)("feGaussianBlur",{stdDeviation:18.066}),(0,o.jsx)("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),(0,o.jsx)("feColorMatrix",{values:"0 0 0 0 0.536175 0 0 0 0 0.741662 0 0 0 0 0.638918 0 0 0 0.7 0"}),(0,o.jsx)("feBlend",{in2:"shape",result:"effect1_innerShadow_0_1"})]}),(0,o.jsxs)("filter",{id:"d",width:162,height:186.088,x:65,y:41.912,colorInterpolationFilters:"sRGB",filterUnits:"userSpaceOnUse",children:[(0,o.jsx)("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),(0,o.jsx)("feBlend",{in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,o.jsx)("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),(0,o.jsx)("feOffset",{dy:-24.088}),(0,o.jsx)("feGaussianBlur",{stdDeviation:18.066}),(0,o.jsx)("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),(0,o.jsx)("feColorMatrix",{values:"0 0 0 0 0.536175 0 0 0 0 0.741662 0 0 0 0 0.638918 0 0 0 0.7 0"}),(0,o.jsx)("feBlend",{in2:"shape",result:"effect1_innerShadow_0_1"})]})]})]}),(0,o.jsxs)("div",{ref:a,className:"absolute w-10/12 md:w-11/12 lg:w-6/12 h-fit flex flex-row items-center justify-center gap-2 md:gap-4 lg:gap-4 pointer-events-none opacity-0",children:[(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"64 64 166 166",fill:"none",className:"smallSphere hidden lg:block absolute left-1/2 top-1/2 -translate-x-1/2 w-[25%] lg:w-[9.5rem] aspect-square opacity-20",children:[(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"url(#e)",d:"M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z",className:"morph-shape"})}),(0,o.jsx)("path",{stroke:"url(#f)",strokeWidth:2.5,d:"M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z",className:"morph-shape-highlight"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{id:"b",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"c",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("linearGradient",{id:"e",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"f",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]})]})]}),(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"64 64 166 166",fill:"none",className:"smallSphere absolute left-1/2  top-1/2 -translate-x-1/2 w-[25%] lg:w-[9.5rem] aspect-square opacity-35",children:[(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"url(#e)",d:"M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z",className:"morph-shape"})}),(0,o.jsx)("path",{stroke:"url(#f)",strokeWidth:2.5,d:"M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z",className:"morph-shape-highlight"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{id:"b",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"c",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("linearGradient",{id:"e",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"f",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]})]})]}),(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"64 64 166 166",fill:"none",className:"smallSphere absolute left-1/2 top-1/2 -translate-x-1/2 w-[25%] lg:w-[9.5rem] aspect-square opacity-50",children:[(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"url(#e)",d:"M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z",className:"morph-shape"})}),(0,o.jsx)("path",{stroke:"url(#f)",strokeWidth:2.5,d:"M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z",className:"morph-shape-highlight"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{id:"b",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"c",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("linearGradient",{id:"e",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"f",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]})]})]}),(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"64 64 166 166",fill:"none",className:"smallSphere absolute left-1/2  top-1/2 -translate-x-1/2 w-[25%] lg:w-[9.5rem] aspect-square opacity-75",children:[(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"url(#e)",d:"M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z",className:"morph-shape"})}),(0,o.jsx)("path",{stroke:"url(#f)",strokeWidth:2.5,d:"M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z",className:"morph-shape-highlight"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{id:"b",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"c",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("linearGradient",{id:"e",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"f",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]})]})]}),(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"64 64 166 166",fill:"none",className:"smallSphere hidden lg:block absolute left-1/2 top-1/2 -translate-x-1/2 w-[25%] lg:w-[9.5rem] aspect-square opacity-100",children:[(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"url(#e)",d:"M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z",className:"morph-shape"})}),(0,o.jsx)("path",{stroke:"url(#f)",strokeWidth:2.5,d:"M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z",className:"morph-shape-highlight"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{id:"b",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"c",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("linearGradient",{id:"e",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"f",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]})]})]})]}),(0,o.jsx)("div",{ref:i,className:"absolute left-1/2 lg:left-2 lg:top-1/2 bottom-[1vh]  -translate-x-1/2 lg:-translate-x-0 lg:-translate-y-1/2  -translate-y-1/2 flex flex-row lg:flex-col lg:items-start  items-center justify-center lg:gap-2 gap-6  px-4 md:px-8 lg:px-12 mx-auto  pointer-events-auto z-[999]",children:y.map((e,t)=>(0,o.jsx)("button",{className:"\n              text-left \n              ".concat(0===t?"hidden":""," \n              transition-colors duration-300\n              ").concat(p===t?"text-white":"text-gray-500","\n            "),style:{fontFamily:"DM-Mono-Light, monospace"},children:e.title},e.title))})]})}g.Ay.registerPlugin(h.u);let m=[{title:"Introduction",index:0,targetId:null},{title:"IDENTIFY",index:1,targetId:"snappy-32"},{title:"EDUCATE",index:2,targetId:"snappy-33"},{title:"DEVELOP",index:3,targetId:"snappy-34"}];function C(e){let{isMobile:t,isTablet:s}=e,n=(0,r.useRef)(null),l=(0,r.useRef)(null),a=(0,r.useRef)(null),i=(0,r.useRef)(null),[p,c]=(0,r.useState)(null);return(0,r.useEffect)(()=>{var e;let o=document.querySelector("#page-wrapper"),r=l.current,p=n.current,d=document.getElementById("outerCircle"),f=document.getElementById("innerCircle"),x=document.getElementById("innerCircleHighlight"),u=i.current;if(!r||!p||!o||!d||!f||!x)return;g.Ay.set(p,{autoAlpha:0}),g.Ay.set(r,{y:0,scale:1}),h.u.create({trigger:"#snappy-31",start:"top center",end:"top center",scroller:o,onEnter:()=>{g.Ay.to(p,{autoAlpha:1,duration:.8,ease:"none"})}}),g.Ay.timeline({scrollTrigger:{trigger:"#snappy-31",start:"top 90%",endTrigger:"#snappy-32",end:"top top",scrub:!0,scroller:o}}).fromTo(r,{bottom:t?"-75vh":s?"-52rem":"-48rem",scale:t||s?1.2:1},{bottom:t||s?"36vh":"14vh",scale:t?.42:s?.6:.4,ease:"none"}),g.Ay.timeline({scrollTrigger:{trigger:"#snappy-32",start:"top 90%",endTrigger:"#snappy-32",end:"top top",scrub:!0,scroller:o}}).fromTo(u,{opacity:0,duration:.2,ease:"none"},{opacity:1,duration:.2,ease:"none"});let y=document.querySelectorAll(".smallSphere"),j=Array.from(y),m=j[1],C=m.cloneNode(!0);C.classList.add("clonedSphere"),null==(e=m.parentElement)||e.appendChild(C),g.Ay.set(C,{opacity:0,yPercent:0});let F=g.Ay.timeline({scrollTrigger:{trigger:"#snappy-32",start:"top top",endTrigger:"#snappy-33",end:"+=100%",scrub:!0,scroller:o}});F.to([d,x],{opacity:0,ease:"none",duration:.2}).to([f],{opacity:0,duration:.2,ease:"none"}).to(a.current,{opacity:1,duration:.2,scale:1.25,ease:"none",onStart:()=>{var e;let t=document.getElementById("innerCircle"),s=a.current;if(!t||!s)return;let o=t.getBoundingClientRect(),r=null==(e=n.current)?void 0:e.getBoundingClientRect();if(!r)return;let l=o.top-r.top;g.Ay.set(s,{position:"absolute",top:l+"px",left:"50%",xPercent:-50,yPercent:-50})}}),y.length;let w=t?100:155;F.to(j,{x:e=>0===e?-w:1===e?0:2===e?w:0,y:0,xPercent:-50,yPercent:0,scale:1,duration:.4,delay:.1,ease:"none"});let A=g.Ay.timeline({scrollTrigger:{trigger:"#snappy-33",start:"center 70%",endTrigger:"#snappy-34",end:"top 10%",scrub:!0,scroller:o}});document.querySelectorAll(".smallSphere .morph-shape"),document.querySelectorAll(".smallSphere .morph-shape-highlight"),y.forEach(e=>{A.to(e,{opacity:1,duration:.3,ease:"power1.out",scrollTrigger:{trigger:"#snappy-34",start:"top bottom",end:"top center",scrub:!0,scroller:o}})}),g.Ay.timeline({scrollTrigger:{trigger:"#snappy-33",start:"bottom bottom",endTrigger:"#snappy-34",end:"top top",scrub:.5,scroller:o}}),A.to({},{duration:.2}),A.to(a.current,{gap:t||s?"0.1rem":"0px",opacity:1,duration:.2,delay:.2,ease:"power1.inOut"});let O=g.Ay.timeline();j.forEach((e,t)=>{let s=e.getBoundingClientRect().height/3;O.to(e,{y:(1===t?-1:1)*s,marginTop:"0rem",ease:"power2.inOut",duration:1.6},"<")}),C&&Array.from(document.querySelectorAll(".clonedSphere")).forEach((e,o)=>{let r=C.getBoundingClientRect().height,n=t||s?r:r/3;O.fromTo(e,{ease:"power2.inOut",y:n,opacity:0,duration:1.6},{y:n,ease:"power2.inOut",opacity:1,duration:1.6})}),A.add(O),h.u.create({trigger:"#snappy-4",start:"top center",end:"top bottom",scroller:o,onLeave:()=>{g.Ay.to(p,{autoAlpha:0,duration:.1,ease:"power2.out"})}}),h.u.create({trigger:"#snappy-34",start:"top center",end:"bottom center",scroller:o,onEnterBack:()=>{g.Ay.to(p,{autoAlpha:1,duration:.05,ease:"power2.out"})}}),[{trigger:"#snappy-31",labelIndex:null},{trigger:"#snappy-32",labelIndex:1},{trigger:"#snappy-33",labelIndex:2},{trigger:"#snappy-34",labelIndex:3}].forEach(e=>{let{trigger:t,labelIndex:s}=e;h.u.create({trigger:t,start:"top bottom",end:"bottom bottom",scroller:o,onEnter:()=>c(s),onEnterBack:()=>c(s),onLeave:()=>c(null),onLeaveBack:()=>c(null)})})},[t,s]),(0,r.useEffect)(()=>{let e=()=>{l.current&&g.Ay.set(l.current,{x:0,xPercent:-50}),h.u.refresh()};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,o.jsxs)("div",{ref:n,className:"fixed top-0 left-0 w-full h-full pointer-events-none z-50 opacity-0",id:"masterAnimationWrapper",children:[(0,o.jsxs)("svg",{ref:l,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 294 294",fill:"none",className:"absolute left-1/2 -translate-x-1/2 max-w-screen w-[31rem] md:w-[31rem] lg:w-[42rem] aspect-square",children:[(0,o.jsxs)("g",{opacity:.8,id:"outerCircle",children:[(0,o.jsx)("g",{filter:"url(#a)",children:(0,o.jsx)("path",{fill:"url(#b)",d:"M294 147C294 65.814 228.186 0 147 0S0 65.814 0 147s65.814 147 147 147 147-65.814 147-147Z"})}),(0,o.jsx)("path",{stroke:"url(#c)",strokeWidth:2.5,d:"M292.75 147C292.75 66.504 227.495 1.25 147 1.25 66.504 1.25 1.25 66.504 1.25 147c0 80.495 65.254 145.75 145.75 145.75 80.495 0 145.75-65.255 145.75-145.75Z"})]}),(0,o.jsx)("g",{filter:"url(#d)",id:"innerCircle",children:(0,o.jsx)("path",{fill:"url(#e)",d:"M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z"})}),(0,o.jsx)("path",{id:"innerCircleHighlight",stroke:"url(#f)",strokeWidth:2.5,d:"M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{id:"b",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"c",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("linearGradient",{id:"e",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"f",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("filter",{id:"a",width:294,height:318.088,x:0,y:-24.088,colorInterpolationFilters:"sRGB",filterUnits:"userSpaceOnUse",children:[(0,o.jsx)("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),(0,o.jsx)("feBlend",{in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,o.jsx)("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),(0,o.jsx)("feOffset",{dy:-24.088}),(0,o.jsx)("feGaussianBlur",{stdDeviation:18.066}),(0,o.jsx)("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),(0,o.jsx)("feColorMatrix",{values:"0 0 0 0 0.536175 0 0 0 0 0.741662 0 0 0 0 0.638918 0 0 0 0.7 0"}),(0,o.jsx)("feBlend",{in2:"shape",result:"effect1_innerShadow_0_1"})]}),(0,o.jsxs)("filter",{id:"d",width:162,height:186.088,x:65,y:41.912,colorInterpolationFilters:"sRGB",filterUnits:"userSpaceOnUse",children:[(0,o.jsx)("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),(0,o.jsx)("feBlend",{in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,o.jsx)("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),(0,o.jsx)("feOffset",{dy:-24.088}),(0,o.jsx)("feGaussianBlur",{stdDeviation:18.066}),(0,o.jsx)("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),(0,o.jsx)("feColorMatrix",{values:"0 0 0 0 0.536175 0 0 0 0 0.741662 0 0 0 0 0.638918 0 0 0 0.7 0"}),(0,o.jsx)("feBlend",{in2:"shape",result:"effect1_innerShadow_0_1"})]})]})]}),(0,o.jsxs)("div",{ref:a,className:"absolute w-10/12 md:w-11/12 lg:w-6/12 md:bg-red-700 h-fit flex flex-row items-center justify-center gap-2 md:gap-8 lg:gap-4 pointer-events-none opacity-0",children:[(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"64 64 166 166",fill:"none",className:"smallSphere absolute left-1/2 top-1/2 -translate-x-1/2 w-[22%] md:w-[20%] lg:w-[9.5rem] aspect-square opacity-70",children:[(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"url(#e)",d:"M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z",className:"morph-shape"})}),(0,o.jsx)("path",{stroke:"url(#f)",strokeWidth:2.5,d:"M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z",className:"morph-shape-highlight"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{id:"b",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"c",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("linearGradient",{id:"e",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"f",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]})]})]}),(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"64 64 166 166",fill:"none",className:"smallSphere absolute left-1/2 top-1/2 -translate-x-1/2 w-[22%] md:w-[20%] lg:w-[9.5rem] aspect-square opacity-85",children:[(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"url(#e)",d:"M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z",className:"morph-shape"})}),(0,o.jsx)("path",{stroke:"url(#f)",strokeWidth:2.5,d:"M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z",className:"morph-shape-highlight"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{id:"b",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"c",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("linearGradient",{id:"e",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"f",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]})]})]}),(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"64 64 166 166",fill:"none",className:"smallSphere absolute left-1/2  top-1/2 -translate-x-1/2 w-[22%] md:w-[20%] lg:w-[9.5rem] aspect-square opacity-100",children:[(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"url(#e)",d:"M227 147c0-44.735-36.265-81-81-81s-81 36.265-81 81 36.265 81 81 81 81-36.265 81-81Z",className:"morph-shape"})}),(0,o.jsx)("path",{stroke:"url(#f)",strokeWidth:2.5,d:"M225.75 147c0-44.045-35.705-79.75-79.75-79.75S66.25 102.955 66.25 147s35.705 79.75 79.75 79.75 79.75-35.705 79.75-79.75Z",className:"morph-shape-highlight"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{id:"b",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"c",x1:147,x2:147,y1:0,y2:294,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]}),(0,o.jsxs)("linearGradient",{id:"e",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#fff"}),(0,o.jsx)("stop",{offset:.6,stopColor:"#4CAA7D",stopOpacity:.1}),(0,o.jsx)("stop",{offset:1,stopColor:"#E1FFF1",stopOpacity:.5})]}),(0,o.jsxs)("linearGradient",{id:"f",x1:146,x2:146,y1:66,y2:228,gradientUnits:"userSpaceOnUse",children:[(0,o.jsx)("stop",{stopColor:"#FDFFFE"}),(0,o.jsx)("stop",{offset:.4,stopColor:"#549876"}),(0,o.jsx)("stop",{offset:1,stopColor:"#fff"})]})]})]})]}),(0,o.jsx)("div",{ref:i,className:"absolute left-1/2 lg:left-2 lg:top-1/2 bottom-[1vh]  -translate-x-1/2 lg:-translate-x-0 lg:-translate-y-1/2  -translate-y-1/2 flex flex-row lg:flex-col lg:items-start  items-center justify-center lg:gap-2 gap-6  px-4 md:px-8 lg:px-12 mx-auto  pointer-events-auto z-[999]",children:m.map((e,t)=>(0,o.jsx)("button",{className:"\n              text-left \n              ".concat(0===t?"hidden":""," \n              transition-colors duration-300\n              ").concat(p===t?"text-white":"text-gray-500","\n            "),style:{fontFamily:"DM-Mono-Light, monospace"},children:e.title},e.title))})]})}function F(){let e=(0,r.useRef)(null),{isMobile:t,isTablet:s}=function(){let[e,t]=(0,r.useState)(!1),[s,o]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=window.innerWidth;t(e<640),o(e>=640&&e<1024)},[]),{isMobile:e,isTablet:s}}();return(0,o.jsxs)(n.A,{children:[t||s?(0,o.jsx)(C,{isMobile:t,isTablet:s}):(0,o.jsx)(j,{isMobile:!1,isTablet:!1}),(0,o.jsxs)("div",{id:"page-wrapper",ref:e,tabIndex:0,role:"region","aria-label":"Main content sections",className:"snap-y snap-mandatory overflow-y-scroll h-[100dvh] min-h-[100dvh] w-full overflow-x-hidden no-scrollbar",children:[(0,o.jsx)("div",{className:"snap-always snap-center min-h-screen",children:(0,o.jsx)(l.A,{})}),(0,o.jsx)("div",{id:"snappy-2",className:"snap-always snap-center min-h-screen",children:(0,o.jsx)(a.A,{})}),(0,o.jsx)("div",{id:"snappy-31",className:"snap-always snap-center min-h-screen",children:(0,o.jsx)(i.A,{})}),(0,o.jsx)("div",{id:"snappy-32",className:"snap-always snap-center min-h-screen",children:(0,o.jsx)(p.A,{})}),(0,o.jsx)("div",{id:"snappy-33",className:"snap-always snap-center min-h-screen",children:(0,o.jsx)(c.A,{})}),(0,o.jsx)("div",{id:"snappy-34",className:"snap-always snap-center min-h-screen",children:(0,o.jsx)(d.A,{})}),(0,o.jsx)("div",{id:"snappy-4",className:"snap-always snap-center min-h-screen",children:(0,o.jsx)(f.A,{scrollContainerRef:e})}),(0,o.jsx)("div",{className:"snap-always snap-center min-h-screen",children:(0,o.jsx)(x.A,{})}),(0,o.jsx)("div",{className:"snap-always snap-center",children:(0,o.jsx)(u.A,{})})]})]})}},7676:(e,t,s)=>{Promise.resolve().then(s.bind(s,3672))}},e=>{var t=t=>e(e.s=t);e.O(0,[77,506,206,202,592,844,521,297,262,73,921,441,684,358],()=>t(7676)),_N_E=e.O()}]);