"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[262],{956:(e,l,a)=>{a.d(l,{A:()=>d});var s=a(5155),n=a(289),r=a(7129),t=a(748),i=a(7509);let c=r.Ik().shape({name:r.Yj().min(2,"Name must be at least 2 characters").required("Required"),email:r.Yj().email("Invalid email").required("Required"),role:r.Yj().min(2,"Role must be at least 2 characters").required("Required"),company_name:r.Yj().min(2,"Company name must be at least 2 characters").required("Required"),company_website:r.Yj().min(2,"Company website must be at least 2 characters").url("Enter a valid URL (e.g. https://example.com)").required("Required"),company_size:r.Yj().required("Required"),companys_revenue:r.Yj().required("Required"),project_budget:r.Yj().required("Required"),services_needed:r.Yj().required("Required"),message:r.Yj().min(2,"Message must be at least 2 characters").required("Required")}),m=e=>{let{name:l,field:a}=e;return(0,s.jsxs)("div",{className:"flex flex-row items-center gap-1",children:[(0,s.jsx)("p",{className:"text-md font-medium",children:l}),(0,s.jsx)(n.Kw,{name:a,component:()=>(0,s.jsx)(t.eXv,{size:14,color:"red"})})]})},o=e=>{let{name:l}=e;return(0,s.jsx)(n.Kw,{name:l,component:"div",className:"text-red-500 text-xs"})},d=e=>{let{setSuccess:l}=e;return(0,s.jsx)(n.l1,{initialValues:{name:"",email:"",role:"",company_name:"",company_website:"",company_size:"",companys_revenue:"",project_budget:"",services_needed:"",message:""},validationSchema:c,onSubmit:async(e,a)=>{let{resetForm:s}=a;try{await fetch("/api/sendFormData",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),console.log("Form submitted successfully!"),l(!0),s()}catch(e){console.error(e)}},children:e=>{let{isSubmitting:l}=e;return(0,s.jsxs)(n.lV,{className:"w-full h-full flex flex-col pr-1 gap-[1rem] md:pr-0",children:[(0,s.jsxs)("div",{className:"w-full flex flex-col lg:flex-row gap-2 mb-4",children:[(0,s.jsxs)("div",{className:"w-full lg:w-1/2 flex flex-col gap-2",children:[(0,s.jsx)(m,{name:"What is your name?",field:"name"}),(0,s.jsx)(n.D0,{type:"text",name:"name",placeholder:"Name",className:"border"}),(0,s.jsx)(o,{name:"name"})]}),(0,s.jsxs)("div",{className:"w-full lg:w-1/2 flex flex-col gap-2",children:[(0,s.jsx)(m,{name:"What is your email?",field:"email"}),(0,s.jsx)(n.D0,{type:"email",name:"email",placeholder:"Email",className:"border"}),(0,s.jsx)(o,{name:"email"})]})]}),(0,s.jsxs)("div",{className:"w-full flex flex-col gap-2 mb-4",children:[(0,s.jsx)(m,{name:"What is your role in the company?",field:"role"}),(0,s.jsx)(n.D0,{type:"text",name:"role",placeholder:"Enter role",className:"border"}),(0,s.jsx)(o,{name:"role"})]}),(0,s.jsxs)("div",{className:"w-full flex flex-col lg:flex-row gap-2 mb-4",children:[(0,s.jsxs)("div",{className:"w-full lg:w-1/2 flex flex-col gap-2",children:[(0,s.jsx)(m,{name:"Company Name",field:"company_name"}),(0,s.jsx)(n.D0,{type:"text",name:"company_name",placeholder:"Enter company name",className:"border"}),(0,s.jsx)(o,{name:"company_name"})]}),(0,s.jsxs)("div",{className:"w-full lg:w-1/2 flex flex-col gap-2",children:[(0,s.jsx)(m,{name:"Company Website",field:"company_website"}),(0,s.jsx)(n.D0,{type:"url",name:"company_website",placeholder:"Enter company website",className:"border"}),(0,s.jsx)(o,{name:"company_website"})]})]}),(0,s.jsxs)("div",{className:"w-full flex flex-col lg:flex-row gap-2 mb-4",children:[(0,s.jsxs)("div",{className:"w-full lg:w-1/2 flex flex-col gap-2",children:[(0,s.jsx)(m,{name:"Company Size",field:"company_size"}),(0,s.jsxs)(n.D0,{as:"select",name:"company_size",className:"border",children:[(0,s.jsx)("option",{value:"",children:"Select company size"}),(0,s.jsx)("option",{value:"1-20",children:"Less than 20"}),(0,s.jsx)("option",{value:"20-50",children:"20-50"}),(0,s.jsx)("option",{value:"50-100",children:"50-100"}),(0,s.jsx)("option",{value:"100-500",children:"100-500"}),(0,s.jsx)("option",{value:"500-1000",children:"More than 500"})]}),(0,s.jsx)(o,{name:"company_size"})]}),(0,s.jsxs)("div",{className:"w-full lg:w-1/2 flex flex-col gap-2",children:[(0,s.jsx)(m,{name:"Company's Annual Revenue",field:"companys_revenue"}),(0,s.jsxs)(n.D0,{as:"select",name:"companys_revenue",className:"border",children:[(0,s.jsx)("option",{value:"",children:"Select revenue range"}),(0,s.jsx)("option",{value:"<100K",children:"Less than $100K"}),(0,s.jsx)("option",{value:"100K-500K",children:"$100K-$500K"}),(0,s.jsx)("option",{value:"500K-1M",children:"$500K-$1M"}),(0,s.jsx)("option",{value:"1M-2M",children:"$1M-$2M"}),(0,s.jsx)("option",{value:">2M",children:"More than $2M"})]}),(0,s.jsx)(o,{name:"companys_revenue"})]})]}),(0,s.jsxs)("div",{className:"w-full flex flex-col gap-2 mb-4",children:[(0,s.jsx)(m,{name:"Project budget",field:"project_budget"}),(0,s.jsxs)(n.D0,{as:"select",name:"project_budget",className:"border",children:[(0,s.jsx)("option",{value:"",children:"Select budget range"}),(0,s.jsx)("option",{value:"<10K>",children:"Less than $10K"}),(0,s.jsx)("option",{value:"10K-50K",children:"$10K-$50K"}),(0,s.jsx)("option",{value:"51K-100K",children:"$50K-$100K"}),(0,s.jsx)("option",{value:">100K",children:"More than $100K"})]}),(0,s.jsx)(o,{name:"project_budget"})]}),(0,s.jsxs)("div",{className:"w-full flex flex-col gap-2 mb-4",children:[(0,s.jsx)(m,{name:"What services are you interested in?",field:"services_needed"}),(0,s.jsxs)(n.D0,{as:"select",name:"services_needed",className:"border",children:[(0,s.jsx)("option",{value:"",children:"Select service"}),(0,s.jsx)("option",{value:"Identifying",children:"Identifying AI opportunities"}),(0,s.jsx)("option",{value:"Educating",children:"Educating your team on AI"}),(0,s.jsx)("option",{value:"Developping",children:"Developing custom AI solutions"})]}),(0,s.jsx)(o,{name:"services_needed"})]}),(0,s.jsxs)("div",{className:"w-full flex flex-col gap-2 mb-4",children:[(0,s.jsx)(m,{name:"Message",field:"message"}),(0,s.jsx)(n.D0,{as:"textarea",rows:7,name:"message",placeholder:"Enter message",className:"border resize-none"}),(0,s.jsx)(o,{name:"message"})]}),(0,s.jsx)("button",{type:"submit",disabled:l,className:"w-fit text-black cursor-pointer py-2 px-4 rounded-full border border-black ".concat(l?"opacity-50":""),children:l?(0,s.jsxs)("div",{className:"flex flex-row items-center gap-2",children:[(0,s.jsx)(i.oS8,{size:16,className:"animate-spin"}),"Sending..."]}):"Send inquiry"})]})}})}},8631:(e,l,a)=>{a.d(l,{A:()=>i});var s,n,r=a(2115);function t(){return(t=Object.assign?Object.assign.bind():function(e){for(var l=1;l<arguments.length;l++){var a=arguments[l];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}let i=function(e){return r.createElement("svg",t({xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",viewBox:"0 0 385.6 70",width:"1em",height:"1em"},e),s||(s=r.createElement("defs",null,r.createElement("style",null,".cls-1 {\n        font-family: BrasleyDemo-Medium, 'Brasley Demo';\n        font-size: 42.4px;\n        font-weight: 500;\n      }\n\n      .cls-1, .cls-2, .cls-3 {\n        isolation: isolate;\n      }\n\n      .cls-1, .cls-4 {\n        fill: #fff;\n      }\n\n      .cls-3 {\n        display: none;\n      }"))),n||(n=r.createElement("g",null,r.createElement("g",{id:"Layer_1"},r.createElement("g",{id:"Layer_1-2","data-name":"Layer_1"},r.createElement("g",{id:"Layer_1-2"},r.createElement("g",null,r.createElement("g",{id:"Layer_1-2-2","data-name":"Layer_1-2"},r.createElement("image",{className:"cls-3",width:500,height:129,transform:"translate(-9.4 -14.8) scale(.8)",xlinkHref:"../Morningside White.png"}),r.createElement("g",null,r.createElement("path",{className:"cls-4 logo-shape",d:"M94.7,63.1h31.6v-31.6h-31.6v31.6Z"}),r.createElement("path",{className:"cls-4 logo-shape",d:"M47.3,31.6v31.6l47.3-31.6V0l-47.3,31.6Z"}),r.createElement("path",{className:"cls-4 logo-shape",d:"M0,31.6v31.6l47.3-31.6V0L0,31.6Z"}))),r.createElement("g",{id:"Layer_2"},r.createElement("g",{className:"cls-2"},r.createElement("g",{className:"cls-2"},r.createElement("g",{className:"cls-2"},r.createElement("text",{className:"cls-1 logo-text",transform:"translate(134.1 59.5)"},r.createElement("tspan",{x:0,y:0},"morningside")))))))))))))}}}]);