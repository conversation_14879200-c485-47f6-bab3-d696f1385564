"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[844],{351:(t,e,n)=>{n.d(e,{qdV:()=>i});var r=n(4436);function i(t){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"7",y1:"17",x2:"17",y2:"7"},child:[]},{tag:"polyline",attr:{points:"7 7 17 7 17 17"},child:[]}]})(t)}}}]);