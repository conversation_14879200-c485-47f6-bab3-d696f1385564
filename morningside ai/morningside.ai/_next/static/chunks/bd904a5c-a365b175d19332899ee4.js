"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[831],{3264:(t,e,i)=>{let s,r,n,a,o;i.d(e,{$EB:()=>w,$Ed:()=>aS,$Kf:()=>nG,$NF:()=>hO,$O9:()=>tT,$Yl:()=>q,$_I:()=>tR,$ei:()=>R,$p8:()=>h1,A$4:()=>rF,AKb:()=>hd,ALV:()=>sa,AQS:()=>iD,Am1:()=>ln,B69:()=>re,B6O:()=>a8,BER:()=>tp,BH$:()=>aV,BKk:()=>nr,BND:()=>ux,BRH:()=>hF,BXX:()=>t9,B_h:()=>eo,CMB:()=>tG,CR7:()=>e7,CSG:()=>hs,CV9:()=>oa,CVz:()=>es,CWW:()=>eT,Cfg:()=>tz,CmU:()=>h8,CwR:()=>l_,D$Q:()=>o6,DAe:()=>eF,DXC:()=>aN,Df:()=>ht,Dmk:()=>tq,E0M:()=>uw,EAD:()=>n0,EQC:()=>eR,EZo:()=>_,EdD:()=>T,F1T:()=>nu,F1l:()=>ue,FCc:()=>aF,FFZ:()=>id,FNr:()=>hn,FV:()=>tl,FXf:()=>C,FZo:()=>h5,Fn:()=>ev,Fpm:()=>l6,FvD:()=>aZ,Fvi:()=>tm,Fvt:()=>ha,G3T:()=>ez,GBG:()=>hw,GJx:()=>tw,GOR:()=>a$,GWd:()=>tZ,GYF:()=>n$,GZZ:()=>os,G_z:()=>hi,Gu$:()=>o$,Gwm:()=>K,GxU:()=>rI,H23:()=>ew,H2z:()=>u_,HIg:()=>tX,HLH:()=>e1,HO_:()=>eA,HPb:()=>e6,HXV:()=>ee,HgN:()=>i5,HiM:()=>h$,Hit:()=>hv,Ho_:()=>om,Hrb:()=>rN,Hrq:()=>iv,I46:()=>nY,I9Y:()=>ij,IE4:()=>t4,IUQ:()=>st,IWo:()=>ug,Iit:()=>ne,Ipv:()=>aK,Iw4:()=>lL,IzY:()=>uM,Jnc:()=>p,K52:()=>tt,KDk:()=>en,KLL:()=>e$,KPJ:()=>hz,KRh:()=>ts,Ke9:()=>eN,Kef:()=>eS,Ktl:()=>iy,Kwu:()=>A,Kzg:()=>ls,Kzv:()=>tY,LAk:()=>tc,Ld9:()=>uS,LiQ:()=>N,LlO:()=>ns,LoY:()=>rY,LuO:()=>lq,MBL:()=>hM,MOq:()=>iw,MSw:()=>oG,MW4:()=>rL,Mjd:()=>to,Mmk:()=>iQ,N1A:()=>aR,N2s:()=>lA,N5j:()=>eW,NRn:()=>sc,NTi:()=>S,NZq:()=>tE,Nex:()=>uA,Nt7:()=>j,Nv2:()=>aq,Nwf:()=>lO,Nz6:()=>t6,O0B:()=>se,O3Y:()=>oK,O49:()=>eL,O9p:()=>sZ,ONl:()=>aD,OUM:()=>tP,Oax:()=>rj,Om:()=>tv,OuU:()=>V,P5j:()=>lN,PFK:()=>uc,PJ3:()=>eB,PPD:()=>aa,PTz:()=>so,Pdi:()=>hm,Pem:()=>aX,Pf$:()=>lg,Pq0:()=>sh,Q1f:()=>rv,QCA:()=>oH,QP0:()=>f,Qev:()=>iI,Qrf:()=>eu,R1W:()=>un,R3r:()=>ny,RJ4:()=>eV,ROr:()=>e2,RQf:()=>tj,RcT:()=>it,RiT:()=>h_,Riy:()=>er,Rkk:()=>eU,RlV:()=>sF,RoJ:()=>nM,RrE:()=>H,Ru$:()=>e3,RyA:()=>x,S$4:()=>eb,S20:()=>a0,SUR:()=>h9,SYV:()=>aJ,ScU:()=>hN,T6I:()=>hb,TDQ:()=>oM,THS:()=>rR,TMh:()=>ii,Tap:()=>hV,TdN:()=>iA,TiK:()=>ih,TkQ:()=>t2,U3G:()=>Q,UJ6:()=>hg,UPV:()=>o0,UTZ:()=>ty,Ufg:()=>oX,UpK:()=>nL,UtB:()=>l1,UtX:()=>lr,V3x:()=>tH,V9B:()=>r_,VCu:()=>aQ,VGF:()=>t3,VT0:()=>tK,VVr:()=>eK,Vb5:()=>d,VnP:()=>or,Vnu:()=>im,Vwu:()=>uT,VxR:()=>eG,W9U:()=>eM,WBB:()=>oJ,WNZ:()=>c,WTh:()=>um,Wdf:()=>i_,Wew:()=>tW,Wk7:()=>g,Wyr:()=>iS,XG_:()=>e_,XIg:()=>M,XJ7:()=>o2,XMJ:()=>lj,XTe:()=>h7,XrR:()=>tr,Y9S:()=>hR,YHV:()=>l$,YJl:()=>np,YOZ:()=>hx,YRT:()=>hP,Yhb:()=>hf,Yuy:()=>tV,Z0B:()=>oe,Z58:()=>nx,ZLX:()=>n7,ZM4:()=>ub,ZQM:()=>t0,Zcv:()=>as,Zpd:()=>oQ,Zr2:()=>eY,ZyN:()=>h0,_4j:()=>o8,_QJ:()=>ep,_Ut:()=>ni,_xc:()=>ut,a$r:()=>tI,a55:()=>iR,a5J:()=>ed,aEY:()=>W,aHM:()=>hC,aJ8:()=>td,aMy:()=>eI,aVO:()=>hr,agE:()=>ip,amv:()=>ir,b4q:()=>nc,bC7:()=>ef,bCz:()=>z,bI3:()=>eq,bTm:()=>y,baL:()=>rP,bdM:()=>oZ,bkx:()=>tL,brA:()=>G,bw0:()=>te,c5h:()=>aG,c90:()=>t5,cHt:()=>tF,cRK:()=>nf,cZY:()=>l5,caT:()=>ti,cj9:()=>iL,czI:()=>eh,dAo:()=>on,dYF:()=>sn,dcC:()=>tQ,dhZ:()=>eO,dth:()=>hj,dwI:()=>iU,dzP:()=>lH,e0p:()=>J,eB$:()=>nb,eHc:()=>Z,eHs:()=>nw,eaF:()=>r8,eoi:()=>il,er$:()=>eZ,ezk:()=>hy,f4X:()=>O,fBL:()=>tN,fJr:()=>e5,fP5:()=>lx,fTw:()=>ua,fc6:()=>rO,g7M:()=>tu,gJ2:()=>tD,gO9:()=>I,gPd:()=>i7,gWB:()=>ic,ghU:()=>tM,h2z:()=>eP,hB5:()=>b,hIf:()=>ix,hZF:()=>hS,h_9:()=>hI,hdd:()=>L,hgQ:()=>D,hjs:()=>lQ,hsX:()=>v,hxR:()=>t_,hy7:()=>tf,hzE:()=>uo,i7d:()=>nn,i7u:()=>iT,iNn:()=>r7,iOZ:()=>aY,iUH:()=>tB,ibB:()=>a1,ie2:()=>F,imn:()=>rS,ix0:()=>tU,iyt:()=>sC,j6:()=>o1,jGm:()=>ol,jR7:()=>t8,jUj:()=>ng,jej:()=>iG,jf0:()=>eX,jsO:()=>e9,jut:()=>h4,jzd:()=>iu,k6Q:()=>t7,k6q:()=>tC,k8v:()=>eH,kBv:()=>l,kEx:()=>hE,kG0:()=>e0,kLi:()=>i6,kO0:()=>io,kRr:()=>tk,kTW:()=>tS,kTp:()=>et,kYr:()=>e8,k_V:()=>lK,keZ:()=>ll,klZ:()=>ie,kn4:()=>sV,kqe:()=>eQ,kxk:()=>nO,kyO:()=>ta,l2R:()=>hT,lGu:()=>Y,lGw:()=>hp,lMl:()=>ry,lPF:()=>iX,lc7:()=>eC,ljd:()=>ek,lxW:()=>nt,lyL:()=>ey,mcG:()=>iY,mrM:()=>a_,nCl:()=>hJ,nEu:()=>og,nNL:()=>th,nST:()=>k,nWS:()=>si,nZQ:()=>lu,nc$:()=>lW,nzx:()=>oc,o6l:()=>nd,oVO:()=>e4,oh6:()=>lJ,ojh:()=>E,ojs:()=>ex,ov9:()=>X,pBf:()=>ei,pFK:()=>of,pHI:()=>tA,pPE:()=>lF,paN:()=>t1,ppV:()=>i1,psI:()=>ec,q2:()=>o4,qBx:()=>he,qFE:()=>oy,qU7:()=>od,qUd:()=>hQ,qa3:()=>ea,qad:()=>P,qq$:()=>iJ,qtW:()=>rU,r6x:()=>h6,rFo:()=>ss,rKP:()=>oY,rOG:()=>iM,rQf:()=>eE,rSH:()=>el,rYR:()=>ej,rrX:()=>lU,s0K:()=>oi,sKt:()=>is,sPf:()=>h,tBo:()=>lZ,tJf:()=>tO,tXL:()=>o7,tcD:()=>op,tz3:()=>hA,uB5:()=>em,uSd:()=>o9,uV5:()=>tb,uWO:()=>n1,uXQ:()=>ig,ubm:()=>nl,uov:()=>ib,ure:()=>h2,v9J:()=>iz,veJ:()=>hL,vim:()=>ia,vmz:()=>rV,vyJ:()=>eJ,wAk:()=>ou,wTz:()=>eD,wfO:()=>tx,wn6:()=>U,wrO:()=>tJ,wtR:()=>u,wvS:()=>rE,xFO:()=>tg,xJ6:()=>oU,xOk:()=>h3,xSv:()=>$,xZx:()=>lS,xfg:()=>oo,xiE:()=>i$,y3Z:()=>eg,y9J:()=>sr,y_p:()=>tn,ypk:()=>oS,ywQ:()=>m,zD7:()=>lc,zdS:()=>t$,zgK:()=>sY,znC:()=>B});let h="175",l={LEFT:0,MIDDLE:1,RIGHT:2,ROTATE:0,DOLLY:1,PAN:2},u={ROTATE:0,PAN:1,DOLLY_PAN:2,DOLLY_ROTATE:3},c=0,d=1,p=2,m=3,y=0,f=1,g=2,x=3,b=0,v=1,w=2,M=0,S=1,_=2,A=3,T=4,z=5,I=100,C=101,k=102,B=103,R=104,E=200,P=201,O=202,N=203,F=204,V=205,L=206,j=207,U=208,W=209,D=210,H=211,q=212,J=213,X=214,Z=0,Y=1,G=2,$=3,Q=4,K=5,tt=6,te=7,ti=0,ts=1,tr=2,tn=0,ta=1,to=2,th=3,tl=4,tu=5,tc=6,td=7,tp="attached",tm="detached",ty=300,tf=301,tg=302,tx=303,tb=304,tv=306,tw=1e3,tM=1001,tS=1002,t_=1003,tA=1004,tT=1004,tz=1005,tI=1005,tC=1006,tk=1007,tB=1007,tR=1008,tE=1008,tP=1009,tO=1010,tN=1011,tF=1012,tV=1013,tL=1014,tj=1015,tU=1016,tW=1017,tD=1018,tH=1020,tq=35902,tJ=1021,tX=1022,tZ=1023,tY=1024,tG=1025,t$=1026,tQ=1027,tK=1028,t0=1029,t1=1030,t2=1031,t3=1032,t5=1033,t4=33776,t6=33777,t8=33778,t9=33779,t7=35840,et=35841,ee=35842,ei=35843,es=36196,er=37492,en=37496,ea=37808,eo=37809,eh=37810,el=37811,eu=37812,ec=37813,ed=37814,ep=37815,em=37816,ey=37817,ef=37818,eg=37819,ex=37820,eb=37821,ev=36492,ew=36494,eM=36495,eS=36283,e_=36284,eA=36285,eT=36286,ez=2200,eI=2201,eC=2202,ek=2300,eB=2301,eR=2302,eE=2400,eP=2401,eO=2402,eN=2500,eF=2501,eV=0,eL=1,ej=2,eU=3200,eW=3201,eD=3202,eH=3203,eq=0,eJ=1,eX="",eZ="srgb",eY="srgb-linear",eG="linear",e$="srgb",eQ=0,eK=7680,e0=7681,e1=7682,e2=7683,e3=34055,e5=34056,e4=5386,e6=512,e8=513,e9=514,e7=515,it=516,ie=517,ii=518,is=519,ir=512,ia=513,io=514,ih=515,il=516,iu=517,ic=518,id=519,ip=35044,im=35048,iy=35040,ig=35045,ix=35049,ib=35041,iv=35046,iw=35050,iM=35042,iS="100",i_="300 es",iA=2e3,iT=2001,iz={COMPUTE:"compute",RENDER:"render"};class iI{addEventListener(t,e){void 0===this._listeners&&(this._listeners={});let i=this._listeners;void 0===i[t]&&(i[t]=[]),-1===i[t].indexOf(e)&&i[t].push(e)}hasEventListener(t,e){let i=this._listeners;return void 0!==i&&void 0!==i[t]&&-1!==i[t].indexOf(e)}removeEventListener(t,e){let i=this._listeners;if(void 0===i)return;let s=i[t];if(void 0!==s){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}}dispatchEvent(t){let e=this._listeners;if(void 0===e)return;let i=e[t.type];if(void 0!==i){t.target=this;let e=i.slice(0);for(let i=0,s=e.length;i<s;i++)e[i].call(this,t);t.target=null}}}let iC=["00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff"],ik=1234567,iB=Math.PI/180,iR=180/Math.PI;function iE(){let t=0xffffffff*Math.random()|0,e=0xffffffff*Math.random()|0,i=0xffffffff*Math.random()|0,s=0xffffffff*Math.random()|0;return(iC[255&t]+iC[t>>8&255]+iC[t>>16&255]+iC[t>>24&255]+"-"+iC[255&e]+iC[e>>8&255]+"-"+iC[e>>16&15|64]+iC[e>>24&255]+"-"+iC[63&i|128]+iC[i>>8&255]+"-"+iC[i>>16&255]+iC[i>>24&255]+iC[255&s]+iC[s>>8&255]+iC[s>>16&255]+iC[s>>24&255]).toLowerCase()}function iP(t,e,i){return Math.max(e,Math.min(i,t))}function iO(t,e){return(t%e+e)%e}function iN(t,e,i){return(1-i)*t+i*e}function iF(t,e){switch(e.constructor){case Float32Array:return t;case Uint32Array:return t/0xffffffff;case Uint16Array:return t/65535;case Uint8Array:return t/255;case Int32Array:return Math.max(t/0x7fffffff,-1);case Int16Array:return Math.max(t/32767,-1);case Int8Array:return Math.max(t/127,-1);default:throw Error("Invalid component type.")}}function iV(t,e){switch(e.constructor){case Float32Array:return t;case Uint32Array:return Math.round(0xffffffff*t);case Uint16Array:return Math.round(65535*t);case Uint8Array:return Math.round(255*t);case Int32Array:return Math.round(0x7fffffff*t);case Int16Array:return Math.round(32767*t);case Int8Array:return Math.round(127*t);default:throw Error("Invalid component type.")}}let iL={DEG2RAD:iB,RAD2DEG:iR,generateUUID:iE,clamp:iP,euclideanModulo:iO,mapLinear:function(t,e,i,s,r){return s+(t-e)*(r-s)/(i-e)},inverseLerp:function(t,e,i){return t!==e?(i-t)/(e-t):0},lerp:iN,damp:function(t,e,i,s){return iN(t,e,1-Math.exp(-i*s))},pingpong:function(t,e=1){return e-Math.abs(iO(t,2*e)-e)},smoothstep:function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e))*t*(3-2*t)},smootherstep:function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e))*t*t*(t*(6*t-15)+10)},randInt:function(t,e){return t+Math.floor(Math.random()*(e-t+1))},randFloat:function(t,e){return t+Math.random()*(e-t)},randFloatSpread:function(t){return t*(.5-Math.random())},seededRandom:function(t){void 0!==t&&(ik=t);let e=ik+=0x6d2b79f5;return e=Math.imul(e^e>>>15,1|e),(((e^=e+Math.imul(e^e>>>7,61|e))^e>>>14)>>>0)/0x100000000},degToRad:function(t){return t*iB},radToDeg:function(t){return t*iR},isPowerOfTwo:function(t){return(t&t-1)==0&&0!==t},ceilPowerOfTwo:function(t){return Math.pow(2,Math.ceil(Math.log(t)/Math.LN2))},floorPowerOfTwo:function(t){return Math.pow(2,Math.floor(Math.log(t)/Math.LN2))},setQuaternionFromProperEuler:function(t,e,i,s,r){let n=Math.cos,a=Math.sin,o=n(i/2),h=a(i/2),l=n((e+s)/2),u=a((e+s)/2),c=n((e-s)/2),d=a((e-s)/2),p=n((s-e)/2),m=a((s-e)/2);switch(r){case"XYX":t.set(o*u,h*c,h*d,o*l);break;case"YZY":t.set(h*d,o*u,h*c,o*l);break;case"ZXZ":t.set(h*c,h*d,o*u,o*l);break;case"XZX":t.set(o*u,h*m,h*p,o*l);break;case"YXY":t.set(h*p,o*u,h*m,o*l);break;case"ZYZ":t.set(h*m,h*p,o*u,o*l);break;default:console.warn("THREE.MathUtils: .setQuaternionFromProperEuler() encountered an unknown order: "+r)}},normalize:iV,denormalize:iF};class ij{constructor(t=0,e=0){ij.prototype.isVector2=!0,this.x=t,this.y=e}get width(){return this.x}set width(t){this.x=t}get height(){return this.y}set height(t){this.y=t}set(t,e){return this.x=t,this.y=e,this}setScalar(t){return this.x=t,this.y=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;default:throw Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;default:throw Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y)}copy(t){return this.x=t.x,this.y=t.y,this}add(t){return this.x+=t.x,this.y+=t.y,this}addScalar(t){return this.x+=t,this.y+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this}subScalar(t){return this.x-=t,this.y-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this}multiply(t){return this.x*=t.x,this.y*=t.y,this}multiplyScalar(t){return this.x*=t,this.y*=t,this}divide(t){return this.x/=t.x,this.y/=t.y,this}divideScalar(t){return this.multiplyScalar(1/t)}applyMatrix3(t){let e=this.x,i=this.y,s=t.elements;return this.x=s[0]*e+s[3]*i+s[6],this.y=s[1]*e+s[4]*i+s[7],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this}clamp(t,e){return this.x=iP(this.x,t.x,e.x),this.y=iP(this.y,t.y,e.y),this}clampScalar(t,e){return this.x=iP(this.x,t,e),this.y=iP(this.y,t,e),this}clampLength(t,e){let i=this.length();return this.divideScalar(i||1).multiplyScalar(iP(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this}negate(){return this.x=-this.x,this.y=-this.y,this}dot(t){return this.x*t.x+this.y*t.y}cross(t){return this.x*t.y-this.y*t.x}lengthSq(){return this.x*this.x+this.y*this.y}length(){return Math.sqrt(this.x*this.x+this.y*this.y)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)}normalize(){return this.divideScalar(this.length()||1)}angle(){return Math.atan2(-this.y,-this.x)+Math.PI}angleTo(t){let e=Math.sqrt(this.lengthSq()*t.lengthSq());return 0===e?Math.PI/2:Math.acos(iP(this.dot(t)/e,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){let e=this.x-t.x,i=this.y-t.y;return e*e+i*i}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this}equals(t){return t.x===this.x&&t.y===this.y}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this}rotateAround(t,e){let i=Math.cos(e),s=Math.sin(e),r=this.x-t.x,n=this.y-t.y;return this.x=r*i-n*s+t.x,this.y=r*s+n*i+t.y,this}random(){return this.x=Math.random(),this.y=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y}}class iU{constructor(t,e,i,s,r,n,a,o,h){iU.prototype.isMatrix3=!0,this.elements=[1,0,0,0,1,0,0,0,1],void 0!==t&&this.set(t,e,i,s,r,n,a,o,h)}set(t,e,i,s,r,n,a,o,h){let l=this.elements;return l[0]=t,l[1]=s,l[2]=a,l[3]=e,l[4]=r,l[5]=o,l[6]=i,l[7]=n,l[8]=h,this}identity(){return this.set(1,0,0,0,1,0,0,0,1),this}copy(t){let e=this.elements,i=t.elements;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],this}extractBasis(t,e,i){return t.setFromMatrix3Column(this,0),e.setFromMatrix3Column(this,1),i.setFromMatrix3Column(this,2),this}setFromMatrix4(t){let e=t.elements;return this.set(e[0],e[4],e[8],e[1],e[5],e[9],e[2],e[6],e[10]),this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){let i=t.elements,s=e.elements,r=this.elements,n=i[0],a=i[3],o=i[6],h=i[1],l=i[4],u=i[7],c=i[2],d=i[5],p=i[8],m=s[0],y=s[3],f=s[6],g=s[1],x=s[4],b=s[7],v=s[2],w=s[5],M=s[8];return r[0]=n*m+a*g+o*v,r[3]=n*y+a*x+o*w,r[6]=n*f+a*b+o*M,r[1]=h*m+l*g+u*v,r[4]=h*y+l*x+u*w,r[7]=h*f+l*b+u*M,r[2]=c*m+d*g+p*v,r[5]=c*y+d*x+p*w,r[8]=c*f+d*b+p*M,this}multiplyScalar(t){let e=this.elements;return e[0]*=t,e[3]*=t,e[6]*=t,e[1]*=t,e[4]*=t,e[7]*=t,e[2]*=t,e[5]*=t,e[8]*=t,this}determinant(){let t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8];return e*n*l-e*a*h-i*r*l+i*a*o+s*r*h-s*n*o}invert(){let t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],u=l*n-a*h,c=a*o-l*r,d=h*r-n*o,p=e*u+i*c+s*d;if(0===p)return this.set(0,0,0,0,0,0,0,0,0);let m=1/p;return t[0]=u*m,t[1]=(s*h-l*i)*m,t[2]=(a*i-s*n)*m,t[3]=c*m,t[4]=(l*e-s*o)*m,t[5]=(s*r-a*e)*m,t[6]=d*m,t[7]=(i*o-h*e)*m,t[8]=(n*e-i*r)*m,this}transpose(){let t,e=this.elements;return t=e[1],e[1]=e[3],e[3]=t,t=e[2],e[2]=e[6],e[6]=t,t=e[5],e[5]=e[7],e[7]=t,this}getNormalMatrix(t){return this.setFromMatrix4(t).invert().transpose()}transposeIntoArray(t){let e=this.elements;return t[0]=e[0],t[1]=e[3],t[2]=e[6],t[3]=e[1],t[4]=e[4],t[5]=e[7],t[6]=e[2],t[7]=e[5],t[8]=e[8],this}setUvTransform(t,e,i,s,r,n,a){let o=Math.cos(r),h=Math.sin(r);return this.set(i*o,i*h,-i*(o*n+h*a)+n+t,-s*h,s*o,-s*(-h*n+o*a)+a+e,0,0,1),this}scale(t,e){return this.premultiply(iW.makeScale(t,e)),this}rotate(t){return this.premultiply(iW.makeRotation(-t)),this}translate(t,e){return this.premultiply(iW.makeTranslation(t,e)),this}makeTranslation(t,e){return t.isVector2?this.set(1,0,t.x,0,1,t.y,0,0,1):this.set(1,0,t,0,1,e,0,0,1),this}makeRotation(t){let e=Math.cos(t),i=Math.sin(t);return this.set(e,-i,0,i,e,0,0,0,1),this}makeScale(t,e){return this.set(t,0,0,0,e,0,0,0,1),this}equals(t){let e=this.elements,i=t.elements;for(let t=0;t<9;t++)if(e[t]!==i[t])return!1;return!0}fromArray(t,e=0){for(let i=0;i<9;i++)this.elements[i]=t[i+e];return this}toArray(t=[],e=0){let i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t}clone(){return new this.constructor().fromArray(this.elements)}}let iW=new iU;function iD(t){for(let e=t.length-1;e>=0;--e)if(t[e]>=65535)return!0;return!1}let iH={Int8Array:Int8Array,Uint8Array:Uint8Array,Uint8ClampedArray:Uint8ClampedArray,Int16Array:Int16Array,Uint16Array:Uint16Array,Int32Array:Int32Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array};function iq(t,e){return new iH[t](e)}function iJ(t){return document.createElementNS("http://www.w3.org/1999/xhtml",t)}function iX(){let t=iJ("canvas");return t.style.display="block",t}let iZ={};function iY(t){t in iZ||(iZ[t]=!0,console.warn(t))}function iG(t,e,i){return new Promise(function(s,r){setTimeout(function n(){switch(t.clientWaitSync(e,t.SYNC_FLUSH_COMMANDS_BIT,0)){case t.WAIT_FAILED:r();break;case t.TIMEOUT_EXPIRED:setTimeout(n,i);break;default:s()}},i)})}function i$(t){let e=t.elements;e[2]=.5*e[2]+.5*e[3],e[6]=.5*e[6]+.5*e[7],e[10]=.5*e[10]+.5*e[11],e[14]=.5*e[14]+.5*e[15]}function iQ(t){let e=t.elements;-1===e[11]?(e[10]=-e[10]-1,e[14]=-e[14]):(e[10]=-e[10],e[14]=-e[14]+1)}let iK=new iU().set(.4123908,.3575843,.1804808,.212639,.7151687,.0721923,.0193308,.1191948,.9505322),i0=new iU().set(3.2409699,-1.5373832,-.4986108,-.9692436,1.8759675,.0415551,.0556301,-.203977,1.0569715),i1=function(){let t={enabled:!0,workingColorSpace:eY,spaces:{},convert:function(t,e,i){return!1!==this.enabled&&e!==i&&e&&i&&(this.spaces[e].transfer===e$&&(t.r=i2(t.r),t.g=i2(t.g),t.b=i2(t.b)),this.spaces[e].primaries!==this.spaces[i].primaries&&(t.applyMatrix3(this.spaces[e].toXYZ),t.applyMatrix3(this.spaces[i].fromXYZ)),this.spaces[i].transfer===e$&&(t.r=i3(t.r),t.g=i3(t.g),t.b=i3(t.b))),t},fromWorkingColorSpace:function(t,e){return this.convert(t,this.workingColorSpace,e)},toWorkingColorSpace:function(t,e){return this.convert(t,e,this.workingColorSpace)},getPrimaries:function(t){return this.spaces[t].primaries},getTransfer:function(t){return t===eX?eG:this.spaces[t].transfer},getLuminanceCoefficients:function(t,e=this.workingColorSpace){return t.fromArray(this.spaces[e].luminanceCoefficients)},define:function(t){Object.assign(this.spaces,t)},_getMatrix:function(t,e,i){return t.copy(this.spaces[e].toXYZ).multiply(this.spaces[i].fromXYZ)},_getDrawingBufferColorSpace:function(t){return this.spaces[t].outputColorSpaceConfig.drawingBufferColorSpace},_getUnpackColorSpace:function(t=this.workingColorSpace){return this.spaces[t].workingColorSpaceConfig.unpackColorSpace}},e=[.64,.33,.3,.6,.15,.06],i=[.2126,.7152,.0722],s=[.3127,.329];return t.define({[eY]:{primaries:e,whitePoint:s,transfer:eG,toXYZ:iK,fromXYZ:i0,luminanceCoefficients:i,workingColorSpaceConfig:{unpackColorSpace:eZ},outputColorSpaceConfig:{drawingBufferColorSpace:eZ}},[eZ]:{primaries:e,whitePoint:s,transfer:e$,toXYZ:iK,fromXYZ:i0,luminanceCoefficients:i,outputColorSpaceConfig:{drawingBufferColorSpace:eZ}}}),t}();function i2(t){return t<.04045?.0773993808*t:Math.pow(.9478672986*t+.0521327014,2.4)}function i3(t){return t<.0031308?12.92*t:1.055*Math.pow(t,.41666)-.055}class i5{static getDataURL(t,e="image/png"){let i;if(/^data:/i.test(t.src)||"undefined"==typeof HTMLCanvasElement)return t.src;if(t instanceof HTMLCanvasElement)i=t;else{void 0===s&&(s=iJ("canvas")),s.width=t.width,s.height=t.height;let e=s.getContext("2d");t instanceof ImageData?e.putImageData(t,0,0):e.drawImage(t,0,0,t.width,t.height),i=s}return i.toDataURL(e)}static sRGBToLinear(t){if("undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap){let e=iJ("canvas");e.width=t.width,e.height=t.height;let i=e.getContext("2d");i.drawImage(t,0,0,t.width,t.height);let s=i.getImageData(0,0,t.width,t.height),r=s.data;for(let t=0;t<r.length;t++)r[t]=255*i2(r[t]/255);return i.putImageData(s,0,0),e}if(!t.data)return console.warn("THREE.ImageUtils.sRGBToLinear(): Unsupported image type. No color space conversion applied."),t;{let e=t.data.slice(0);for(let t=0;t<e.length;t++)e instanceof Uint8Array||e instanceof Uint8ClampedArray?e[t]=Math.floor(255*i2(e[t]/255)):e[t]=i2(e[t]);return{data:e,width:t.width,height:t.height}}}}let i4=0;class i6{constructor(t=null){this.isSource=!0,Object.defineProperty(this,"id",{value:i4++}),this.uuid=iE(),this.data=t,this.dataReady=!0,this.version=0}set needsUpdate(t){!0===t&&this.version++}toJSON(t){let e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.images[this.uuid])return t.images[this.uuid];let i={uuid:this.uuid,url:""},s=this.data;if(null!==s){let t;if(Array.isArray(s)){t=[];for(let e=0,i=s.length;e<i;e++)s[e].isDataTexture?t.push(i8(s[e].image)):t.push(i8(s[e]))}else t=i8(s);i.url=t}return e||(t.images[this.uuid]=i),i}}function i8(t){return"undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap?i5.getDataURL(t):t.data?{data:Array.from(t.data),width:t.width,height:t.height,type:t.data.constructor.name}:(console.warn("THREE.Texture: Unable to serialize Texture."),{})}let i9=0;class i7 extends iI{constructor(t=i7.DEFAULT_IMAGE,e=i7.DEFAULT_MAPPING,i=tM,s=tM,r=tC,n=tR,a=tZ,o=tP,h=i7.DEFAULT_ANISOTROPY,l=eX){super(),this.isTexture=!0,Object.defineProperty(this,"id",{value:i9++}),this.uuid=iE(),this.name="",this.source=new i6(t),this.mipmaps=[],this.mapping=e,this.channel=0,this.wrapS=i,this.wrapT=s,this.magFilter=r,this.minFilter=n,this.anisotropy=h,this.format=a,this.internalFormat=null,this.type=o,this.offset=new ij(0,0),this.repeat=new ij(1,1),this.center=new ij(0,0),this.rotation=0,this.matrixAutoUpdate=!0,this.matrix=new iU,this.generateMipmaps=!0,this.premultiplyAlpha=!1,this.flipY=!0,this.unpackAlignment=4,this.colorSpace=l,this.userData={},this.version=0,this.onUpdate=null,this.renderTarget=null,this.isRenderTargetTexture=!1,this.pmremVersion=0}get image(){return this.source.data}set image(t=null){this.source.data=t}updateMatrix(){this.matrix.setUvTransform(this.offset.x,this.offset.y,this.repeat.x,this.repeat.y,this.rotation,this.center.x,this.center.y)}clone(){return new this.constructor().copy(this)}copy(t){return this.name=t.name,this.source=t.source,this.mipmaps=t.mipmaps.slice(0),this.mapping=t.mapping,this.channel=t.channel,this.wrapS=t.wrapS,this.wrapT=t.wrapT,this.magFilter=t.magFilter,this.minFilter=t.minFilter,this.anisotropy=t.anisotropy,this.format=t.format,this.internalFormat=t.internalFormat,this.type=t.type,this.offset.copy(t.offset),this.repeat.copy(t.repeat),this.center.copy(t.center),this.rotation=t.rotation,this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrix.copy(t.matrix),this.generateMipmaps=t.generateMipmaps,this.premultiplyAlpha=t.premultiplyAlpha,this.flipY=t.flipY,this.unpackAlignment=t.unpackAlignment,this.colorSpace=t.colorSpace,this.renderTarget=t.renderTarget,this.isRenderTargetTexture=t.isRenderTargetTexture,this.userData=JSON.parse(JSON.stringify(t.userData)),this.needsUpdate=!0,this}toJSON(t){let e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.textures[this.uuid])return t.textures[this.uuid];let i={metadata:{version:4.6,type:"Texture",generator:"Texture.toJSON"},uuid:this.uuid,name:this.name,image:this.source.toJSON(t).uuid,mapping:this.mapping,channel:this.channel,repeat:[this.repeat.x,this.repeat.y],offset:[this.offset.x,this.offset.y],center:[this.center.x,this.center.y],rotation:this.rotation,wrap:[this.wrapS,this.wrapT],format:this.format,internalFormat:this.internalFormat,type:this.type,colorSpace:this.colorSpace,minFilter:this.minFilter,magFilter:this.magFilter,anisotropy:this.anisotropy,flipY:this.flipY,generateMipmaps:this.generateMipmaps,premultiplyAlpha:this.premultiplyAlpha,unpackAlignment:this.unpackAlignment};return Object.keys(this.userData).length>0&&(i.userData=this.userData),e||(t.textures[this.uuid]=i),i}dispose(){this.dispatchEvent({type:"dispose"})}transformUv(t){if(this.mapping!==ty)return t;if(t.applyMatrix3(this.matrix),t.x<0||t.x>1)switch(this.wrapS){case tw:t.x=t.x-Math.floor(t.x);break;case tM:t.x=t.x<0?0:1;break;case tS:1===Math.abs(Math.floor(t.x)%2)?t.x=Math.ceil(t.x)-t.x:t.x=t.x-Math.floor(t.x)}if(t.y<0||t.y>1)switch(this.wrapT){case tw:t.y=t.y-Math.floor(t.y);break;case tM:t.y=t.y<0?0:1;break;case tS:1===Math.abs(Math.floor(t.y)%2)?t.y=Math.ceil(t.y)-t.y:t.y=t.y-Math.floor(t.y)}return this.flipY&&(t.y=1-t.y),t}set needsUpdate(t){!0===t&&(this.version++,this.source.needsUpdate=!0)}set needsPMREMUpdate(t){!0===t&&this.pmremVersion++}}i7.DEFAULT_IMAGE=null,i7.DEFAULT_MAPPING=ty,i7.DEFAULT_ANISOTROPY=1;class st{constructor(t=0,e=0,i=0,s=1){st.prototype.isVector4=!0,this.x=t,this.y=e,this.z=i,this.w=s}get width(){return this.z}set width(t){this.z=t}get height(){return this.w}set height(t){this.w=t}set(t,e,i,s){return this.x=t,this.y=e,this.z=i,this.w=s,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this.w=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setW(t){return this.w=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;case 3:this.w=e;break;default:throw Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;case 3:return this.w;default:throw Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z,this.w)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this.w=void 0!==t.w?t.w:1,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this.w+=t.w,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this.w+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this.w=t.w+e.w,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this.w+=t.w*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this.w-=t.w,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this.w-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this.w=t.w-e.w,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this.w*=t.w,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this.w*=t,this}applyMatrix4(t){let e=this.x,i=this.y,s=this.z,r=this.w,n=t.elements;return this.x=n[0]*e+n[4]*i+n[8]*s+n[12]*r,this.y=n[1]*e+n[5]*i+n[9]*s+n[13]*r,this.z=n[2]*e+n[6]*i+n[10]*s+n[14]*r,this.w=n[3]*e+n[7]*i+n[11]*s+n[15]*r,this}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this.w/=t.w,this}divideScalar(t){return this.multiplyScalar(1/t)}setAxisAngleFromQuaternion(t){this.w=2*Math.acos(t.w);let e=Math.sqrt(1-t.w*t.w);return e<1e-4?(this.x=1,this.y=0,this.z=0):(this.x=t.x/e,this.y=t.y/e,this.z=t.z/e),this}setAxisAngleFromRotationMatrix(t){let e,i,s,r,n=t.elements,a=n[0],o=n[4],h=n[8],l=n[1],u=n[5],c=n[9],d=n[2],p=n[6],m=n[10];if(.01>Math.abs(o-l)&&.01>Math.abs(h-d)&&.01>Math.abs(c-p)){if(.1>Math.abs(o+l)&&.1>Math.abs(h+d)&&.1>Math.abs(c+p)&&.1>Math.abs(a+u+m-3))return this.set(1,0,0,0),this;e=Math.PI;let t=(a+1)/2,n=(u+1)/2,y=(m+1)/2,f=(o+l)/4,g=(h+d)/4,x=(c+p)/4;return t>n&&t>y?t<.01?(i=0,s=.*********,r=.*********):(s=f/(i=Math.sqrt(t)),r=g/i):n>y?n<.01?(i=.*********,s=0,r=.*********):(i=f/(s=Math.sqrt(n)),r=x/s):y<.01?(i=.*********,s=.*********,r=0):(i=g/(r=Math.sqrt(y)),s=x/r),this.set(i,s,r,e),this}let y=Math.sqrt((p-c)*(p-c)+(h-d)*(h-d)+(l-o)*(l-o));return .001>Math.abs(y)&&(y=1),this.x=(p-c)/y,this.y=(h-d)/y,this.z=(l-o)/y,this.w=Math.acos((a+u+m-1)/2),this}setFromMatrixPosition(t){let e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this.w=e[15],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this.w=Math.min(this.w,t.w),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this.w=Math.max(this.w,t.w),this}clamp(t,e){return this.x=iP(this.x,t.x,e.x),this.y=iP(this.y,t.y,e.y),this.z=iP(this.z,t.z,e.z),this.w=iP(this.w,t.w,e.w),this}clampScalar(t,e){return this.x=iP(this.x,t,e),this.y=iP(this.y,t,e),this.z=iP(this.z,t,e),this.w=iP(this.w,t,e),this}clampLength(t,e){let i=this.length();return this.divideScalar(i||1).multiplyScalar(iP(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this.w=Math.floor(this.w),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this.w=Math.ceil(this.w),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this.w=Math.round(this.w),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this.w=Math.trunc(this.w),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)+Math.abs(this.w)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this.w+=(t.w-this.w)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this.z=t.z+(e.z-t.z)*i,this.w=t.w+(e.w-t.w)*i,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z&&t.w===this.w}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this.w=t[e+3],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t[e+3]=this.w,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this.w=t.getW(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this.w=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z,yield this.w}}class se extends iI{constructor(t=1,e=1,i={}){super(),this.isRenderTarget=!0,this.width=t,this.height=e,this.depth=1,this.scissor=new st(0,0,t,e),this.scissorTest=!1,this.viewport=new st(0,0,t,e);let s=new i7({width:t,height:e,depth:1},(i=Object.assign({generateMipmaps:!1,internalFormat:null,minFilter:tC,depthBuffer:!0,stencilBuffer:!1,resolveDepthBuffer:!0,resolveStencilBuffer:!0,depthTexture:null,samples:0,count:1},i)).mapping,i.wrapS,i.wrapT,i.magFilter,i.minFilter,i.format,i.type,i.anisotropy,i.colorSpace);s.flipY=!1,s.generateMipmaps=i.generateMipmaps,s.internalFormat=i.internalFormat,this.textures=[];let r=i.count;for(let t=0;t<r;t++)this.textures[t]=s.clone(),this.textures[t].isRenderTargetTexture=!0,this.textures[t].renderTarget=this;this.depthBuffer=i.depthBuffer,this.stencilBuffer=i.stencilBuffer,this.resolveDepthBuffer=i.resolveDepthBuffer,this.resolveStencilBuffer=i.resolveStencilBuffer,this._depthTexture=i.depthTexture,this.samples=i.samples}get texture(){return this.textures[0]}set texture(t){this.textures[0]=t}set depthTexture(t){null!==this._depthTexture&&(this._depthTexture.renderTarget=null),null!==t&&(t.renderTarget=this),this._depthTexture=t}get depthTexture(){return this._depthTexture}setSize(t,e,i=1){if(this.width!==t||this.height!==e||this.depth!==i){this.width=t,this.height=e,this.depth=i;for(let s=0,r=this.textures.length;s<r;s++)this.textures[s].image.width=t,this.textures[s].image.height=e,this.textures[s].image.depth=i;this.dispose()}this.viewport.set(0,0,t,e),this.scissor.set(0,0,t,e)}clone(){return new this.constructor().copy(this)}copy(t){this.width=t.width,this.height=t.height,this.depth=t.depth,this.scissor.copy(t.scissor),this.scissorTest=t.scissorTest,this.viewport.copy(t.viewport),this.textures.length=0;for(let e=0,i=t.textures.length;e<i;e++){this.textures[e]=t.textures[e].clone(),this.textures[e].isRenderTargetTexture=!0,this.textures[e].renderTarget=this;let i=Object.assign({},t.textures[e].image);this.textures[e].source=new i6(i)}return this.depthBuffer=t.depthBuffer,this.stencilBuffer=t.stencilBuffer,this.resolveDepthBuffer=t.resolveDepthBuffer,this.resolveStencilBuffer=t.resolveStencilBuffer,null!==t.depthTexture&&(this.depthTexture=t.depthTexture.clone()),this.samples=t.samples,this}dispose(){this.dispatchEvent({type:"dispose"})}}class si extends se{constructor(t=1,e=1,i={}){super(t,e,i),this.isWebGLRenderTarget=!0}}class ss extends i7{constructor(t=null,e=1,i=1,s=1){super(null),this.isDataArrayTexture=!0,this.image={data:t,width:e,height:i,depth:s},this.magFilter=t_,this.minFilter=t_,this.wrapR=tM,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1,this.layerUpdates=new Set}addLayerUpdate(t){this.layerUpdates.add(t)}clearLayerUpdates(){this.layerUpdates.clear()}}class sr extends si{constructor(t=1,e=1,i=1,s={}){super(t,e,s),this.isWebGLArrayRenderTarget=!0,this.depth=i,this.texture=new ss(null,t,e,i),this.texture.isRenderTargetTexture=!0}}class sn extends i7{constructor(t=null,e=1,i=1,s=1){super(null),this.isData3DTexture=!0,this.image={data:t,width:e,height:i,depth:s},this.magFilter=t_,this.minFilter=t_,this.wrapR=tM,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}class sa extends si{constructor(t=1,e=1,i=1,s={}){super(t,e,s),this.isWebGL3DRenderTarget=!0,this.depth=i,this.texture=new sn(null,t,e,i),this.texture.isRenderTargetTexture=!0}}class so{constructor(t=0,e=0,i=0,s=1){this.isQuaternion=!0,this._x=t,this._y=e,this._z=i,this._w=s}static slerpFlat(t,e,i,s,r,n,a){let o=i[s+0],h=i[s+1],l=i[s+2],u=i[s+3],c=r[n+0],d=r[n+1],p=r[n+2],m=r[n+3];if(0===a){t[e+0]=o,t[e+1]=h,t[e+2]=l,t[e+3]=u;return}if(1===a){t[e+0]=c,t[e+1]=d,t[e+2]=p,t[e+3]=m;return}if(u!==m||o!==c||h!==d||l!==p){let t=1-a,e=o*c+h*d+l*p+u*m,i=e>=0?1:-1,s=1-e*e;if(s>Number.EPSILON){let r=Math.sqrt(s),n=Math.atan2(r,e*i);t=Math.sin(t*n)/r,a=Math.sin(a*n)/r}let r=a*i;if(o=o*t+c*r,h=h*t+d*r,l=l*t+p*r,u=u*t+m*r,t===1-a){let t=1/Math.sqrt(o*o+h*h+l*l+u*u);o*=t,h*=t,l*=t,u*=t}}t[e]=o,t[e+1]=h,t[e+2]=l,t[e+3]=u}static multiplyQuaternionsFlat(t,e,i,s,r,n){let a=i[s],o=i[s+1],h=i[s+2],l=i[s+3],u=r[n],c=r[n+1],d=r[n+2],p=r[n+3];return t[e]=a*p+l*u+o*d-h*c,t[e+1]=o*p+l*c+h*u-a*d,t[e+2]=h*p+l*d+a*c-o*u,t[e+3]=l*p-a*u-o*c-h*d,t}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get w(){return this._w}set w(t){this._w=t,this._onChangeCallback()}set(t,e,i,s){return this._x=t,this._y=e,this._z=i,this._w=s,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._w)}copy(t){return this._x=t.x,this._y=t.y,this._z=t.z,this._w=t.w,this._onChangeCallback(),this}setFromEuler(t,e=!0){let i=t._x,s=t._y,r=t._z,n=t._order,a=Math.cos,o=Math.sin,h=a(i/2),l=a(s/2),u=a(r/2),c=o(i/2),d=o(s/2),p=o(r/2);switch(n){case"XYZ":this._x=c*l*u+h*d*p,this._y=h*d*u-c*l*p,this._z=h*l*p+c*d*u,this._w=h*l*u-c*d*p;break;case"YXZ":this._x=c*l*u+h*d*p,this._y=h*d*u-c*l*p,this._z=h*l*p-c*d*u,this._w=h*l*u+c*d*p;break;case"ZXY":this._x=c*l*u-h*d*p,this._y=h*d*u+c*l*p,this._z=h*l*p+c*d*u,this._w=h*l*u-c*d*p;break;case"ZYX":this._x=c*l*u-h*d*p,this._y=h*d*u+c*l*p,this._z=h*l*p-c*d*u,this._w=h*l*u+c*d*p;break;case"YZX":this._x=c*l*u+h*d*p,this._y=h*d*u+c*l*p,this._z=h*l*p-c*d*u,this._w=h*l*u-c*d*p;break;case"XZY":this._x=c*l*u-h*d*p,this._y=h*d*u-c*l*p,this._z=h*l*p+c*d*u,this._w=h*l*u+c*d*p;break;default:console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: "+n)}return!0===e&&this._onChangeCallback(),this}setFromAxisAngle(t,e){let i=e/2,s=Math.sin(i);return this._x=t.x*s,this._y=t.y*s,this._z=t.z*s,this._w=Math.cos(i),this._onChangeCallback(),this}setFromRotationMatrix(t){let e=t.elements,i=e[0],s=e[4],r=e[8],n=e[1],a=e[5],o=e[9],h=e[2],l=e[6],u=e[10],c=i+a+u;if(c>0){let t=.5/Math.sqrt(c+1);this._w=.25/t,this._x=(l-o)*t,this._y=(r-h)*t,this._z=(n-s)*t}else if(i>a&&i>u){let t=2*Math.sqrt(1+i-a-u);this._w=(l-o)/t,this._x=.25*t,this._y=(s+n)/t,this._z=(r+h)/t}else if(a>u){let t=2*Math.sqrt(1+a-i-u);this._w=(r-h)/t,this._x=(s+n)/t,this._y=.25*t,this._z=(o+l)/t}else{let t=2*Math.sqrt(1+u-i-a);this._w=(n-s)/t,this._x=(r+h)/t,this._y=(o+l)/t,this._z=.25*t}return this._onChangeCallback(),this}setFromUnitVectors(t,e){let i=t.dot(e)+1;return i<Number.EPSILON?(i=0,Math.abs(t.x)>Math.abs(t.z)?(this._x=-t.y,this._y=t.x,this._z=0):(this._x=0,this._y=-t.z,this._z=t.y)):(this._x=t.y*e.z-t.z*e.y,this._y=t.z*e.x-t.x*e.z,this._z=t.x*e.y-t.y*e.x),this._w=i,this.normalize()}angleTo(t){return 2*Math.acos(Math.abs(iP(this.dot(t),-1,1)))}rotateTowards(t,e){let i=this.angleTo(t);if(0===i)return this;let s=Math.min(1,e/i);return this.slerp(t,s),this}identity(){return this.set(0,0,0,1)}invert(){return this.conjugate()}conjugate(){return this._x*=-1,this._y*=-1,this._z*=-1,this._onChangeCallback(),this}dot(t){return this._x*t._x+this._y*t._y+this._z*t._z+this._w*t._w}lengthSq(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w}length(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)}normalize(){let t=this.length();return 0===t?(this._x=0,this._y=0,this._z=0,this._w=1):(t=1/t,this._x=this._x*t,this._y=this._y*t,this._z=this._z*t,this._w=this._w*t),this._onChangeCallback(),this}multiply(t){return this.multiplyQuaternions(this,t)}premultiply(t){return this.multiplyQuaternions(t,this)}multiplyQuaternions(t,e){let i=t._x,s=t._y,r=t._z,n=t._w,a=e._x,o=e._y,h=e._z,l=e._w;return this._x=i*l+n*a+s*h-r*o,this._y=s*l+n*o+r*a-i*h,this._z=r*l+n*h+i*o-s*a,this._w=n*l-i*a-s*o-r*h,this._onChangeCallback(),this}slerp(t,e){if(0===e)return this;if(1===e)return this.copy(t);let i=this._x,s=this._y,r=this._z,n=this._w,a=n*t._w+i*t._x+s*t._y+r*t._z;if(a<0?(this._w=-t._w,this._x=-t._x,this._y=-t._y,this._z=-t._z,a=-a):this.copy(t),a>=1)return this._w=n,this._x=i,this._y=s,this._z=r,this;let o=1-a*a;if(o<=Number.EPSILON){let t=1-e;return this._w=t*n+e*this._w,this._x=t*i+e*this._x,this._y=t*s+e*this._y,this._z=t*r+e*this._z,this.normalize(),this}let h=Math.sqrt(o),l=Math.atan2(h,a),u=Math.sin((1-e)*l)/h,c=Math.sin(e*l)/h;return this._w=n*u+this._w*c,this._x=i*u+this._x*c,this._y=s*u+this._y*c,this._z=r*u+this._z*c,this._onChangeCallback(),this}slerpQuaternions(t,e,i){return this.copy(t).slerp(e,i)}random(){let t=2*Math.PI*Math.random(),e=2*Math.PI*Math.random(),i=Math.random(),s=Math.sqrt(1-i),r=Math.sqrt(i);return this.set(s*Math.sin(t),s*Math.cos(t),r*Math.sin(e),r*Math.cos(e))}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._w===this._w}fromArray(t,e=0){return this._x=t[e],this._y=t[e+1],this._z=t[e+2],this._w=t[e+3],this._onChangeCallback(),this}toArray(t=[],e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._w,t}fromBufferAttribute(t,e){return this._x=t.getX(e),this._y=t.getY(e),this._z=t.getZ(e),this._w=t.getW(e),this._onChangeCallback(),this}toJSON(){return this.toArray()}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._w}}class sh{constructor(t=0,e=0,i=0){sh.prototype.isVector3=!0,this.x=t,this.y=e,this.z=i}set(t,e,i){return void 0===i&&(i=this.z),this.x=t,this.y=e,this.z=i,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;default:throw Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this}multiplyVectors(t,e){return this.x=t.x*e.x,this.y=t.y*e.y,this.z=t.z*e.z,this}applyEuler(t){return this.applyQuaternion(su.setFromEuler(t))}applyAxisAngle(t,e){return this.applyQuaternion(su.setFromAxisAngle(t,e))}applyMatrix3(t){let e=this.x,i=this.y,s=this.z,r=t.elements;return this.x=r[0]*e+r[3]*i+r[6]*s,this.y=r[1]*e+r[4]*i+r[7]*s,this.z=r[2]*e+r[5]*i+r[8]*s,this}applyNormalMatrix(t){return this.applyMatrix3(t).normalize()}applyMatrix4(t){let e=this.x,i=this.y,s=this.z,r=t.elements,n=1/(r[3]*e+r[7]*i+r[11]*s+r[15]);return this.x=(r[0]*e+r[4]*i+r[8]*s+r[12])*n,this.y=(r[1]*e+r[5]*i+r[9]*s+r[13])*n,this.z=(r[2]*e+r[6]*i+r[10]*s+r[14])*n,this}applyQuaternion(t){let e=this.x,i=this.y,s=this.z,r=t.x,n=t.y,a=t.z,o=t.w,h=2*(n*s-a*i),l=2*(a*e-r*s),u=2*(r*i-n*e);return this.x=e+o*h+n*u-a*l,this.y=i+o*l+a*h-r*u,this.z=s+o*u+r*l-n*h,this}project(t){return this.applyMatrix4(t.matrixWorldInverse).applyMatrix4(t.projectionMatrix)}unproject(t){return this.applyMatrix4(t.projectionMatrixInverse).applyMatrix4(t.matrixWorld)}transformDirection(t){let e=this.x,i=this.y,s=this.z,r=t.elements;return this.x=r[0]*e+r[4]*i+r[8]*s,this.y=r[1]*e+r[5]*i+r[9]*s,this.z=r[2]*e+r[6]*i+r[10]*s,this.normalize()}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this}divideScalar(t){return this.multiplyScalar(1/t)}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this}clamp(t,e){return this.x=iP(this.x,t.x,e.x),this.y=iP(this.y,t.y,e.y),this.z=iP(this.z,t.z,e.z),this}clampScalar(t,e){return this.x=iP(this.x,t,e),this.y=iP(this.y,t,e),this.z=iP(this.z,t,e),this}clampLength(t,e){let i=this.length();return this.divideScalar(i||1).multiplyScalar(iP(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this.z=t.z+(e.z-t.z)*i,this}cross(t){return this.crossVectors(this,t)}crossVectors(t,e){let i=t.x,s=t.y,r=t.z,n=e.x,a=e.y,o=e.z;return this.x=s*o-r*a,this.y=r*n-i*o,this.z=i*a-s*n,this}projectOnVector(t){let e=t.lengthSq();if(0===e)return this.set(0,0,0);let i=t.dot(this)/e;return this.copy(t).multiplyScalar(i)}projectOnPlane(t){return sl.copy(this).projectOnVector(t),this.sub(sl)}reflect(t){return this.sub(sl.copy(t).multiplyScalar(2*this.dot(t)))}angleTo(t){let e=Math.sqrt(this.lengthSq()*t.lengthSq());return 0===e?Math.PI/2:Math.acos(iP(this.dot(t)/e,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){let e=this.x-t.x,i=this.y-t.y,s=this.z-t.z;return e*e+i*i+s*s}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)+Math.abs(this.z-t.z)}setFromSpherical(t){return this.setFromSphericalCoords(t.radius,t.phi,t.theta)}setFromSphericalCoords(t,e,i){let s=Math.sin(e)*t;return this.x=s*Math.sin(i),this.y=Math.cos(e)*t,this.z=s*Math.cos(i),this}setFromCylindrical(t){return this.setFromCylindricalCoords(t.radius,t.theta,t.y)}setFromCylindricalCoords(t,e,i){return this.x=t*Math.sin(e),this.y=i,this.z=t*Math.cos(e),this}setFromMatrixPosition(t){let e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this}setFromMatrixScale(t){let e=this.setFromMatrixColumn(t,0).length(),i=this.setFromMatrixColumn(t,1).length(),s=this.setFromMatrixColumn(t,2).length();return this.x=e,this.y=i,this.z=s,this}setFromMatrixColumn(t,e){return this.fromArray(t.elements,4*e)}setFromMatrix3Column(t,e){return this.fromArray(t.elements,3*e)}setFromEuler(t){return this.x=t._x,this.y=t._y,this.z=t._z,this}setFromColor(t){return this.x=t.r,this.y=t.g,this.z=t.b,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this}randomDirection(){let t=Math.random()*Math.PI*2,e=2*Math.random()-1,i=Math.sqrt(1-e*e);return this.x=i*Math.cos(t),this.y=e,this.z=i*Math.sin(t),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z}}let sl=new sh,su=new so;class sc{constructor(t=new sh(Infinity,Infinity,Infinity),e=new sh(-1/0,-1/0,-1/0)){this.isBox3=!0,this.min=t,this.max=e}set(t,e){return this.min.copy(t),this.max.copy(e),this}setFromArray(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e+=3)this.expandByPoint(sp.fromArray(t,e));return this}setFromBufferAttribute(t){this.makeEmpty();for(let e=0,i=t.count;e<i;e++)this.expandByPoint(sp.fromBufferAttribute(t,e));return this}setFromPoints(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e++)this.expandByPoint(t[e]);return this}setFromCenterAndSize(t,e){let i=sp.copy(e).multiplyScalar(.5);return this.min.copy(t).sub(i),this.max.copy(t).add(i),this}setFromObject(t,e=!1){return this.makeEmpty(),this.expandByObject(t,e)}clone(){return new this.constructor().copy(this)}copy(t){return this.min.copy(t.min),this.max.copy(t.max),this}makeEmpty(){return this.min.x=this.min.y=this.min.z=Infinity,this.max.x=this.max.y=this.max.z=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z}getCenter(t){return this.isEmpty()?t.set(0,0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(t){return this.isEmpty()?t.set(0,0,0):t.subVectors(this.max,this.min)}expandByPoint(t){return this.min.min(t),this.max.max(t),this}expandByVector(t){return this.min.sub(t),this.max.add(t),this}expandByScalar(t){return this.min.addScalar(-t),this.max.addScalar(t),this}expandByObject(t,e=!1){t.updateWorldMatrix(!1,!1);let i=t.geometry;if(void 0!==i){let s=i.getAttribute("position");if(!0===e&&void 0!==s&&!0!==t.isInstancedMesh)for(let e=0,i=s.count;e<i;e++)!0===t.isMesh?t.getVertexPosition(e,sp):sp.fromBufferAttribute(s,e),sp.applyMatrix4(t.matrixWorld),this.expandByPoint(sp);else void 0!==t.boundingBox?(null===t.boundingBox&&t.computeBoundingBox(),sm.copy(t.boundingBox)):(null===i.boundingBox&&i.computeBoundingBox(),sm.copy(i.boundingBox)),sm.applyMatrix4(t.matrixWorld),this.union(sm)}let s=t.children;for(let t=0,i=s.length;t<i;t++)this.expandByObject(s[t],e);return this}containsPoint(t){return t.x>=this.min.x&&t.x<=this.max.x&&t.y>=this.min.y&&t.y<=this.max.y&&t.z>=this.min.z&&t.z<=this.max.z}containsBox(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y&&this.min.z<=t.min.z&&t.max.z<=this.max.z}getParameter(t,e){return e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y),(t.z-this.min.z)/(this.max.z-this.min.z))}intersectsBox(t){return t.max.x>=this.min.x&&t.min.x<=this.max.x&&t.max.y>=this.min.y&&t.min.y<=this.max.y&&t.max.z>=this.min.z&&t.min.z<=this.max.z}intersectsSphere(t){return this.clampPoint(t.center,sp),sp.distanceToSquared(t.center)<=t.radius*t.radius}intersectsPlane(t){let e,i;return t.normal.x>0?(e=t.normal.x*this.min.x,i=t.normal.x*this.max.x):(e=t.normal.x*this.max.x,i=t.normal.x*this.min.x),t.normal.y>0?(e+=t.normal.y*this.min.y,i+=t.normal.y*this.max.y):(e+=t.normal.y*this.max.y,i+=t.normal.y*this.min.y),t.normal.z>0?(e+=t.normal.z*this.min.z,i+=t.normal.z*this.max.z):(e+=t.normal.z*this.max.z,i+=t.normal.z*this.min.z),e<=-t.constant&&i>=-t.constant}intersectsTriangle(t){if(this.isEmpty())return!1;this.getCenter(sw),sM.subVectors(this.max,sw),sy.subVectors(t.a,sw),sf.subVectors(t.b,sw),sg.subVectors(t.c,sw),sx.subVectors(sf,sy),sb.subVectors(sg,sf),sv.subVectors(sy,sg);let e=[0,-sx.z,sx.y,0,-sb.z,sb.y,0,-sv.z,sv.y,sx.z,0,-sx.x,sb.z,0,-sb.x,sv.z,0,-sv.x,-sx.y,sx.x,0,-sb.y,sb.x,0,-sv.y,sv.x,0];return!!sA(e,sy,sf,sg,sM)&&!!sA(e=[1,0,0,0,1,0,0,0,1],sy,sf,sg,sM)&&(sS.crossVectors(sx,sb),sA(e=[sS.x,sS.y,sS.z],sy,sf,sg,sM))}clampPoint(t,e){return e.copy(t).clamp(this.min,this.max)}distanceToPoint(t){return this.clampPoint(t,sp).distanceTo(t)}getBoundingSphere(t){return this.isEmpty()?t.makeEmpty():(this.getCenter(t.center),t.radius=.5*this.getSize(sp).length()),t}intersect(t){return this.min.max(t.min),this.max.min(t.max),this.isEmpty()&&this.makeEmpty(),this}union(t){return this.min.min(t.min),this.max.max(t.max),this}applyMatrix4(t){return this.isEmpty()||(sd[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(t),sd[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(t),sd[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(t),sd[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(t),sd[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(t),sd[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(t),sd[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(t),sd[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(t),this.setFromPoints(sd)),this}translate(t){return this.min.add(t),this.max.add(t),this}equals(t){return t.min.equals(this.min)&&t.max.equals(this.max)}}let sd=[new sh,new sh,new sh,new sh,new sh,new sh,new sh,new sh],sp=new sh,sm=new sc,sy=new sh,sf=new sh,sg=new sh,sx=new sh,sb=new sh,sv=new sh,sw=new sh,sM=new sh,sS=new sh,s_=new sh;function sA(t,e,i,s,r){for(let n=0,a=t.length-3;n<=a;n+=3){s_.fromArray(t,n);let a=r.x*Math.abs(s_.x)+r.y*Math.abs(s_.y)+r.z*Math.abs(s_.z),o=e.dot(s_),h=i.dot(s_),l=s.dot(s_);if(Math.max(-Math.max(o,h,l),Math.min(o,h,l))>a)return!1}return!0}let sT=new sc,sz=new sh,sI=new sh;class sC{constructor(t=new sh,e=-1){this.isSphere=!0,this.center=t,this.radius=e}set(t,e){return this.center.copy(t),this.radius=e,this}setFromPoints(t,e){let i=this.center;void 0!==e?i.copy(e):sT.setFromPoints(t).getCenter(i);let s=0;for(let e=0,r=t.length;e<r;e++)s=Math.max(s,i.distanceToSquared(t[e]));return this.radius=Math.sqrt(s),this}copy(t){return this.center.copy(t.center),this.radius=t.radius,this}isEmpty(){return this.radius<0}makeEmpty(){return this.center.set(0,0,0),this.radius=-1,this}containsPoint(t){return t.distanceToSquared(this.center)<=this.radius*this.radius}distanceToPoint(t){return t.distanceTo(this.center)-this.radius}intersectsSphere(t){let e=this.radius+t.radius;return t.center.distanceToSquared(this.center)<=e*e}intersectsBox(t){return t.intersectsSphere(this)}intersectsPlane(t){return Math.abs(t.distanceToPoint(this.center))<=this.radius}clampPoint(t,e){let i=this.center.distanceToSquared(t);return e.copy(t),i>this.radius*this.radius&&(e.sub(this.center).normalize(),e.multiplyScalar(this.radius).add(this.center)),e}getBoundingBox(t){return this.isEmpty()?t.makeEmpty():(t.set(this.center,this.center),t.expandByScalar(this.radius)),t}applyMatrix4(t){return this.center.applyMatrix4(t),this.radius=this.radius*t.getMaxScaleOnAxis(),this}translate(t){return this.center.add(t),this}expandByPoint(t){if(this.isEmpty())return this.center.copy(t),this.radius=0,this;sz.subVectors(t,this.center);let e=sz.lengthSq();if(e>this.radius*this.radius){let t=Math.sqrt(e),i=(t-this.radius)*.5;this.center.addScaledVector(sz,i/t),this.radius+=i}return this}union(t){return t.isEmpty()||(this.isEmpty()?this.copy(t):!0===this.center.equals(t.center)?this.radius=Math.max(this.radius,t.radius):(sI.subVectors(t.center,this.center).setLength(t.radius),this.expandByPoint(sz.copy(t.center).add(sI)),this.expandByPoint(sz.copy(t.center).sub(sI)))),this}equals(t){return t.center.equals(this.center)&&t.radius===this.radius}clone(){return new this.constructor().copy(this)}}let sk=new sh,sB=new sh,sR=new sh,sE=new sh,sP=new sh,sO=new sh,sN=new sh;class sF{constructor(t=new sh,e=new sh(0,0,-1)){this.origin=t,this.direction=e}set(t,e){return this.origin.copy(t),this.direction.copy(e),this}copy(t){return this.origin.copy(t.origin),this.direction.copy(t.direction),this}at(t,e){return e.copy(this.origin).addScaledVector(this.direction,t)}lookAt(t){return this.direction.copy(t).sub(this.origin).normalize(),this}recast(t){return this.origin.copy(this.at(t,sk)),this}closestPointToPoint(t,e){e.subVectors(t,this.origin);let i=e.dot(this.direction);return i<0?e.copy(this.origin):e.copy(this.origin).addScaledVector(this.direction,i)}distanceToPoint(t){return Math.sqrt(this.distanceSqToPoint(t))}distanceSqToPoint(t){let e=sk.subVectors(t,this.origin).dot(this.direction);return e<0?this.origin.distanceToSquared(t):(sk.copy(this.origin).addScaledVector(this.direction,e),sk.distanceToSquared(t))}distanceSqToSegment(t,e,i,s){let r,n,a,o;sB.copy(t).add(e).multiplyScalar(.5),sR.copy(e).sub(t).normalize(),sE.copy(this.origin).sub(sB);let h=.5*t.distanceTo(e),l=-this.direction.dot(sR),u=sE.dot(this.direction),c=-sE.dot(sR),d=sE.lengthSq(),p=Math.abs(1-l*l);if(p>0)if(r=l*c-u,n=l*u-c,o=h*p,r>=0)if(n>=-o)if(n<=o){let t=1/p;r*=t,n*=t,a=r*(r+l*n+2*u)+n*(l*r+n+2*c)+d}else a=-(r=Math.max(0,-(l*(n=h)+u)))*r+n*(n+2*c)+d;else a=-(r=Math.max(0,-(l*(n=-h)+u)))*r+n*(n+2*c)+d;else n<=-o?(n=(r=Math.max(0,-(-l*h+u)))>0?-h:Math.min(Math.max(-h,-c),h),a=-r*r+n*(n+2*c)+d):n<=o?(r=0,a=(n=Math.min(Math.max(-h,-c),h))*(n+2*c)+d):(n=(r=Math.max(0,-(l*h+u)))>0?h:Math.min(Math.max(-h,-c),h),a=-r*r+n*(n+2*c)+d);else n=l>0?-h:h,a=-(r=Math.max(0,-(l*n+u)))*r+n*(n+2*c)+d;return i&&i.copy(this.origin).addScaledVector(this.direction,r),s&&s.copy(sB).addScaledVector(sR,n),a}intersectSphere(t,e){sk.subVectors(t.center,this.origin);let i=sk.dot(this.direction),s=sk.dot(sk)-i*i,r=t.radius*t.radius;if(s>r)return null;let n=Math.sqrt(r-s),a=i-n,o=i+n;return o<0?null:a<0?this.at(o,e):this.at(a,e)}intersectsSphere(t){return this.distanceSqToPoint(t.center)<=t.radius*t.radius}distanceToPlane(t){let e=t.normal.dot(this.direction);if(0===e)return 0===t.distanceToPoint(this.origin)?0:null;let i=-(this.origin.dot(t.normal)+t.constant)/e;return i>=0?i:null}intersectPlane(t,e){let i=this.distanceToPlane(t);return null===i?null:this.at(i,e)}intersectsPlane(t){let e=t.distanceToPoint(this.origin);return!!(0===e||t.normal.dot(this.direction)*e<0)}intersectBox(t,e){let i,s,r,n,a,o,h=1/this.direction.x,l=1/this.direction.y,u=1/this.direction.z,c=this.origin;return(h>=0?(i=(t.min.x-c.x)*h,s=(t.max.x-c.x)*h):(i=(t.max.x-c.x)*h,s=(t.min.x-c.x)*h),l>=0?(r=(t.min.y-c.y)*l,n=(t.max.y-c.y)*l):(r=(t.max.y-c.y)*l,n=(t.min.y-c.y)*l),i>n||r>s||((r>i||isNaN(i))&&(i=r),(n<s||isNaN(s))&&(s=n),u>=0?(a=(t.min.z-c.z)*u,o=(t.max.z-c.z)*u):(a=(t.max.z-c.z)*u,o=(t.min.z-c.z)*u),i>o||a>s||((a>i||i!=i)&&(i=a),(o<s||s!=s)&&(s=o),s<0)))?null:this.at(i>=0?i:s,e)}intersectsBox(t){return null!==this.intersectBox(t,sk)}intersectTriangle(t,e,i,s,r){let n;sP.subVectors(e,t),sO.subVectors(i,t),sN.crossVectors(sP,sO);let a=this.direction.dot(sN);if(a>0){if(s)return null;n=1}else{if(!(a<0))return null;n=-1,a=-a}sE.subVectors(this.origin,t);let o=n*this.direction.dot(sO.crossVectors(sE,sO));if(o<0)return null;let h=n*this.direction.dot(sP.cross(sE));if(h<0||o+h>a)return null;let l=-n*sE.dot(sN);return l<0?null:this.at(l/a,r)}applyMatrix4(t){return this.origin.applyMatrix4(t),this.direction.transformDirection(t),this}equals(t){return t.origin.equals(this.origin)&&t.direction.equals(this.direction)}clone(){return new this.constructor().copy(this)}}class sV{constructor(t,e,i,s,r,n,a,o,h,l,u,c,d,p,m,y){sV.prototype.isMatrix4=!0,this.elements=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],void 0!==t&&this.set(t,e,i,s,r,n,a,o,h,l,u,c,d,p,m,y)}set(t,e,i,s,r,n,a,o,h,l,u,c,d,p,m,y){let f=this.elements;return f[0]=t,f[4]=e,f[8]=i,f[12]=s,f[1]=r,f[5]=n,f[9]=a,f[13]=o,f[2]=h,f[6]=l,f[10]=u,f[14]=c,f[3]=d,f[7]=p,f[11]=m,f[15]=y,this}identity(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this}clone(){return new sV().fromArray(this.elements)}copy(t){let e=this.elements,i=t.elements;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],e[9]=i[9],e[10]=i[10],e[11]=i[11],e[12]=i[12],e[13]=i[13],e[14]=i[14],e[15]=i[15],this}copyPosition(t){let e=this.elements,i=t.elements;return e[12]=i[12],e[13]=i[13],e[14]=i[14],this}setFromMatrix3(t){let e=t.elements;return this.set(e[0],e[3],e[6],0,e[1],e[4],e[7],0,e[2],e[5],e[8],0,0,0,0,1),this}extractBasis(t,e,i){return t.setFromMatrixColumn(this,0),e.setFromMatrixColumn(this,1),i.setFromMatrixColumn(this,2),this}makeBasis(t,e,i){return this.set(t.x,e.x,i.x,0,t.y,e.y,i.y,0,t.z,e.z,i.z,0,0,0,0,1),this}extractRotation(t){let e=this.elements,i=t.elements,s=1/sL.setFromMatrixColumn(t,0).length(),r=1/sL.setFromMatrixColumn(t,1).length(),n=1/sL.setFromMatrixColumn(t,2).length();return e[0]=i[0]*s,e[1]=i[1]*s,e[2]=i[2]*s,e[3]=0,e[4]=i[4]*r,e[5]=i[5]*r,e[6]=i[6]*r,e[7]=0,e[8]=i[8]*n,e[9]=i[9]*n,e[10]=i[10]*n,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromEuler(t){let e=this.elements,i=t.x,s=t.y,r=t.z,n=Math.cos(i),a=Math.sin(i),o=Math.cos(s),h=Math.sin(s),l=Math.cos(r),u=Math.sin(r);if("XYZ"===t.order){let t=n*l,i=n*u,s=a*l,r=a*u;e[0]=o*l,e[4]=-o*u,e[8]=h,e[1]=i+s*h,e[5]=t-r*h,e[9]=-a*o,e[2]=r-t*h,e[6]=s+i*h,e[10]=n*o}else if("YXZ"===t.order){let t=o*l,i=o*u,s=h*l,r=h*u;e[0]=t+r*a,e[4]=s*a-i,e[8]=n*h,e[1]=n*u,e[5]=n*l,e[9]=-a,e[2]=i*a-s,e[6]=r+t*a,e[10]=n*o}else if("ZXY"===t.order){let t=o*l,i=o*u,s=h*l,r=h*u;e[0]=t-r*a,e[4]=-n*u,e[8]=s+i*a,e[1]=i+s*a,e[5]=n*l,e[9]=r-t*a,e[2]=-n*h,e[6]=a,e[10]=n*o}else if("ZYX"===t.order){let t=n*l,i=n*u,s=a*l,r=a*u;e[0]=o*l,e[4]=s*h-i,e[8]=t*h+r,e[1]=o*u,e[5]=r*h+t,e[9]=i*h-s,e[2]=-h,e[6]=a*o,e[10]=n*o}else if("YZX"===t.order){let t=n*o,i=n*h,s=a*o,r=a*h;e[0]=o*l,e[4]=r-t*u,e[8]=s*u+i,e[1]=u,e[5]=n*l,e[9]=-a*l,e[2]=-h*l,e[6]=i*u+s,e[10]=t-r*u}else if("XZY"===t.order){let t=n*o,i=n*h,s=a*o,r=a*h;e[0]=o*l,e[4]=-u,e[8]=h*l,e[1]=t*u+r,e[5]=n*l,e[9]=i*u-s,e[2]=s*u-i,e[6]=a*l,e[10]=r*u+t}return e[3]=0,e[7]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromQuaternion(t){return this.compose(sU,t,sW)}lookAt(t,e,i){let s=this.elements;return sq.subVectors(t,e),0===sq.lengthSq()&&(sq.z=1),sq.normalize(),sD.crossVectors(i,sq),0===sD.lengthSq()&&(1===Math.abs(i.z)?sq.x+=1e-4:sq.z+=1e-4,sq.normalize(),sD.crossVectors(i,sq)),sD.normalize(),sH.crossVectors(sq,sD),s[0]=sD.x,s[4]=sH.x,s[8]=sq.x,s[1]=sD.y,s[5]=sH.y,s[9]=sq.y,s[2]=sD.z,s[6]=sH.z,s[10]=sq.z,this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){let i=t.elements,s=e.elements,r=this.elements,n=i[0],a=i[4],o=i[8],h=i[12],l=i[1],u=i[5],c=i[9],d=i[13],p=i[2],m=i[6],y=i[10],f=i[14],g=i[3],x=i[7],b=i[11],v=i[15],w=s[0],M=s[4],S=s[8],_=s[12],A=s[1],T=s[5],z=s[9],I=s[13],C=s[2],k=s[6],B=s[10],R=s[14],E=s[3],P=s[7],O=s[11],N=s[15];return r[0]=n*w+a*A+o*C+h*E,r[4]=n*M+a*T+o*k+h*P,r[8]=n*S+a*z+o*B+h*O,r[12]=n*_+a*I+o*R+h*N,r[1]=l*w+u*A+c*C+d*E,r[5]=l*M+u*T+c*k+d*P,r[9]=l*S+u*z+c*B+d*O,r[13]=l*_+u*I+c*R+d*N,r[2]=p*w+m*A+y*C+f*E,r[6]=p*M+m*T+y*k+f*P,r[10]=p*S+m*z+y*B+f*O,r[14]=p*_+m*I+y*R+f*N,r[3]=g*w+x*A+b*C+v*E,r[7]=g*M+x*T+b*k+v*P,r[11]=g*S+x*z+b*B+v*O,r[15]=g*_+x*I+b*R+v*N,this}multiplyScalar(t){let e=this.elements;return e[0]*=t,e[4]*=t,e[8]*=t,e[12]*=t,e[1]*=t,e[5]*=t,e[9]*=t,e[13]*=t,e[2]*=t,e[6]*=t,e[10]*=t,e[14]*=t,e[3]*=t,e[7]*=t,e[11]*=t,e[15]*=t,this}determinant(){let t=this.elements,e=t[0],i=t[4],s=t[8],r=t[12],n=t[1],a=t[5],o=t[9],h=t[13],l=t[2],u=t[6],c=t[10],d=t[14],p=t[3],m=t[7];return p*(r*o*u-s*h*u-r*a*c+i*h*c+s*a*d-i*o*d)+m*(e*o*d-e*h*c+r*n*c-s*n*d+s*h*l-r*o*l)+t[11]*(e*h*u-e*a*d-r*n*u+i*n*d+r*a*l-i*h*l)+t[15]*(-s*a*l-e*o*u+e*a*c+s*n*u-i*n*c+i*o*l)}transpose(){let t,e=this.elements;return t=e[1],e[1]=e[4],e[4]=t,t=e[2],e[2]=e[8],e[8]=t,t=e[6],e[6]=e[9],e[9]=t,t=e[3],e[3]=e[12],e[12]=t,t=e[7],e[7]=e[13],e[13]=t,t=e[11],e[11]=e[14],e[14]=t,this}setPosition(t,e,i){let s=this.elements;return t.isVector3?(s[12]=t.x,s[13]=t.y,s[14]=t.z):(s[12]=t,s[13]=e,s[14]=i),this}invert(){let t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],u=t[9],c=t[10],d=t[11],p=t[12],m=t[13],y=t[14],f=t[15],g=u*y*h-m*c*h+m*o*d-a*y*d-u*o*f+a*c*f,x=p*c*h-l*y*h-p*o*d+n*y*d+l*o*f-n*c*f,b=l*m*h-p*u*h+p*a*d-n*m*d-l*a*f+n*u*f,v=p*u*o-l*m*o-p*a*c+n*m*c+l*a*y-n*u*y,w=e*g+i*x+s*b+r*v;if(0===w)return this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);let M=1/w;return t[0]=g*M,t[1]=(m*c*r-u*y*r-m*s*d+i*y*d+u*s*f-i*c*f)*M,t[2]=(a*y*r-m*o*r+m*s*h-i*y*h-a*s*f+i*o*f)*M,t[3]=(u*o*r-a*c*r-u*s*h+i*c*h+a*s*d-i*o*d)*M,t[4]=x*M,t[5]=(l*y*r-p*c*r+p*s*d-e*y*d-l*s*f+e*c*f)*M,t[6]=(p*o*r-n*y*r-p*s*h+e*y*h+n*s*f-e*o*f)*M,t[7]=(n*c*r-l*o*r+l*s*h-e*c*h-n*s*d+e*o*d)*M,t[8]=b*M,t[9]=(p*u*r-l*m*r-p*i*d+e*m*d+l*i*f-e*u*f)*M,t[10]=(n*m*r-p*a*r+p*i*h-e*m*h-n*i*f+e*a*f)*M,t[11]=(l*a*r-n*u*r-l*i*h+e*u*h+n*i*d-e*a*d)*M,t[12]=v*M,t[13]=(l*m*s-p*u*s+p*i*c-e*m*c-l*i*y+e*u*y)*M,t[14]=(p*a*s-n*m*s-p*i*o+e*m*o+n*i*y-e*a*y)*M,t[15]=(n*u*s-l*a*s+l*i*o-e*u*o-n*i*c+e*a*c)*M,this}scale(t){let e=this.elements,i=t.x,s=t.y,r=t.z;return e[0]*=i,e[4]*=s,e[8]*=r,e[1]*=i,e[5]*=s,e[9]*=r,e[2]*=i,e[6]*=s,e[10]*=r,e[3]*=i,e[7]*=s,e[11]*=r,this}getMaxScaleOnAxis(){let t=this.elements,e=t[0]*t[0]+t[1]*t[1]+t[2]*t[2];return Math.sqrt(Math.max(e,t[4]*t[4]+t[5]*t[5]+t[6]*t[6],t[8]*t[8]+t[9]*t[9]+t[10]*t[10]))}makeTranslation(t,e,i){return t.isVector3?this.set(1,0,0,t.x,0,1,0,t.y,0,0,1,t.z,0,0,0,1):this.set(1,0,0,t,0,1,0,e,0,0,1,i,0,0,0,1),this}makeRotationX(t){let e=Math.cos(t),i=Math.sin(t);return this.set(1,0,0,0,0,e,-i,0,0,i,e,0,0,0,0,1),this}makeRotationY(t){let e=Math.cos(t),i=Math.sin(t);return this.set(e,0,i,0,0,1,0,0,-i,0,e,0,0,0,0,1),this}makeRotationZ(t){let e=Math.cos(t),i=Math.sin(t);return this.set(e,-i,0,0,i,e,0,0,0,0,1,0,0,0,0,1),this}makeRotationAxis(t,e){let i=Math.cos(e),s=Math.sin(e),r=1-i,n=t.x,a=t.y,o=t.z,h=r*n,l=r*a;return this.set(h*n+i,h*a-s*o,h*o+s*a,0,h*a+s*o,l*a+i,l*o-s*n,0,h*o-s*a,l*o+s*n,r*o*o+i,0,0,0,0,1),this}makeScale(t,e,i){return this.set(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1),this}makeShear(t,e,i,s,r,n){return this.set(1,i,r,0,t,1,n,0,e,s,1,0,0,0,0,1),this}compose(t,e,i){let s=this.elements,r=e._x,n=e._y,a=e._z,o=e._w,h=r+r,l=n+n,u=a+a,c=r*h,d=r*l,p=r*u,m=n*l,y=n*u,f=a*u,g=o*h,x=o*l,b=o*u,v=i.x,w=i.y,M=i.z;return s[0]=(1-(m+f))*v,s[1]=(d+b)*v,s[2]=(p-x)*v,s[3]=0,s[4]=(d-b)*w,s[5]=(1-(c+f))*w,s[6]=(y+g)*w,s[7]=0,s[8]=(p+x)*M,s[9]=(y-g)*M,s[10]=(1-(c+m))*M,s[11]=0,s[12]=t.x,s[13]=t.y,s[14]=t.z,s[15]=1,this}decompose(t,e,i){let s=this.elements,r=sL.set(s[0],s[1],s[2]).length(),n=sL.set(s[4],s[5],s[6]).length(),a=sL.set(s[8],s[9],s[10]).length();0>this.determinant()&&(r=-r),t.x=s[12],t.y=s[13],t.z=s[14],sj.copy(this);let o=1/r,h=1/n,l=1/a;return sj.elements[0]*=o,sj.elements[1]*=o,sj.elements[2]*=o,sj.elements[4]*=h,sj.elements[5]*=h,sj.elements[6]*=h,sj.elements[8]*=l,sj.elements[9]*=l,sj.elements[10]*=l,e.setFromRotationMatrix(sj),i.x=r,i.y=n,i.z=a,this}makePerspective(t,e,i,s,r,n,a=iA){let o,h,l=this.elements;if(a===iA)o=-(n+r)/(n-r),h=-2*n*r/(n-r);else if(a===iT)o=-n/(n-r),h=-n*r/(n-r);else throw Error("THREE.Matrix4.makePerspective(): Invalid coordinate system: "+a);return l[0]=2*r/(e-t),l[4]=0,l[8]=(e+t)/(e-t),l[12]=0,l[1]=0,l[5]=2*r/(i-s),l[9]=(i+s)/(i-s),l[13]=0,l[2]=0,l[6]=0,l[10]=o,l[14]=h,l[3]=0,l[7]=0,l[11]=-1,l[15]=0,this}makeOrthographic(t,e,i,s,r,n,a=iA){let o,h,l=this.elements,u=1/(e-t),c=1/(i-s),d=1/(n-r);if(a===iA)o=(n+r)*d,h=-2*d;else if(a===iT)o=r*d,h=-1*d;else throw Error("THREE.Matrix4.makeOrthographic(): Invalid coordinate system: "+a);return l[0]=2*u,l[4]=0,l[8]=0,l[12]=-((e+t)*u),l[1]=0,l[5]=2*c,l[9]=0,l[13]=-((i+s)*c),l[2]=0,l[6]=0,l[10]=h,l[14]=-o,l[3]=0,l[7]=0,l[11]=0,l[15]=1,this}equals(t){let e=this.elements,i=t.elements;for(let t=0;t<16;t++)if(e[t]!==i[t])return!1;return!0}fromArray(t,e=0){for(let i=0;i<16;i++)this.elements[i]=t[i+e];return this}toArray(t=[],e=0){let i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t[e+9]=i[9],t[e+10]=i[10],t[e+11]=i[11],t[e+12]=i[12],t[e+13]=i[13],t[e+14]=i[14],t[e+15]=i[15],t}}let sL=new sh,sj=new sV,sU=new sh(0,0,0),sW=new sh(1,1,1),sD=new sh,sH=new sh,sq=new sh,sJ=new sV,sX=new so;class sZ{constructor(t=0,e=0,i=0,s=sZ.DEFAULT_ORDER){this.isEuler=!0,this._x=t,this._y=e,this._z=i,this._order=s}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get order(){return this._order}set order(t){this._order=t,this._onChangeCallback()}set(t,e,i,s=this._order){return this._x=t,this._y=e,this._z=i,this._order=s,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._order)}copy(t){return this._x=t._x,this._y=t._y,this._z=t._z,this._order=t._order,this._onChangeCallback(),this}setFromRotationMatrix(t,e=this._order,i=!0){let s=t.elements,r=s[0],n=s[4],a=s[8],o=s[1],h=s[5],l=s[9],u=s[2],c=s[6],d=s[10];switch(e){case"XYZ":this._y=Math.asin(iP(a,-1,1)),.9999999>Math.abs(a)?(this._x=Math.atan2(-l,d),this._z=Math.atan2(-n,r)):(this._x=Math.atan2(c,h),this._z=0);break;case"YXZ":this._x=Math.asin(-iP(l,-1,1)),.9999999>Math.abs(l)?(this._y=Math.atan2(a,d),this._z=Math.atan2(o,h)):(this._y=Math.atan2(-u,r),this._z=0);break;case"ZXY":this._x=Math.asin(iP(c,-1,1)),.9999999>Math.abs(c)?(this._y=Math.atan2(-u,d),this._z=Math.atan2(-n,h)):(this._y=0,this._z=Math.atan2(o,r));break;case"ZYX":this._y=Math.asin(-iP(u,-1,1)),.9999999>Math.abs(u)?(this._x=Math.atan2(c,d),this._z=Math.atan2(o,r)):(this._x=0,this._z=Math.atan2(-n,h));break;case"YZX":this._z=Math.asin(iP(o,-1,1)),.9999999>Math.abs(o)?(this._x=Math.atan2(-l,h),this._y=Math.atan2(-u,r)):(this._x=0,this._y=Math.atan2(a,d));break;case"XZY":this._z=Math.asin(-iP(n,-1,1)),.9999999>Math.abs(n)?(this._x=Math.atan2(c,h),this._y=Math.atan2(a,r)):(this._x=Math.atan2(-l,d),this._y=0);break;default:console.warn("THREE.Euler: .setFromRotationMatrix() encountered an unknown order: "+e)}return this._order=e,!0===i&&this._onChangeCallback(),this}setFromQuaternion(t,e,i){return sJ.makeRotationFromQuaternion(t),this.setFromRotationMatrix(sJ,e,i)}setFromVector3(t,e=this._order){return this.set(t.x,t.y,t.z,e)}reorder(t){return sX.setFromEuler(this),this.setFromQuaternion(sX,t)}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._order===this._order}fromArray(t){return this._x=t[0],this._y=t[1],this._z=t[2],void 0!==t[3]&&(this._order=t[3]),this._onChangeCallback(),this}toArray(t=[],e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._order,t}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._order}}sZ.DEFAULT_ORDER="XYZ";class sY{constructor(){this.mask=1}set(t){this.mask=1<<t>>>0}enable(t){this.mask|=1<<t}enableAll(){this.mask=-1}toggle(t){this.mask^=1<<t}disable(t){this.mask&=~(1<<t)}disableAll(){this.mask=0}test(t){return(this.mask&t.mask)!=0}isEnabled(t){return(this.mask&1<<t)!=0}}let sG=0,s$=new sh,sQ=new so,sK=new sV,s0=new sh,s1=new sh,s2=new sh,s3=new so,s5=new sh(1,0,0),s4=new sh(0,1,0),s6=new sh(0,0,1),s8={type:"added"},s9={type:"removed"},s7={type:"childadded",child:null},rt={type:"childremoved",child:null};class re extends iI{constructor(){super(),this.isObject3D=!0,Object.defineProperty(this,"id",{value:sG++}),this.uuid=iE(),this.name="",this.type="Object3D",this.parent=null,this.children=[],this.up=re.DEFAULT_UP.clone();let t=new sh,e=new sZ,i=new so,s=new sh(1,1,1);e._onChange(function(){i.setFromEuler(e,!1)}),i._onChange(function(){e.setFromQuaternion(i,void 0,!1)}),Object.defineProperties(this,{position:{configurable:!0,enumerable:!0,value:t},rotation:{configurable:!0,enumerable:!0,value:e},quaternion:{configurable:!0,enumerable:!0,value:i},scale:{configurable:!0,enumerable:!0,value:s},modelViewMatrix:{value:new sV},normalMatrix:{value:new iU}}),this.matrix=new sV,this.matrixWorld=new sV,this.matrixAutoUpdate=re.DEFAULT_MATRIX_AUTO_UPDATE,this.matrixWorldAutoUpdate=re.DEFAULT_MATRIX_WORLD_AUTO_UPDATE,this.matrixWorldNeedsUpdate=!1,this.layers=new sY,this.visible=!0,this.castShadow=!1,this.receiveShadow=!1,this.frustumCulled=!0,this.renderOrder=0,this.animations=[],this.customDepthMaterial=void 0,this.customDistanceMaterial=void 0,this.userData={}}onBeforeShadow(){}onAfterShadow(){}onBeforeRender(){}onAfterRender(){}applyMatrix4(t){this.matrixAutoUpdate&&this.updateMatrix(),this.matrix.premultiply(t),this.matrix.decompose(this.position,this.quaternion,this.scale)}applyQuaternion(t){return this.quaternion.premultiply(t),this}setRotationFromAxisAngle(t,e){this.quaternion.setFromAxisAngle(t,e)}setRotationFromEuler(t){this.quaternion.setFromEuler(t,!0)}setRotationFromMatrix(t){this.quaternion.setFromRotationMatrix(t)}setRotationFromQuaternion(t){this.quaternion.copy(t)}rotateOnAxis(t,e){return sQ.setFromAxisAngle(t,e),this.quaternion.multiply(sQ),this}rotateOnWorldAxis(t,e){return sQ.setFromAxisAngle(t,e),this.quaternion.premultiply(sQ),this}rotateX(t){return this.rotateOnAxis(s5,t)}rotateY(t){return this.rotateOnAxis(s4,t)}rotateZ(t){return this.rotateOnAxis(s6,t)}translateOnAxis(t,e){return s$.copy(t).applyQuaternion(this.quaternion),this.position.add(s$.multiplyScalar(e)),this}translateX(t){return this.translateOnAxis(s5,t)}translateY(t){return this.translateOnAxis(s4,t)}translateZ(t){return this.translateOnAxis(s6,t)}localToWorld(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(this.matrixWorld)}worldToLocal(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(sK.copy(this.matrixWorld).invert())}lookAt(t,e,i){t.isVector3?s0.copy(t):s0.set(t,e,i);let s=this.parent;this.updateWorldMatrix(!0,!1),s1.setFromMatrixPosition(this.matrixWorld),this.isCamera||this.isLight?sK.lookAt(s1,s0,this.up):sK.lookAt(s0,s1,this.up),this.quaternion.setFromRotationMatrix(sK),s&&(sK.extractRotation(s.matrixWorld),sQ.setFromRotationMatrix(sK),this.quaternion.premultiply(sQ.invert()))}add(t){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.add(arguments[t]);return this}return t===this?console.error("THREE.Object3D.add: object can't be added as a child of itself.",t):t&&t.isObject3D?(t.removeFromParent(),t.parent=this,this.children.push(t),t.dispatchEvent(s8),s7.child=t,this.dispatchEvent(s7),s7.child=null):console.error("THREE.Object3D.add: object not an instance of THREE.Object3D.",t),this}remove(t){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.remove(arguments[t]);return this}let e=this.children.indexOf(t);return -1!==e&&(t.parent=null,this.children.splice(e,1),t.dispatchEvent(s9),rt.child=t,this.dispatchEvent(rt),rt.child=null),this}removeFromParent(){let t=this.parent;return null!==t&&t.remove(this),this}clear(){return this.remove(...this.children)}attach(t){return this.updateWorldMatrix(!0,!1),sK.copy(this.matrixWorld).invert(),null!==t.parent&&(t.parent.updateWorldMatrix(!0,!1),sK.multiply(t.parent.matrixWorld)),t.applyMatrix4(sK),t.removeFromParent(),t.parent=this,this.children.push(t),t.updateWorldMatrix(!1,!0),t.dispatchEvent(s8),s7.child=t,this.dispatchEvent(s7),s7.child=null,this}getObjectById(t){return this.getObjectByProperty("id",t)}getObjectByName(t){return this.getObjectByProperty("name",t)}getObjectByProperty(t,e){if(this[t]===e)return this;for(let i=0,s=this.children.length;i<s;i++){let s=this.children[i].getObjectByProperty(t,e);if(void 0!==s)return s}}getObjectsByProperty(t,e,i=[]){this[t]===e&&i.push(this);let s=this.children;for(let r=0,n=s.length;r<n;r++)s[r].getObjectsByProperty(t,e,i);return i}getWorldPosition(t){return this.updateWorldMatrix(!0,!1),t.setFromMatrixPosition(this.matrixWorld)}getWorldQuaternion(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(s1,t,s2),t}getWorldScale(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(s1,s3,t),t}getWorldDirection(t){this.updateWorldMatrix(!0,!1);let e=this.matrixWorld.elements;return t.set(e[8],e[9],e[10]).normalize()}raycast(){}traverse(t){t(this);let e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].traverse(t)}traverseVisible(t){if(!1===this.visible)return;t(this);let e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].traverseVisible(t)}traverseAncestors(t){let e=this.parent;null!==e&&(t(e),e.traverseAncestors(t))}updateMatrix(){this.matrix.compose(this.position,this.quaternion,this.scale),this.matrixWorldNeedsUpdate=!0}updateMatrixWorld(t){this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||t)&&(!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),this.matrixWorldNeedsUpdate=!1,t=!0);let e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].updateMatrixWorld(t)}updateWorldMatrix(t,e){let i=this.parent;if(!0===t&&null!==i&&i.updateWorldMatrix(!0,!1),this.matrixAutoUpdate&&this.updateMatrix(),!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),!0===e){let t=this.children;for(let e=0,i=t.length;e<i;e++)t[e].updateWorldMatrix(!1,!0)}}toJSON(t){let e=void 0===t||"string"==typeof t,i={};e&&(t={geometries:{},materials:{},textures:{},images:{},shapes:{},skeletons:{},animations:{},nodes:{}},i.metadata={version:4.6,type:"Object",generator:"Object3D.toJSON"});let s={};function r(e,i){return void 0===e[i.uuid]&&(e[i.uuid]=i.toJSON(t)),i.uuid}if(s.uuid=this.uuid,s.type=this.type,""!==this.name&&(s.name=this.name),!0===this.castShadow&&(s.castShadow=!0),!0===this.receiveShadow&&(s.receiveShadow=!0),!1===this.visible&&(s.visible=!1),!1===this.frustumCulled&&(s.frustumCulled=!1),0!==this.renderOrder&&(s.renderOrder=this.renderOrder),Object.keys(this.userData).length>0&&(s.userData=this.userData),s.layers=this.layers.mask,s.matrix=this.matrix.toArray(),s.up=this.up.toArray(),!1===this.matrixAutoUpdate&&(s.matrixAutoUpdate=!1),this.isInstancedMesh&&(s.type="InstancedMesh",s.count=this.count,s.instanceMatrix=this.instanceMatrix.toJSON(),null!==this.instanceColor&&(s.instanceColor=this.instanceColor.toJSON())),this.isBatchedMesh&&(s.type="BatchedMesh",s.perObjectFrustumCulled=this.perObjectFrustumCulled,s.sortObjects=this.sortObjects,s.drawRanges=this._drawRanges,s.reservedRanges=this._reservedRanges,s.visibility=this._visibility,s.active=this._active,s.bounds=this._bounds.map(t=>({boxInitialized:t.boxInitialized,boxMin:t.box.min.toArray(),boxMax:t.box.max.toArray(),sphereInitialized:t.sphereInitialized,sphereRadius:t.sphere.radius,sphereCenter:t.sphere.center.toArray()})),s.maxInstanceCount=this._maxInstanceCount,s.maxVertexCount=this._maxVertexCount,s.maxIndexCount=this._maxIndexCount,s.geometryInitialized=this._geometryInitialized,s.geometryCount=this._geometryCount,s.matricesTexture=this._matricesTexture.toJSON(t),null!==this._colorsTexture&&(s.colorsTexture=this._colorsTexture.toJSON(t)),null!==this.boundingSphere&&(s.boundingSphere={center:s.boundingSphere.center.toArray(),radius:s.boundingSphere.radius}),null!==this.boundingBox&&(s.boundingBox={min:s.boundingBox.min.toArray(),max:s.boundingBox.max.toArray()})),this.isScene)this.background&&(this.background.isColor?s.background=this.background.toJSON():this.background.isTexture&&(s.background=this.background.toJSON(t).uuid)),this.environment&&this.environment.isTexture&&!0!==this.environment.isRenderTargetTexture&&(s.environment=this.environment.toJSON(t).uuid);else if(this.isMesh||this.isLine||this.isPoints){s.geometry=r(t.geometries,this.geometry);let e=this.geometry.parameters;if(void 0!==e&&void 0!==e.shapes){let i=e.shapes;if(Array.isArray(i))for(let e=0,s=i.length;e<s;e++){let s=i[e];r(t.shapes,s)}else r(t.shapes,i)}}if(this.isSkinnedMesh&&(s.bindMode=this.bindMode,s.bindMatrix=this.bindMatrix.toArray(),void 0!==this.skeleton&&(r(t.skeletons,this.skeleton),s.skeleton=this.skeleton.uuid)),void 0!==this.material)if(Array.isArray(this.material)){let e=[];for(let i=0,s=this.material.length;i<s;i++)e.push(r(t.materials,this.material[i]));s.material=e}else s.material=r(t.materials,this.material);if(this.children.length>0){s.children=[];for(let e=0;e<this.children.length;e++)s.children.push(this.children[e].toJSON(t).object)}if(this.animations.length>0){s.animations=[];for(let e=0;e<this.animations.length;e++){let i=this.animations[e];s.animations.push(r(t.animations,i))}}if(e){let e=n(t.geometries),s=n(t.materials),r=n(t.textures),a=n(t.images),o=n(t.shapes),h=n(t.skeletons),l=n(t.animations),u=n(t.nodes);e.length>0&&(i.geometries=e),s.length>0&&(i.materials=s),r.length>0&&(i.textures=r),a.length>0&&(i.images=a),o.length>0&&(i.shapes=o),h.length>0&&(i.skeletons=h),l.length>0&&(i.animations=l),u.length>0&&(i.nodes=u)}return i.object=s,i;function n(t){let e=[];for(let i in t){let s=t[i];delete s.metadata,e.push(s)}return e}}clone(t){return new this.constructor().copy(this,t)}copy(t,e=!0){if(this.name=t.name,this.up.copy(t.up),this.position.copy(t.position),this.rotation.order=t.rotation.order,this.quaternion.copy(t.quaternion),this.scale.copy(t.scale),this.matrix.copy(t.matrix),this.matrixWorld.copy(t.matrixWorld),this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrixWorldAutoUpdate=t.matrixWorldAutoUpdate,this.matrixWorldNeedsUpdate=t.matrixWorldNeedsUpdate,this.layers.mask=t.layers.mask,this.visible=t.visible,this.castShadow=t.castShadow,this.receiveShadow=t.receiveShadow,this.frustumCulled=t.frustumCulled,this.renderOrder=t.renderOrder,this.animations=t.animations.slice(),this.userData=JSON.parse(JSON.stringify(t.userData)),!0===e)for(let e=0;e<t.children.length;e++){let i=t.children[e];this.add(i.clone())}return this}}re.DEFAULT_UP=new sh(0,1,0),re.DEFAULT_MATRIX_AUTO_UPDATE=!0,re.DEFAULT_MATRIX_WORLD_AUTO_UPDATE=!0;let ri=new sh,rs=new sh,rr=new sh,rn=new sh,ra=new sh,ro=new sh,rh=new sh,rl=new sh,ru=new sh,rc=new sh,rd=new st,rp=new st,rm=new st;class ry{constructor(t=new sh,e=new sh,i=new sh){this.a=t,this.b=e,this.c=i}static getNormal(t,e,i,s){s.subVectors(i,e),ri.subVectors(t,e),s.cross(ri);let r=s.lengthSq();return r>0?s.multiplyScalar(1/Math.sqrt(r)):s.set(0,0,0)}static getBarycoord(t,e,i,s,r){ri.subVectors(s,e),rs.subVectors(i,e),rr.subVectors(t,e);let n=ri.dot(ri),a=ri.dot(rs),o=ri.dot(rr),h=rs.dot(rs),l=rs.dot(rr),u=n*h-a*a;if(0===u)return r.set(0,0,0),null;let c=1/u,d=(h*o-a*l)*c,p=(n*l-a*o)*c;return r.set(1-d-p,p,d)}static containsPoint(t,e,i,s){return null!==this.getBarycoord(t,e,i,s,rn)&&rn.x>=0&&rn.y>=0&&rn.x+rn.y<=1}static getInterpolation(t,e,i,s,r,n,a,o){return null===this.getBarycoord(t,e,i,s,rn)?(o.x=0,o.y=0,"z"in o&&(o.z=0),"w"in o&&(o.w=0),null):(o.setScalar(0),o.addScaledVector(r,rn.x),o.addScaledVector(n,rn.y),o.addScaledVector(a,rn.z),o)}static getInterpolatedAttribute(t,e,i,s,r,n){return rd.setScalar(0),rp.setScalar(0),rm.setScalar(0),rd.fromBufferAttribute(t,e),rp.fromBufferAttribute(t,i),rm.fromBufferAttribute(t,s),n.setScalar(0),n.addScaledVector(rd,r.x),n.addScaledVector(rp,r.y),n.addScaledVector(rm,r.z),n}static isFrontFacing(t,e,i,s){return ri.subVectors(i,e),rs.subVectors(t,e),0>ri.cross(rs).dot(s)}set(t,e,i){return this.a.copy(t),this.b.copy(e),this.c.copy(i),this}setFromPointsAndIndices(t,e,i,s){return this.a.copy(t[e]),this.b.copy(t[i]),this.c.copy(t[s]),this}setFromAttributeAndIndices(t,e,i,s){return this.a.fromBufferAttribute(t,e),this.b.fromBufferAttribute(t,i),this.c.fromBufferAttribute(t,s),this}clone(){return new this.constructor().copy(this)}copy(t){return this.a.copy(t.a),this.b.copy(t.b),this.c.copy(t.c),this}getArea(){return ri.subVectors(this.c,this.b),rs.subVectors(this.a,this.b),.5*ri.cross(rs).length()}getMidpoint(t){return t.addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)}getNormal(t){return ry.getNormal(this.a,this.b,this.c,t)}getPlane(t){return t.setFromCoplanarPoints(this.a,this.b,this.c)}getBarycoord(t,e){return ry.getBarycoord(t,this.a,this.b,this.c,e)}getInterpolation(t,e,i,s,r){return ry.getInterpolation(t,this.a,this.b,this.c,e,i,s,r)}containsPoint(t){return ry.containsPoint(t,this.a,this.b,this.c)}isFrontFacing(t){return ry.isFrontFacing(this.a,this.b,this.c,t)}intersectsBox(t){return t.intersectsTriangle(this)}closestPointToPoint(t,e){let i,s,r=this.a,n=this.b,a=this.c;ra.subVectors(n,r),ro.subVectors(a,r),rl.subVectors(t,r);let o=ra.dot(rl),h=ro.dot(rl);if(o<=0&&h<=0)return e.copy(r);ru.subVectors(t,n);let l=ra.dot(ru),u=ro.dot(ru);if(l>=0&&u<=l)return e.copy(n);let c=o*u-l*h;if(c<=0&&o>=0&&l<=0)return i=o/(o-l),e.copy(r).addScaledVector(ra,i);rc.subVectors(t,a);let d=ra.dot(rc),p=ro.dot(rc);if(p>=0&&d<=p)return e.copy(a);let m=d*h-o*p;if(m<=0&&h>=0&&p<=0)return s=h/(h-p),e.copy(r).addScaledVector(ro,s);let y=l*p-d*u;if(y<=0&&u-l>=0&&d-p>=0)return rh.subVectors(a,n),s=(u-l)/(u-l+(d-p)),e.copy(n).addScaledVector(rh,s);let f=1/(y+m+c);return i=m*f,s=c*f,e.copy(r).addScaledVector(ra,i).addScaledVector(ro,s)}equals(t){return t.a.equals(this.a)&&t.b.equals(this.b)&&t.c.equals(this.c)}}let rf={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32},rg={h:0,s:0,l:0},rx={h:0,s:0,l:0};function rb(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*6*(2/3-i):t}class rv{constructor(t,e,i){return this.isColor=!0,this.r=1,this.g=1,this.b=1,this.set(t,e,i)}set(t,e,i){return void 0===e&&void 0===i?t&&t.isColor?this.copy(t):"number"==typeof t?this.setHex(t):"string"==typeof t&&this.setStyle(t):this.setRGB(t,e,i),this}setScalar(t){return this.r=t,this.g=t,this.b=t,this}setHex(t,e=eZ){return t=Math.floor(t),this.r=(t>>16&255)/255,this.g=(t>>8&255)/255,this.b=(255&t)/255,i1.toWorkingColorSpace(this,e),this}setRGB(t,e,i,s=i1.workingColorSpace){return this.r=t,this.g=e,this.b=i,i1.toWorkingColorSpace(this,s),this}setHSL(t,e,i,s=i1.workingColorSpace){if(t=iO(t,1),e=iP(e,0,1),i=iP(i,0,1),0===e)this.r=this.g=this.b=i;else{let s=i<=.5?i*(1+e):i+e-i*e,r=2*i-s;this.r=rb(r,s,t+1/3),this.g=rb(r,s,t),this.b=rb(r,s,t-1/3)}return i1.toWorkingColorSpace(this,s),this}setStyle(t,e=eZ){let i;function s(e){void 0!==e&&1>parseFloat(e)&&console.warn("THREE.Color: Alpha component of "+t+" will be ignored.")}if(i=/^(\w+)\(([^\)]*)\)/.exec(t)){let r,n=i[1],a=i[2];switch(n){case"rgb":case"rgba":if(r=/^\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return s(r[4]),this.setRGB(Math.min(255,parseInt(r[1],10))/255,Math.min(255,parseInt(r[2],10))/255,Math.min(255,parseInt(r[3],10))/255,e);if(r=/^\s*(\d+)\%\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return s(r[4]),this.setRGB(Math.min(100,parseInt(r[1],10))/100,Math.min(100,parseInt(r[2],10))/100,Math.min(100,parseInt(r[3],10))/100,e);break;case"hsl":case"hsla":if(r=/^\s*(\d*\.?\d+)\s*,\s*(\d*\.?\d+)\%\s*,\s*(\d*\.?\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return s(r[4]),this.setHSL(parseFloat(r[1])/360,parseFloat(r[2])/100,parseFloat(r[3])/100,e);break;default:console.warn("THREE.Color: Unknown color model "+t)}}else if(i=/^\#([A-Fa-f\d]+)$/.exec(t)){let s=i[1],r=s.length;if(3===r)return this.setRGB(parseInt(s.charAt(0),16)/15,parseInt(s.charAt(1),16)/15,parseInt(s.charAt(2),16)/15,e);if(6===r)return this.setHex(parseInt(s,16),e);console.warn("THREE.Color: Invalid hex color "+t)}else if(t&&t.length>0)return this.setColorName(t,e);return this}setColorName(t,e=eZ){let i=rf[t.toLowerCase()];return void 0!==i?this.setHex(i,e):console.warn("THREE.Color: Unknown color "+t),this}clone(){return new this.constructor(this.r,this.g,this.b)}copy(t){return this.r=t.r,this.g=t.g,this.b=t.b,this}copySRGBToLinear(t){return this.r=i2(t.r),this.g=i2(t.g),this.b=i2(t.b),this}copyLinearToSRGB(t){return this.r=i3(t.r),this.g=i3(t.g),this.b=i3(t.b),this}convertSRGBToLinear(){return this.copySRGBToLinear(this),this}convertLinearToSRGB(){return this.copyLinearToSRGB(this),this}getHex(t=eZ){return i1.fromWorkingColorSpace(rw.copy(this),t),65536*Math.round(iP(255*rw.r,0,255))+256*Math.round(iP(255*rw.g,0,255))+Math.round(iP(255*rw.b,0,255))}getHexString(t=eZ){return("000000"+this.getHex(t).toString(16)).slice(-6)}getHSL(t,e=i1.workingColorSpace){let i,s;i1.fromWorkingColorSpace(rw.copy(this),e);let r=rw.r,n=rw.g,a=rw.b,o=Math.max(r,n,a),h=Math.min(r,n,a),l=(h+o)/2;if(h===o)i=0,s=0;else{let t=o-h;switch(s=l<=.5?t/(o+h):t/(2-o-h),o){case r:i=(n-a)/t+6*(n<a);break;case n:i=(a-r)/t+2;break;case a:i=(r-n)/t+4}i/=6}return t.h=i,t.s=s,t.l=l,t}getRGB(t,e=i1.workingColorSpace){return i1.fromWorkingColorSpace(rw.copy(this),e),t.r=rw.r,t.g=rw.g,t.b=rw.b,t}getStyle(t=eZ){i1.fromWorkingColorSpace(rw.copy(this),t);let e=rw.r,i=rw.g,s=rw.b;return t!==eZ?`color(${t} ${e.toFixed(3)} ${i.toFixed(3)} ${s.toFixed(3)})`:`rgb(${Math.round(255*e)},${Math.round(255*i)},${Math.round(255*s)})`}offsetHSL(t,e,i){return this.getHSL(rg),this.setHSL(rg.h+t,rg.s+e,rg.l+i)}add(t){return this.r+=t.r,this.g+=t.g,this.b+=t.b,this}addColors(t,e){return this.r=t.r+e.r,this.g=t.g+e.g,this.b=t.b+e.b,this}addScalar(t){return this.r+=t,this.g+=t,this.b+=t,this}sub(t){return this.r=Math.max(0,this.r-t.r),this.g=Math.max(0,this.g-t.g),this.b=Math.max(0,this.b-t.b),this}multiply(t){return this.r*=t.r,this.g*=t.g,this.b*=t.b,this}multiplyScalar(t){return this.r*=t,this.g*=t,this.b*=t,this}lerp(t,e){return this.r+=(t.r-this.r)*e,this.g+=(t.g-this.g)*e,this.b+=(t.b-this.b)*e,this}lerpColors(t,e,i){return this.r=t.r+(e.r-t.r)*i,this.g=t.g+(e.g-t.g)*i,this.b=t.b+(e.b-t.b)*i,this}lerpHSL(t,e){this.getHSL(rg),t.getHSL(rx);let i=iN(rg.h,rx.h,e),s=iN(rg.s,rx.s,e),r=iN(rg.l,rx.l,e);return this.setHSL(i,s,r),this}setFromVector3(t){return this.r=t.x,this.g=t.y,this.b=t.z,this}applyMatrix3(t){let e=this.r,i=this.g,s=this.b,r=t.elements;return this.r=r[0]*e+r[3]*i+r[6]*s,this.g=r[1]*e+r[4]*i+r[7]*s,this.b=r[2]*e+r[5]*i+r[8]*s,this}equals(t){return t.r===this.r&&t.g===this.g&&t.b===this.b}fromArray(t,e=0){return this.r=t[e],this.g=t[e+1],this.b=t[e+2],this}toArray(t=[],e=0){return t[e]=this.r,t[e+1]=this.g,t[e+2]=this.b,t}fromBufferAttribute(t,e){return this.r=t.getX(e),this.g=t.getY(e),this.b=t.getZ(e),this}toJSON(){return this.getHex()}*[Symbol.iterator](){yield this.r,yield this.g,yield this.b}}let rw=new rv;rv.NAMES=rf;let rM=0;class rS extends iI{constructor(){super(),this.isMaterial=!0,Object.defineProperty(this,"id",{value:rM++}),this.uuid=iE(),this.name="",this.type="Material",this.blending=S,this.side=b,this.vertexColors=!1,this.opacity=1,this.transparent=!1,this.alphaHash=!1,this.blendSrc=F,this.blendDst=V,this.blendEquation=I,this.blendSrcAlpha=null,this.blendDstAlpha=null,this.blendEquationAlpha=null,this.blendColor=new rv(0,0,0),this.blendAlpha=0,this.depthFunc=$,this.depthTest=!0,this.depthWrite=!0,this.stencilWriteMask=255,this.stencilFunc=is,this.stencilRef=0,this.stencilFuncMask=255,this.stencilFail=eK,this.stencilZFail=eK,this.stencilZPass=eK,this.stencilWrite=!1,this.clippingPlanes=null,this.clipIntersection=!1,this.clipShadows=!1,this.shadowSide=null,this.colorWrite=!0,this.precision=null,this.polygonOffset=!1,this.polygonOffsetFactor=0,this.polygonOffsetUnits=0,this.dithering=!1,this.alphaToCoverage=!1,this.premultipliedAlpha=!1,this.forceSinglePass=!1,this.allowOverride=!0,this.visible=!0,this.toneMapped=!0,this.userData={},this.version=0,this._alphaTest=0}get alphaTest(){return this._alphaTest}set alphaTest(t){this._alphaTest>0!=t>0&&this.version++,this._alphaTest=t}onBeforeRender(){}onBeforeCompile(){}customProgramCacheKey(){return this.onBeforeCompile.toString()}setValues(t){if(void 0!==t)for(let e in t){let i=t[e];if(void 0===i){console.warn(`THREE.Material: parameter '${e}' has value of undefined.`);continue}let s=this[e];if(void 0===s){console.warn(`THREE.Material: '${e}' is not a property of THREE.${this.type}.`);continue}s&&s.isColor?s.set(i):s&&s.isVector3&&i&&i.isVector3?s.copy(i):this[e]=i}}toJSON(t){let e=void 0===t||"string"==typeof t;e&&(t={textures:{},images:{}});let i={metadata:{version:4.6,type:"Material",generator:"Material.toJSON"}};function s(t){let e=[];for(let i in t){let s=t[i];delete s.metadata,e.push(s)}return e}if(i.uuid=this.uuid,i.type=this.type,""!==this.name&&(i.name=this.name),this.color&&this.color.isColor&&(i.color=this.color.getHex()),void 0!==this.roughness&&(i.roughness=this.roughness),void 0!==this.metalness&&(i.metalness=this.metalness),void 0!==this.sheen&&(i.sheen=this.sheen),this.sheenColor&&this.sheenColor.isColor&&(i.sheenColor=this.sheenColor.getHex()),void 0!==this.sheenRoughness&&(i.sheenRoughness=this.sheenRoughness),this.emissive&&this.emissive.isColor&&(i.emissive=this.emissive.getHex()),void 0!==this.emissiveIntensity&&1!==this.emissiveIntensity&&(i.emissiveIntensity=this.emissiveIntensity),this.specular&&this.specular.isColor&&(i.specular=this.specular.getHex()),void 0!==this.specularIntensity&&(i.specularIntensity=this.specularIntensity),this.specularColor&&this.specularColor.isColor&&(i.specularColor=this.specularColor.getHex()),void 0!==this.shininess&&(i.shininess=this.shininess),void 0!==this.clearcoat&&(i.clearcoat=this.clearcoat),void 0!==this.clearcoatRoughness&&(i.clearcoatRoughness=this.clearcoatRoughness),this.clearcoatMap&&this.clearcoatMap.isTexture&&(i.clearcoatMap=this.clearcoatMap.toJSON(t).uuid),this.clearcoatRoughnessMap&&this.clearcoatRoughnessMap.isTexture&&(i.clearcoatRoughnessMap=this.clearcoatRoughnessMap.toJSON(t).uuid),this.clearcoatNormalMap&&this.clearcoatNormalMap.isTexture&&(i.clearcoatNormalMap=this.clearcoatNormalMap.toJSON(t).uuid,i.clearcoatNormalScale=this.clearcoatNormalScale.toArray()),void 0!==this.dispersion&&(i.dispersion=this.dispersion),void 0!==this.iridescence&&(i.iridescence=this.iridescence),void 0!==this.iridescenceIOR&&(i.iridescenceIOR=this.iridescenceIOR),void 0!==this.iridescenceThicknessRange&&(i.iridescenceThicknessRange=this.iridescenceThicknessRange),this.iridescenceMap&&this.iridescenceMap.isTexture&&(i.iridescenceMap=this.iridescenceMap.toJSON(t).uuid),this.iridescenceThicknessMap&&this.iridescenceThicknessMap.isTexture&&(i.iridescenceThicknessMap=this.iridescenceThicknessMap.toJSON(t).uuid),void 0!==this.anisotropy&&(i.anisotropy=this.anisotropy),void 0!==this.anisotropyRotation&&(i.anisotropyRotation=this.anisotropyRotation),this.anisotropyMap&&this.anisotropyMap.isTexture&&(i.anisotropyMap=this.anisotropyMap.toJSON(t).uuid),this.map&&this.map.isTexture&&(i.map=this.map.toJSON(t).uuid),this.matcap&&this.matcap.isTexture&&(i.matcap=this.matcap.toJSON(t).uuid),this.alphaMap&&this.alphaMap.isTexture&&(i.alphaMap=this.alphaMap.toJSON(t).uuid),this.lightMap&&this.lightMap.isTexture&&(i.lightMap=this.lightMap.toJSON(t).uuid,i.lightMapIntensity=this.lightMapIntensity),this.aoMap&&this.aoMap.isTexture&&(i.aoMap=this.aoMap.toJSON(t).uuid,i.aoMapIntensity=this.aoMapIntensity),this.bumpMap&&this.bumpMap.isTexture&&(i.bumpMap=this.bumpMap.toJSON(t).uuid,i.bumpScale=this.bumpScale),this.normalMap&&this.normalMap.isTexture&&(i.normalMap=this.normalMap.toJSON(t).uuid,i.normalMapType=this.normalMapType,i.normalScale=this.normalScale.toArray()),this.displacementMap&&this.displacementMap.isTexture&&(i.displacementMap=this.displacementMap.toJSON(t).uuid,i.displacementScale=this.displacementScale,i.displacementBias=this.displacementBias),this.roughnessMap&&this.roughnessMap.isTexture&&(i.roughnessMap=this.roughnessMap.toJSON(t).uuid),this.metalnessMap&&this.metalnessMap.isTexture&&(i.metalnessMap=this.metalnessMap.toJSON(t).uuid),this.emissiveMap&&this.emissiveMap.isTexture&&(i.emissiveMap=this.emissiveMap.toJSON(t).uuid),this.specularMap&&this.specularMap.isTexture&&(i.specularMap=this.specularMap.toJSON(t).uuid),this.specularIntensityMap&&this.specularIntensityMap.isTexture&&(i.specularIntensityMap=this.specularIntensityMap.toJSON(t).uuid),this.specularColorMap&&this.specularColorMap.isTexture&&(i.specularColorMap=this.specularColorMap.toJSON(t).uuid),this.envMap&&this.envMap.isTexture&&(i.envMap=this.envMap.toJSON(t).uuid,void 0!==this.combine&&(i.combine=this.combine)),void 0!==this.envMapRotation&&(i.envMapRotation=this.envMapRotation.toArray()),void 0!==this.envMapIntensity&&(i.envMapIntensity=this.envMapIntensity),void 0!==this.reflectivity&&(i.reflectivity=this.reflectivity),void 0!==this.refractionRatio&&(i.refractionRatio=this.refractionRatio),this.gradientMap&&this.gradientMap.isTexture&&(i.gradientMap=this.gradientMap.toJSON(t).uuid),void 0!==this.transmission&&(i.transmission=this.transmission),this.transmissionMap&&this.transmissionMap.isTexture&&(i.transmissionMap=this.transmissionMap.toJSON(t).uuid),void 0!==this.thickness&&(i.thickness=this.thickness),this.thicknessMap&&this.thicknessMap.isTexture&&(i.thicknessMap=this.thicknessMap.toJSON(t).uuid),void 0!==this.attenuationDistance&&this.attenuationDistance!==1/0&&(i.attenuationDistance=this.attenuationDistance),void 0!==this.attenuationColor&&(i.attenuationColor=this.attenuationColor.getHex()),void 0!==this.size&&(i.size=this.size),null!==this.shadowSide&&(i.shadowSide=this.shadowSide),void 0!==this.sizeAttenuation&&(i.sizeAttenuation=this.sizeAttenuation),this.blending!==S&&(i.blending=this.blending),this.side!==b&&(i.side=this.side),!0===this.vertexColors&&(i.vertexColors=!0),this.opacity<1&&(i.opacity=this.opacity),!0===this.transparent&&(i.transparent=!0),this.blendSrc!==F&&(i.blendSrc=this.blendSrc),this.blendDst!==V&&(i.blendDst=this.blendDst),this.blendEquation!==I&&(i.blendEquation=this.blendEquation),null!==this.blendSrcAlpha&&(i.blendSrcAlpha=this.blendSrcAlpha),null!==this.blendDstAlpha&&(i.blendDstAlpha=this.blendDstAlpha),null!==this.blendEquationAlpha&&(i.blendEquationAlpha=this.blendEquationAlpha),this.blendColor&&this.blendColor.isColor&&(i.blendColor=this.blendColor.getHex()),0!==this.blendAlpha&&(i.blendAlpha=this.blendAlpha),this.depthFunc!==$&&(i.depthFunc=this.depthFunc),!1===this.depthTest&&(i.depthTest=this.depthTest),!1===this.depthWrite&&(i.depthWrite=this.depthWrite),!1===this.colorWrite&&(i.colorWrite=this.colorWrite),255!==this.stencilWriteMask&&(i.stencilWriteMask=this.stencilWriteMask),this.stencilFunc!==is&&(i.stencilFunc=this.stencilFunc),0!==this.stencilRef&&(i.stencilRef=this.stencilRef),255!==this.stencilFuncMask&&(i.stencilFuncMask=this.stencilFuncMask),this.stencilFail!==eK&&(i.stencilFail=this.stencilFail),this.stencilZFail!==eK&&(i.stencilZFail=this.stencilZFail),this.stencilZPass!==eK&&(i.stencilZPass=this.stencilZPass),!0===this.stencilWrite&&(i.stencilWrite=this.stencilWrite),void 0!==this.rotation&&0!==this.rotation&&(i.rotation=this.rotation),!0===this.polygonOffset&&(i.polygonOffset=!0),0!==this.polygonOffsetFactor&&(i.polygonOffsetFactor=this.polygonOffsetFactor),0!==this.polygonOffsetUnits&&(i.polygonOffsetUnits=this.polygonOffsetUnits),void 0!==this.linewidth&&1!==this.linewidth&&(i.linewidth=this.linewidth),void 0!==this.dashSize&&(i.dashSize=this.dashSize),void 0!==this.gapSize&&(i.gapSize=this.gapSize),void 0!==this.scale&&(i.scale=this.scale),!0===this.dithering&&(i.dithering=!0),this.alphaTest>0&&(i.alphaTest=this.alphaTest),!0===this.alphaHash&&(i.alphaHash=!0),!0===this.alphaToCoverage&&(i.alphaToCoverage=!0),!0===this.premultipliedAlpha&&(i.premultipliedAlpha=!0),!0===this.forceSinglePass&&(i.forceSinglePass=!0),!0===this.wireframe&&(i.wireframe=!0),this.wireframeLinewidth>1&&(i.wireframeLinewidth=this.wireframeLinewidth),"round"!==this.wireframeLinecap&&(i.wireframeLinecap=this.wireframeLinecap),"round"!==this.wireframeLinejoin&&(i.wireframeLinejoin=this.wireframeLinejoin),!0===this.flatShading&&(i.flatShading=!0),!1===this.visible&&(i.visible=!1),!1===this.toneMapped&&(i.toneMapped=!1),!1===this.fog&&(i.fog=!1),Object.keys(this.userData).length>0&&(i.userData=this.userData),e){let e=s(t.textures),r=s(t.images);e.length>0&&(i.textures=e),r.length>0&&(i.images=r)}return i}clone(){return new this.constructor().copy(this)}copy(t){this.name=t.name,this.blending=t.blending,this.side=t.side,this.vertexColors=t.vertexColors,this.opacity=t.opacity,this.transparent=t.transparent,this.blendSrc=t.blendSrc,this.blendDst=t.blendDst,this.blendEquation=t.blendEquation,this.blendSrcAlpha=t.blendSrcAlpha,this.blendDstAlpha=t.blendDstAlpha,this.blendEquationAlpha=t.blendEquationAlpha,this.blendColor.copy(t.blendColor),this.blendAlpha=t.blendAlpha,this.depthFunc=t.depthFunc,this.depthTest=t.depthTest,this.depthWrite=t.depthWrite,this.stencilWriteMask=t.stencilWriteMask,this.stencilFunc=t.stencilFunc,this.stencilRef=t.stencilRef,this.stencilFuncMask=t.stencilFuncMask,this.stencilFail=t.stencilFail,this.stencilZFail=t.stencilZFail,this.stencilZPass=t.stencilZPass,this.stencilWrite=t.stencilWrite;let e=t.clippingPlanes,i=null;if(null!==e){let t=e.length;i=Array(t);for(let s=0;s!==t;++s)i[s]=e[s].clone()}return this.clippingPlanes=i,this.clipIntersection=t.clipIntersection,this.clipShadows=t.clipShadows,this.shadowSide=t.shadowSide,this.colorWrite=t.colorWrite,this.precision=t.precision,this.polygonOffset=t.polygonOffset,this.polygonOffsetFactor=t.polygonOffsetFactor,this.polygonOffsetUnits=t.polygonOffsetUnits,this.dithering=t.dithering,this.alphaTest=t.alphaTest,this.alphaHash=t.alphaHash,this.alphaToCoverage=t.alphaToCoverage,this.premultipliedAlpha=t.premultipliedAlpha,this.forceSinglePass=t.forceSinglePass,this.visible=t.visible,this.toneMapped=t.toneMapped,this.userData=JSON.parse(JSON.stringify(t.userData)),this}dispose(){this.dispatchEvent({type:"dispose"})}set needsUpdate(t){!0===t&&this.version++}onBuild(){console.warn("Material: onBuild() has been removed.")}}class r_ extends rS{constructor(t){super(),this.isMeshBasicMaterial=!0,this.type="MeshBasicMaterial",this.color=new rv(0xffffff),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new sZ,this.combine=ti,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.fog=t.fog,this}}let rA=function(){let t=new ArrayBuffer(4),e=new Float32Array(t),i=new Uint32Array(t),s=new Uint32Array(512),r=new Uint32Array(512);for(let t=0;t<256;++t){let e=t-127;e<-27?(s[t]=0,s[256|t]=32768,r[t]=24,r[256|t]=24):e<-14?(s[t]=1024>>-e-14,s[256|t]=1024>>-e-14|32768,r[t]=-e-1,r[256|t]=-e-1):e<=15?(s[t]=e+15<<10,s[256|t]=e+15<<10|32768,r[t]=13,r[256|t]=13):e<128?(s[t]=31744,s[256|t]=64512,r[t]=24,r[256|t]=24):(s[t]=31744,s[256|t]=64512,r[t]=13,r[256|t]=13)}let n=new Uint32Array(2048),a=new Uint32Array(64),o=new Uint32Array(64);for(let t=1;t<1024;++t){let e=t<<13,i=0;for(;(8388608&e)==0;)e<<=1,i-=8388608;e&=-8388609,i+=0x38800000,n[t]=e|i}for(let t=1024;t<2048;++t)n[t]=0x38000000+(t-1024<<13);for(let t=1;t<31;++t)a[t]=t<<23;a[31]=0x47800000,a[32]=0x80000000;for(let t=33;t<63;++t)a[t]=0x80000000+(t-32<<23);a[63]=0xc7800000;for(let t=1;t<64;++t)32!==t&&(o[t]=1024);return{floatView:e,uint32View:i,baseTable:s,shiftTable:r,mantissaTable:n,exponentTable:a,offsetTable:o}}();function rT(t){Math.abs(t)>65504&&console.warn("THREE.DataUtils.toHalfFloat(): Value out of range."),t=iP(t,-65504,65504),rA.floatView[0]=t;let e=rA.uint32View[0],i=e>>23&511;return rA.baseTable[i]+((8388607&e)>>rA.shiftTable[i])}function rz(t){let e=t>>10;return rA.uint32View[0]=rA.mantissaTable[rA.offsetTable[e]+(1023&t)]+rA.exponentTable[e],rA.floatView[0]}class rI{static toHalfFloat(t){return rT(t)}static fromHalfFloat(t){return rz(t)}}let rC=new sh,rk=new ij,rB=0;class rR{constructor(t,e,i=!1){if(Array.isArray(t))throw TypeError("THREE.BufferAttribute: array should be a Typed Array.");this.isBufferAttribute=!0,Object.defineProperty(this,"id",{value:rB++}),this.name="",this.array=t,this.itemSize=e,this.count=void 0!==t?t.length/e:0,this.normalized=i,this.usage=ip,this.updateRanges=[],this.gpuType=tj,this.version=0}onUploadCallback(){}set needsUpdate(t){!0===t&&this.version++}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.name=t.name,this.array=new t.array.constructor(t.array),this.itemSize=t.itemSize,this.count=t.count,this.normalized=t.normalized,this.usage=t.usage,this.gpuType=t.gpuType,this}copyAt(t,e,i){t*=this.itemSize,i*=e.itemSize;for(let s=0,r=this.itemSize;s<r;s++)this.array[t+s]=e.array[i+s];return this}copyArray(t){return this.array.set(t),this}applyMatrix3(t){if(2===this.itemSize)for(let e=0,i=this.count;e<i;e++)rk.fromBufferAttribute(this,e),rk.applyMatrix3(t),this.setXY(e,rk.x,rk.y);else if(3===this.itemSize)for(let e=0,i=this.count;e<i;e++)rC.fromBufferAttribute(this,e),rC.applyMatrix3(t),this.setXYZ(e,rC.x,rC.y,rC.z);return this}applyMatrix4(t){for(let e=0,i=this.count;e<i;e++)rC.fromBufferAttribute(this,e),rC.applyMatrix4(t),this.setXYZ(e,rC.x,rC.y,rC.z);return this}applyNormalMatrix(t){for(let e=0,i=this.count;e<i;e++)rC.fromBufferAttribute(this,e),rC.applyNormalMatrix(t),this.setXYZ(e,rC.x,rC.y,rC.z);return this}transformDirection(t){for(let e=0,i=this.count;e<i;e++)rC.fromBufferAttribute(this,e),rC.transformDirection(t),this.setXYZ(e,rC.x,rC.y,rC.z);return this}set(t,e=0){return this.array.set(t,e),this}getComponent(t,e){let i=this.array[t*this.itemSize+e];return this.normalized&&(i=iF(i,this.array)),i}setComponent(t,e,i){return this.normalized&&(i=iV(i,this.array)),this.array[t*this.itemSize+e]=i,this}getX(t){let e=this.array[t*this.itemSize];return this.normalized&&(e=iF(e,this.array)),e}setX(t,e){return this.normalized&&(e=iV(e,this.array)),this.array[t*this.itemSize]=e,this}getY(t){let e=this.array[t*this.itemSize+1];return this.normalized&&(e=iF(e,this.array)),e}setY(t,e){return this.normalized&&(e=iV(e,this.array)),this.array[t*this.itemSize+1]=e,this}getZ(t){let e=this.array[t*this.itemSize+2];return this.normalized&&(e=iF(e,this.array)),e}setZ(t,e){return this.normalized&&(e=iV(e,this.array)),this.array[t*this.itemSize+2]=e,this}getW(t){let e=this.array[t*this.itemSize+3];return this.normalized&&(e=iF(e,this.array)),e}setW(t,e){return this.normalized&&(e=iV(e,this.array)),this.array[t*this.itemSize+3]=e,this}setXY(t,e,i){return t*=this.itemSize,this.normalized&&(e=iV(e,this.array),i=iV(i,this.array)),this.array[t+0]=e,this.array[t+1]=i,this}setXYZ(t,e,i,s){return t*=this.itemSize,this.normalized&&(e=iV(e,this.array),i=iV(i,this.array),s=iV(s,this.array)),this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=s,this}setXYZW(t,e,i,s,r){return t*=this.itemSize,this.normalized&&(e=iV(e,this.array),i=iV(i,this.array),s=iV(s,this.array),r=iV(r,this.array)),this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=s,this.array[t+3]=r,this}onUpload(t){return this.onUploadCallback=t,this}clone(){return new this.constructor(this.array,this.itemSize).copy(this)}toJSON(){let t={itemSize:this.itemSize,type:this.array.constructor.name,array:Array.from(this.array),normalized:this.normalized};return""!==this.name&&(t.name=this.name),this.usage!==ip&&(t.usage=this.usage),t}}class rE extends rR{constructor(t,e,i){super(new Int8Array(t),e,i)}}class rP extends rR{constructor(t,e,i){super(new Uint8Array(t),e,i)}}class rO extends rR{constructor(t,e,i){super(new Uint8ClampedArray(t),e,i)}}class rN extends rR{constructor(t,e,i){super(new Int16Array(t),e,i)}}class rF extends rR{constructor(t,e,i){super(new Uint16Array(t),e,i)}}class rV extends rR{constructor(t,e,i){super(new Int32Array(t),e,i)}}class rL extends rR{constructor(t,e,i){super(new Uint32Array(t),e,i)}}class rj extends rR{constructor(t,e,i){super(new Uint16Array(t),e,i),this.isFloat16BufferAttribute=!0}getX(t){let e=rz(this.array[t*this.itemSize]);return this.normalized&&(e=iF(e,this.array)),e}setX(t,e){return this.normalized&&(e=iV(e,this.array)),this.array[t*this.itemSize]=rT(e),this}getY(t){let e=rz(this.array[t*this.itemSize+1]);return this.normalized&&(e=iF(e,this.array)),e}setY(t,e){return this.normalized&&(e=iV(e,this.array)),this.array[t*this.itemSize+1]=rT(e),this}getZ(t){let e=rz(this.array[t*this.itemSize+2]);return this.normalized&&(e=iF(e,this.array)),e}setZ(t,e){return this.normalized&&(e=iV(e,this.array)),this.array[t*this.itemSize+2]=rT(e),this}getW(t){let e=rz(this.array[t*this.itemSize+3]);return this.normalized&&(e=iF(e,this.array)),e}setW(t,e){return this.normalized&&(e=iV(e,this.array)),this.array[t*this.itemSize+3]=rT(e),this}setXY(t,e,i){return t*=this.itemSize,this.normalized&&(e=iV(e,this.array),i=iV(i,this.array)),this.array[t+0]=rT(e),this.array[t+1]=rT(i),this}setXYZ(t,e,i,s){return t*=this.itemSize,this.normalized&&(e=iV(e,this.array),i=iV(i,this.array),s=iV(s,this.array)),this.array[t+0]=rT(e),this.array[t+1]=rT(i),this.array[t+2]=rT(s),this}setXYZW(t,e,i,s,r){return t*=this.itemSize,this.normalized&&(e=iV(e,this.array),i=iV(i,this.array),s=iV(s,this.array),r=iV(r,this.array)),this.array[t+0]=rT(e),this.array[t+1]=rT(i),this.array[t+2]=rT(s),this.array[t+3]=rT(r),this}}class rU extends rR{constructor(t,e,i){super(new Float32Array(t),e,i)}}let rW=0,rD=new sV,rH=new re,rq=new sh,rJ=new sc,rX=new sc,rZ=new sh;class rY extends iI{constructor(){super(),this.isBufferGeometry=!0,Object.defineProperty(this,"id",{value:rW++}),this.uuid=iE(),this.name="",this.type="BufferGeometry",this.index=null,this.indirect=null,this.attributes={},this.morphAttributes={},this.morphTargetsRelative=!1,this.groups=[],this.boundingBox=null,this.boundingSphere=null,this.drawRange={start:0,count:1/0},this.userData={}}getIndex(){return this.index}setIndex(t){return Array.isArray(t)?this.index=new(iD(t)?rL:rF)(t,1):this.index=t,this}setIndirect(t){return this.indirect=t,this}getIndirect(){return this.indirect}getAttribute(t){return this.attributes[t]}setAttribute(t,e){return this.attributes[t]=e,this}deleteAttribute(t){return delete this.attributes[t],this}hasAttribute(t){return void 0!==this.attributes[t]}addGroup(t,e,i=0){this.groups.push({start:t,count:e,materialIndex:i})}clearGroups(){this.groups=[]}setDrawRange(t,e){this.drawRange.start=t,this.drawRange.count=e}applyMatrix4(t){let e=this.attributes.position;void 0!==e&&(e.applyMatrix4(t),e.needsUpdate=!0);let i=this.attributes.normal;if(void 0!==i){let e=new iU().getNormalMatrix(t);i.applyNormalMatrix(e),i.needsUpdate=!0}let s=this.attributes.tangent;return void 0!==s&&(s.transformDirection(t),s.needsUpdate=!0),null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this}applyQuaternion(t){return rD.makeRotationFromQuaternion(t),this.applyMatrix4(rD),this}rotateX(t){return rD.makeRotationX(t),this.applyMatrix4(rD),this}rotateY(t){return rD.makeRotationY(t),this.applyMatrix4(rD),this}rotateZ(t){return rD.makeRotationZ(t),this.applyMatrix4(rD),this}translate(t,e,i){return rD.makeTranslation(t,e,i),this.applyMatrix4(rD),this}scale(t,e,i){return rD.makeScale(t,e,i),this.applyMatrix4(rD),this}lookAt(t){return rH.lookAt(t),rH.updateMatrix(),this.applyMatrix4(rH.matrix),this}center(){return this.computeBoundingBox(),this.boundingBox.getCenter(rq).negate(),this.translate(rq.x,rq.y,rq.z),this}setFromPoints(t){let e=this.getAttribute("position");if(void 0===e){let e=[];for(let i=0,s=t.length;i<s;i++){let s=t[i];e.push(s.x,s.y,s.z||0)}this.setAttribute("position",new rU(e,3))}else{let i=Math.min(t.length,e.count);for(let s=0;s<i;s++){let i=t[s];e.setXYZ(s,i.x,i.y,i.z||0)}t.length>e.count&&console.warn("THREE.BufferGeometry: Buffer size too small for points data. Use .dispose() and create a new geometry."),e.needsUpdate=!0}return this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new sc);let t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute){console.error("THREE.BufferGeometry.computeBoundingBox(): GLBufferAttribute requires a manual bounding box.",this),this.boundingBox.set(new sh(-1/0,-1/0,-1/0),new sh(Infinity,Infinity,Infinity));return}if(void 0!==t){if(this.boundingBox.setFromBufferAttribute(t),e)for(let t=0,i=e.length;t<i;t++){let i=e[t];rJ.setFromBufferAttribute(i),this.morphTargetsRelative?(rZ.addVectors(this.boundingBox.min,rJ.min),this.boundingBox.expandByPoint(rZ),rZ.addVectors(this.boundingBox.max,rJ.max),this.boundingBox.expandByPoint(rZ)):(this.boundingBox.expandByPoint(rJ.min),this.boundingBox.expandByPoint(rJ.max))}}else this.boundingBox.makeEmpty();(isNaN(this.boundingBox.min.x)||isNaN(this.boundingBox.min.y)||isNaN(this.boundingBox.min.z))&&console.error('THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values. The "position" attribute is likely to have NaN values.',this)}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new sC);let t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute){console.error("THREE.BufferGeometry.computeBoundingSphere(): GLBufferAttribute requires a manual bounding sphere.",this),this.boundingSphere.set(new sh,1/0);return}if(t){let i=this.boundingSphere.center;if(rJ.setFromBufferAttribute(t),e)for(let t=0,i=e.length;t<i;t++){let i=e[t];rX.setFromBufferAttribute(i),this.morphTargetsRelative?(rZ.addVectors(rJ.min,rX.min),rJ.expandByPoint(rZ),rZ.addVectors(rJ.max,rX.max),rJ.expandByPoint(rZ)):(rJ.expandByPoint(rX.min),rJ.expandByPoint(rX.max))}rJ.getCenter(i);let s=0;for(let e=0,r=t.count;e<r;e++)rZ.fromBufferAttribute(t,e),s=Math.max(s,i.distanceToSquared(rZ));if(e)for(let r=0,n=e.length;r<n;r++){let n=e[r],a=this.morphTargetsRelative;for(let e=0,r=n.count;e<r;e++)rZ.fromBufferAttribute(n,e),a&&(rq.fromBufferAttribute(t,e),rZ.add(rq)),s=Math.max(s,i.distanceToSquared(rZ))}this.boundingSphere.radius=Math.sqrt(s),isNaN(this.boundingSphere.radius)&&console.error('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.',this)}}computeTangents(){let t=this.index,e=this.attributes;if(null===t||void 0===e.position||void 0===e.normal||void 0===e.uv)return void console.error("THREE.BufferGeometry: .computeTangents() failed. Missing required attributes (index, position, normal or uv)");let i=e.position,s=e.normal,r=e.uv;!1===this.hasAttribute("tangent")&&this.setAttribute("tangent",new rR(new Float32Array(4*i.count),4));let n=this.getAttribute("tangent"),a=[],o=[];for(let t=0;t<i.count;t++)a[t]=new sh,o[t]=new sh;let h=new sh,l=new sh,u=new sh,c=new ij,d=new ij,p=new ij,m=new sh,y=new sh,f=this.groups;0===f.length&&(f=[{start:0,count:t.count}]);for(let e=0,s=f.length;e<s;++e){let s=f[e],n=s.start,g=s.count;for(let e=n,s=n+g;e<s;e+=3)!function(t,e,s){h.fromBufferAttribute(i,t),l.fromBufferAttribute(i,e),u.fromBufferAttribute(i,s),c.fromBufferAttribute(r,t),d.fromBufferAttribute(r,e),p.fromBufferAttribute(r,s),l.sub(h),u.sub(h),d.sub(c),p.sub(c);let n=1/(d.x*p.y-p.x*d.y);isFinite(n)&&(m.copy(l).multiplyScalar(p.y).addScaledVector(u,-d.y).multiplyScalar(n),y.copy(u).multiplyScalar(d.x).addScaledVector(l,-p.x).multiplyScalar(n),a[t].add(m),a[e].add(m),a[s].add(m),o[t].add(y),o[e].add(y),o[s].add(y))}(t.getX(e+0),t.getX(e+1),t.getX(e+2))}let g=new sh,x=new sh,b=new sh,v=new sh;function w(t){b.fromBufferAttribute(s,t),v.copy(b);let e=a[t];g.copy(e),g.sub(b.multiplyScalar(b.dot(e))).normalize(),x.crossVectors(v,e);let i=x.dot(o[t]);n.setXYZW(t,g.x,g.y,g.z,i<0?-1:1)}for(let e=0,i=f.length;e<i;++e){let i=f[e],s=i.start,r=i.count;for(let e=s,i=s+r;e<i;e+=3)w(t.getX(e+0)),w(t.getX(e+1)),w(t.getX(e+2))}}computeVertexNormals(){let t=this.index,e=this.getAttribute("position");if(void 0!==e){let i=this.getAttribute("normal");if(void 0===i)i=new rR(new Float32Array(3*e.count),3),this.setAttribute("normal",i);else for(let t=0,e=i.count;t<e;t++)i.setXYZ(t,0,0,0);let s=new sh,r=new sh,n=new sh,a=new sh,o=new sh,h=new sh,l=new sh,u=new sh;if(t)for(let c=0,d=t.count;c<d;c+=3){let d=t.getX(c+0),p=t.getX(c+1),m=t.getX(c+2);s.fromBufferAttribute(e,d),r.fromBufferAttribute(e,p),n.fromBufferAttribute(e,m),l.subVectors(n,r),u.subVectors(s,r),l.cross(u),a.fromBufferAttribute(i,d),o.fromBufferAttribute(i,p),h.fromBufferAttribute(i,m),a.add(l),o.add(l),h.add(l),i.setXYZ(d,a.x,a.y,a.z),i.setXYZ(p,o.x,o.y,o.z),i.setXYZ(m,h.x,h.y,h.z)}else for(let t=0,a=e.count;t<a;t+=3)s.fromBufferAttribute(e,t+0),r.fromBufferAttribute(e,t+1),n.fromBufferAttribute(e,t+2),l.subVectors(n,r),u.subVectors(s,r),l.cross(u),i.setXYZ(t+0,l.x,l.y,l.z),i.setXYZ(t+1,l.x,l.y,l.z),i.setXYZ(t+2,l.x,l.y,l.z);this.normalizeNormals(),i.needsUpdate=!0}}normalizeNormals(){let t=this.attributes.normal;for(let e=0,i=t.count;e<i;e++)rZ.fromBufferAttribute(t,e),rZ.normalize(),t.setXYZ(e,rZ.x,rZ.y,rZ.z)}toNonIndexed(){function t(t,e){let i=t.array,s=t.itemSize,r=t.normalized,n=new i.constructor(e.length*s),a=0,o=0;for(let r=0,h=e.length;r<h;r++){a=t.isInterleavedBufferAttribute?e[r]*t.data.stride+t.offset:e[r]*s;for(let t=0;t<s;t++)n[o++]=i[a++]}return new rR(n,s,r)}if(null===this.index)return console.warn("THREE.BufferGeometry.toNonIndexed(): BufferGeometry is already non-indexed."),this;let e=new rY,i=this.index.array,s=this.attributes;for(let r in s){let n=t(s[r],i);e.setAttribute(r,n)}let r=this.morphAttributes;for(let s in r){let n=[],a=r[s];for(let e=0,s=a.length;e<s;e++){let s=t(a[e],i);n.push(s)}e.morphAttributes[s]=n}e.morphTargetsRelative=this.morphTargetsRelative;let n=this.groups;for(let t=0,i=n.length;t<i;t++){let i=n[t];e.addGroup(i.start,i.count,i.materialIndex)}return e}toJSON(){let t={metadata:{version:4.6,type:"BufferGeometry",generator:"BufferGeometry.toJSON"}};if(t.uuid=this.uuid,t.type=this.type,""!==this.name&&(t.name=this.name),Object.keys(this.userData).length>0&&(t.userData=this.userData),void 0!==this.parameters){let e=this.parameters;for(let i in e)void 0!==e[i]&&(t[i]=e[i]);return t}t.data={attributes:{}};let e=this.index;null!==e&&(t.data.index={type:e.array.constructor.name,array:Array.prototype.slice.call(e.array)});let i=this.attributes;for(let e in i){let s=i[e];t.data.attributes[e]=s.toJSON(t.data)}let s={},r=!1;for(let e in this.morphAttributes){let i=this.morphAttributes[e],n=[];for(let e=0,s=i.length;e<s;e++){let s=i[e];n.push(s.toJSON(t.data))}n.length>0&&(s[e]=n,r=!0)}r&&(t.data.morphAttributes=s,t.data.morphTargetsRelative=this.morphTargetsRelative);let n=this.groups;n.length>0&&(t.data.groups=JSON.parse(JSON.stringify(n)));let a=this.boundingSphere;return null!==a&&(t.data.boundingSphere={center:a.center.toArray(),radius:a.radius}),t}clone(){return new this.constructor().copy(this)}copy(t){this.index=null,this.attributes={},this.morphAttributes={},this.groups=[],this.boundingBox=null,this.boundingSphere=null;let e={};this.name=t.name;let i=t.index;null!==i&&this.setIndex(i.clone());let s=t.attributes;for(let t in s){let i=s[t];this.setAttribute(t,i.clone(e))}let r=t.morphAttributes;for(let t in r){let i=[],s=r[t];for(let t=0,r=s.length;t<r;t++)i.push(s[t].clone(e));this.morphAttributes[t]=i}this.morphTargetsRelative=t.morphTargetsRelative;let n=t.groups;for(let t=0,e=n.length;t<e;t++){let e=n[t];this.addGroup(e.start,e.count,e.materialIndex)}let a=t.boundingBox;null!==a&&(this.boundingBox=a.clone());let o=t.boundingSphere;return null!==o&&(this.boundingSphere=o.clone()),this.drawRange.start=t.drawRange.start,this.drawRange.count=t.drawRange.count,this.userData=t.userData,this}dispose(){this.dispatchEvent({type:"dispose"})}}let rG=new sV,r$=new sF,rQ=new sC,rK=new sh,r0=new sh,r1=new sh,r2=new sh,r3=new sh,r5=new sh,r4=new sh,r6=new sh;class r8 extends re{constructor(t=new rY,e=new r_){super(),this.isMesh=!0,this.type="Mesh",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),void 0!==t.morphTargetInfluences&&(this.morphTargetInfluences=t.morphTargetInfluences.slice()),void 0!==t.morphTargetDictionary&&(this.morphTargetDictionary=Object.assign({},t.morphTargetDictionary)),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}updateMorphTargets(){let t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){let i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){let e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}getVertexPosition(t,e){let i=this.geometry,s=i.attributes.position,r=i.morphAttributes.position,n=i.morphTargetsRelative;e.fromBufferAttribute(s,t);let a=this.morphTargetInfluences;if(r&&a){r5.set(0,0,0);for(let i=0,s=r.length;i<s;i++){let s=a[i],o=r[i];0!==s&&(r3.fromBufferAttribute(o,t),n?r5.addScaledVector(r3,s):r5.addScaledVector(r3.sub(e),s))}e.add(r5)}return e}raycast(t,e){let i=this.geometry,s=this.material,r=this.matrixWorld;if(void 0!==s)null===i.boundingSphere&&i.computeBoundingSphere(),rQ.copy(i.boundingSphere),rQ.applyMatrix4(r),r$.copy(t.ray).recast(t.near),!1===rQ.containsPoint(r$.origin)&&(null===r$.intersectSphere(rQ,rK)||r$.origin.distanceToSquared(rK)>(t.far-t.near)**2)||(rG.copy(r).invert(),r$.copy(t.ray).applyMatrix4(rG),(null===i.boundingBox||!1!==r$.intersectsBox(i.boundingBox))&&this._computeIntersections(t,e,r$))}_computeIntersections(t,e,i){let s,r=this.geometry,n=this.material,a=r.index,o=r.attributes.position,h=r.attributes.uv,l=r.attributes.uv1,u=r.attributes.normal,c=r.groups,d=r.drawRange;if(null!==a)if(Array.isArray(n))for(let r=0,o=c.length;r<o;r++){let o=c[r],p=n[o.materialIndex],m=Math.max(o.start,d.start),y=Math.min(a.count,Math.min(o.start+o.count,d.start+d.count));for(let r=m;r<y;r+=3){let n=a.getX(r);(s=r9(this,p,t,i,h,l,u,n,a.getX(r+1),a.getX(r+2)))&&(s.faceIndex=Math.floor(r/3),s.face.materialIndex=o.materialIndex,e.push(s))}}else{let r=Math.max(0,d.start),o=Math.min(a.count,d.start+d.count);for(let c=r;c<o;c+=3){let r=a.getX(c);(s=r9(this,n,t,i,h,l,u,r,a.getX(c+1),a.getX(c+2)))&&(s.faceIndex=Math.floor(c/3),e.push(s))}}else if(void 0!==o)if(Array.isArray(n))for(let r=0,a=c.length;r<a;r++){let a=c[r],p=n[a.materialIndex],m=Math.max(a.start,d.start),y=Math.min(o.count,Math.min(a.start+a.count,d.start+d.count));for(let r=m;r<y;r+=3)(s=r9(this,p,t,i,h,l,u,r,r+1,r+2))&&(s.faceIndex=Math.floor(r/3),s.face.materialIndex=a.materialIndex,e.push(s))}else{let r=Math.max(0,d.start),a=Math.min(o.count,d.start+d.count);for(let o=r;o<a;o+=3)(s=r9(this,n,t,i,h,l,u,o,o+1,o+2))&&(s.faceIndex=Math.floor(o/3),e.push(s))}}}function r9(t,e,i,s,r,n,a,o,h,l){t.getVertexPosition(o,r0),t.getVertexPosition(h,r1),t.getVertexPosition(l,r2);let u=function(t,e,i,s,r,n,a,o){let h;if(null===(e.side===v?s.intersectTriangle(a,n,r,!0,o):s.intersectTriangle(r,n,a,e.side===b,o)))return null;r6.copy(o),r6.applyMatrix4(t.matrixWorld);let l=i.ray.origin.distanceTo(r6);return l<i.near||l>i.far?null:{distance:l,point:r6.clone(),object:t}}(t,e,i,s,r0,r1,r2,r4);if(u){let t=new sh;ry.getBarycoord(r4,r0,r1,r2,t),r&&(u.uv=ry.getInterpolatedAttribute(r,o,h,l,t,new ij)),n&&(u.uv1=ry.getInterpolatedAttribute(n,o,h,l,t,new ij)),a&&(u.normal=ry.getInterpolatedAttribute(a,o,h,l,t,new sh),u.normal.dot(s.direction)>0&&u.normal.multiplyScalar(-1));let e={a:o,b:h,c:l,normal:new sh,materialIndex:0};ry.getNormal(r0,r1,r2,e.normal),u.face=e,u.barycoord=t}return u}class r7 extends rY{constructor(t=1,e=1,i=1,s=1,r=1,n=1){super(),this.type="BoxGeometry",this.parameters={width:t,height:e,depth:i,widthSegments:s,heightSegments:r,depthSegments:n};let a=this;s=Math.floor(s),r=Math.floor(r);let o=[],h=[],l=[],u=[],c=0,d=0;function p(t,e,i,s,r,n,p,m,y,f,g){let x=n/y,b=p/f,v=n/2,w=p/2,M=m/2,S=y+1,_=f+1,A=0,T=0,z=new sh;for(let n=0;n<_;n++){let a=n*b-w;for(let o=0;o<S;o++){let c=o*x-v;z[t]=c*s,z[e]=a*r,z[i]=M,h.push(z.x,z.y,z.z),z[t]=0,z[e]=0,z[i]=m>0?1:-1,l.push(z.x,z.y,z.z),u.push(o/y),u.push(1-n/f),A+=1}}for(let t=0;t<f;t++)for(let e=0;e<y;e++){let i=c+e+S*t,s=c+e+S*(t+1),r=c+(e+1)+S*(t+1),n=c+(e+1)+S*t;o.push(i,s,n),o.push(s,r,n),T+=6}a.addGroup(d,T,g),d+=T,c+=A}p("z","y","x",-1,-1,i,e,t,n=Math.floor(n),r,0),p("z","y","x",1,-1,i,e,-t,n,r,1),p("x","z","y",1,1,t,i,e,s,n,2),p("x","z","y",1,-1,t,i,-e,s,n,3),p("x","y","z",1,-1,t,e,i,s,r,4),p("x","y","z",-1,-1,t,e,-i,s,r,5),this.setIndex(o),this.setAttribute("position",new rU(h,3)),this.setAttribute("normal",new rU(l,3)),this.setAttribute("uv",new rU(u,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new r7(t.width,t.height,t.depth,t.widthSegments,t.heightSegments,t.depthSegments)}}function nt(t){let e={};for(let i in t)for(let s in e[i]={},t[i]){let r=t[i][s];r&&(r.isColor||r.isMatrix3||r.isMatrix4||r.isVector2||r.isVector3||r.isVector4||r.isTexture||r.isQuaternion)?r.isRenderTargetTexture?(console.warn("UniformsUtils: Textures of render targets cannot be cloned via cloneUniforms() or mergeUniforms()."),e[i][s]=null):e[i][s]=r.clone():Array.isArray(r)?e[i][s]=r.slice():e[i][s]=r}return e}function ne(t){let e={};for(let i=0;i<t.length;i++){let s=nt(t[i]);for(let t in s)e[t]=s[t]}return e}function ni(t){let e=t.getRenderTarget();return null===e?t.outputColorSpace:!0===e.isXRRenderTarget?e.texture.colorSpace:i1.workingColorSpace}let ns={clone:nt,merge:ne};class nr extends rS{constructor(t){super(),this.isShaderMaterial=!0,this.type="ShaderMaterial",this.defines={},this.uniforms={},this.uniformsGroups=[],this.vertexShader="void main() {\n	gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}",this.fragmentShader="void main() {\n	gl_FragColor = vec4( 1.0, 0.0, 0.0, 1.0 );\n}",this.linewidth=1,this.wireframe=!1,this.wireframeLinewidth=1,this.fog=!1,this.lights=!1,this.clipping=!1,this.forceSinglePass=!0,this.extensions={clipCullDistance:!1,multiDraw:!1},this.defaultAttributeValues={color:[1,1,1],uv:[0,0],uv1:[0,0]},this.index0AttributeName=void 0,this.uniformsNeedUpdate=!1,this.glslVersion=null,void 0!==t&&this.setValues(t)}copy(t){return super.copy(t),this.fragmentShader=t.fragmentShader,this.vertexShader=t.vertexShader,this.uniforms=nt(t.uniforms),this.uniformsGroups=function(t){let e=[];for(let i=0;i<t.length;i++)e.push(t[i].clone());return e}(t.uniformsGroups),this.defines=Object.assign({},t.defines),this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.fog=t.fog,this.lights=t.lights,this.clipping=t.clipping,this.extensions=Object.assign({},t.extensions),this.glslVersion=t.glslVersion,this}toJSON(t){let e=super.toJSON(t);for(let i in e.glslVersion=this.glslVersion,e.uniforms={},this.uniforms){let s=this.uniforms[i].value;s&&s.isTexture?e.uniforms[i]={type:"t",value:s.toJSON(t).uuid}:s&&s.isColor?e.uniforms[i]={type:"c",value:s.getHex()}:s&&s.isVector2?e.uniforms[i]={type:"v2",value:s.toArray()}:s&&s.isVector3?e.uniforms[i]={type:"v3",value:s.toArray()}:s&&s.isVector4?e.uniforms[i]={type:"v4",value:s.toArray()}:s&&s.isMatrix3?e.uniforms[i]={type:"m3",value:s.toArray()}:s&&s.isMatrix4?e.uniforms[i]={type:"m4",value:s.toArray()}:e.uniforms[i]={value:s}}Object.keys(this.defines).length>0&&(e.defines=this.defines),e.vertexShader=this.vertexShader,e.fragmentShader=this.fragmentShader,e.lights=this.lights,e.clipping=this.clipping;let i={};for(let t in this.extensions)!0===this.extensions[t]&&(i[t]=!0);return Object.keys(i).length>0&&(e.extensions=i),e}}class nn extends re{constructor(){super(),this.isCamera=!0,this.type="Camera",this.matrixWorldInverse=new sV,this.projectionMatrix=new sV,this.projectionMatrixInverse=new sV,this.coordinateSystem=iA}copy(t,e){return super.copy(t,e),this.matrixWorldInverse.copy(t.matrixWorldInverse),this.projectionMatrix.copy(t.projectionMatrix),this.projectionMatrixInverse.copy(t.projectionMatrixInverse),this.coordinateSystem=t.coordinateSystem,this}getWorldDirection(t){return super.getWorldDirection(t).negate()}updateMatrixWorld(t){super.updateMatrixWorld(t),this.matrixWorldInverse.copy(this.matrixWorld).invert()}updateWorldMatrix(t,e){super.updateWorldMatrix(t,e),this.matrixWorldInverse.copy(this.matrixWorld).invert()}clone(){return new this.constructor().copy(this)}}let na=new sh,no=new ij,nh=new ij;class nl extends nn{constructor(t=50,e=1,i=.1,s=2e3){super(),this.isPerspectiveCamera=!0,this.type="PerspectiveCamera",this.fov=t,this.zoom=1,this.near=i,this.far=s,this.focus=10,this.aspect=e,this.view=null,this.filmGauge=35,this.filmOffset=0,this.updateProjectionMatrix()}copy(t,e){return super.copy(t,e),this.fov=t.fov,this.zoom=t.zoom,this.near=t.near,this.far=t.far,this.focus=t.focus,this.aspect=t.aspect,this.view=null===t.view?null:Object.assign({},t.view),this.filmGauge=t.filmGauge,this.filmOffset=t.filmOffset,this}setFocalLength(t){let e=.5*this.getFilmHeight()/t;this.fov=2*iR*Math.atan(e),this.updateProjectionMatrix()}getFocalLength(){let t=Math.tan(.5*iB*this.fov);return .5*this.getFilmHeight()/t}getEffectiveFOV(){return 2*iR*Math.atan(Math.tan(.5*iB*this.fov)/this.zoom)}getFilmWidth(){return this.filmGauge*Math.min(this.aspect,1)}getFilmHeight(){return this.filmGauge/Math.max(this.aspect,1)}getViewBounds(t,e,i){na.set(-1,-1,.5).applyMatrix4(this.projectionMatrixInverse),e.set(na.x,na.y).multiplyScalar(-t/na.z),na.set(1,1,.5).applyMatrix4(this.projectionMatrixInverse),i.set(na.x,na.y).multiplyScalar(-t/na.z)}getViewSize(t,e){return this.getViewBounds(t,no,nh),e.subVectors(nh,no)}setViewOffset(t,e,i,s,r,n){this.aspect=t/e,null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=s,this.view.width=r,this.view.height=n,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){let t=this.near,e=t*Math.tan(.5*iB*this.fov)/this.zoom,i=2*e,s=this.aspect*i,r=-.5*s,n=this.view;if(null!==this.view&&this.view.enabled){let t=n.fullWidth,a=n.fullHeight;r+=n.offsetX*s/t,e-=n.offsetY*i/a,s*=n.width/t,i*=n.height/a}let a=this.filmOffset;0!==a&&(r+=t*a/this.getFilmWidth()),this.projectionMatrix.makePerspective(r,r+s,e,e-i,t,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(t){let e=super.toJSON(t);return e.object.fov=this.fov,e.object.zoom=this.zoom,e.object.near=this.near,e.object.far=this.far,e.object.focus=this.focus,e.object.aspect=this.aspect,null!==this.view&&(e.object.view=Object.assign({},this.view)),e.object.filmGauge=this.filmGauge,e.object.filmOffset=this.filmOffset,e}}class nu extends re{constructor(t,e,i){super(),this.type="CubeCamera",this.renderTarget=i,this.coordinateSystem=null,this.activeMipmapLevel=0;let s=new nl(-90,1,t,e);s.layers=this.layers,this.add(s);let r=new nl(-90,1,t,e);r.layers=this.layers,this.add(r);let n=new nl(-90,1,t,e);n.layers=this.layers,this.add(n);let a=new nl(-90,1,t,e);a.layers=this.layers,this.add(a);let o=new nl(-90,1,t,e);o.layers=this.layers,this.add(o);let h=new nl(-90,1,t,e);h.layers=this.layers,this.add(h)}updateCoordinateSystem(){let t=this.coordinateSystem,e=this.children.concat(),[i,s,r,n,a,o]=e;for(let t of e)this.remove(t);if(t===iA)i.up.set(0,1,0),i.lookAt(1,0,0),s.up.set(0,1,0),s.lookAt(-1,0,0),r.up.set(0,0,-1),r.lookAt(0,1,0),n.up.set(0,0,1),n.lookAt(0,-1,0),a.up.set(0,1,0),a.lookAt(0,0,1),o.up.set(0,1,0),o.lookAt(0,0,-1);else if(t===iT)i.up.set(0,-1,0),i.lookAt(-1,0,0),s.up.set(0,-1,0),s.lookAt(1,0,0),r.up.set(0,0,1),r.lookAt(0,1,0),n.up.set(0,0,-1),n.lookAt(0,-1,0),a.up.set(0,-1,0),a.lookAt(0,0,1),o.up.set(0,-1,0),o.lookAt(0,0,-1);else throw Error("THREE.CubeCamera.updateCoordinateSystem(): Invalid coordinate system: "+t);for(let t of e)this.add(t),t.updateMatrixWorld()}update(t,e){null===this.parent&&this.updateMatrixWorld();let{renderTarget:i,activeMipmapLevel:s}=this;this.coordinateSystem!==t.coordinateSystem&&(this.coordinateSystem=t.coordinateSystem,this.updateCoordinateSystem());let[r,n,a,o,h,l]=this.children,u=t.getRenderTarget(),c=t.getActiveCubeFace(),d=t.getActiveMipmapLevel(),p=t.xr.enabled;t.xr.enabled=!1;let m=i.texture.generateMipmaps;i.texture.generateMipmaps=!1,t.setRenderTarget(i,0,s),t.render(e,r),t.setRenderTarget(i,1,s),t.render(e,n),t.setRenderTarget(i,2,s),t.render(e,a),t.setRenderTarget(i,3,s),t.render(e,o),t.setRenderTarget(i,4,s),t.render(e,h),i.texture.generateMipmaps=m,t.setRenderTarget(i,5,s),t.render(e,l),t.setRenderTarget(u,c,d),t.xr.enabled=p,i.texture.needsPMREMUpdate=!0}}class nc extends i7{constructor(t=[],e=tf,i,s,r,n,a,o,h,l){super(t,e,i,s,r,n,a,o,h,l),this.isCubeTexture=!0,this.flipY=!1}get images(){return this.image}set images(t){this.image=t}}class nd extends si{constructor(t=1,e={}){super(t,t,e),this.isWebGLCubeRenderTarget=!0;let i={width:t,height:t,depth:1};this.texture=new nc([i,i,i,i,i,i],e.mapping,e.wrapS,e.wrapT,e.magFilter,e.minFilter,e.format,e.type,e.anisotropy,e.colorSpace),this.texture.isRenderTargetTexture=!0,this.texture.generateMipmaps=void 0!==e.generateMipmaps&&e.generateMipmaps,this.texture.minFilter=void 0!==e.minFilter?e.minFilter:tC}fromEquirectangularTexture(t,e){this.texture.type=e.type,this.texture.colorSpace=e.colorSpace,this.texture.generateMipmaps=e.generateMipmaps,this.texture.minFilter=e.minFilter,this.texture.magFilter=e.magFilter;let i={uniforms:{tEquirect:{value:null}},vertexShader:`

				varying vec3 vWorldDirection;

				vec3 transformDirection( in vec3 dir, in mat4 matrix ) {

					return normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );

				}

				void main() {

					vWorldDirection = transformDirection( position, modelMatrix );

					#include <begin_vertex>
					#include <project_vertex>

				}
			`,fragmentShader:`

				uniform sampler2D tEquirect;

				varying vec3 vWorldDirection;

				#include <common>

				void main() {

					vec3 direction = normalize( vWorldDirection );

					vec2 sampleUV = equirectUv( direction );

					gl_FragColor = texture2D( tEquirect, sampleUV );

				}
			`},s=new r7(5,5,5),r=new nr({name:"CubemapFromEquirect",uniforms:nt(i.uniforms),vertexShader:i.vertexShader,fragmentShader:i.fragmentShader,side:v,blending:M});r.uniforms.tEquirect.value=e;let n=new r8(s,r),a=e.minFilter;return e.minFilter===tR&&(e.minFilter=tC),new nu(1,10,this).update(t,n),e.minFilter=a,n.geometry.dispose(),n.material.dispose(),this}clear(t,e=!0,i=!0,s=!0){let r=t.getRenderTarget();for(let r=0;r<6;r++)t.setRenderTarget(this,r),t.clear(e,i,s);t.setRenderTarget(r)}}class np extends re{constructor(){super(),this.isGroup=!0,this.type="Group"}}let nm={type:"move"};class ny{constructor(){this._targetRay=null,this._grip=null,this._hand=null}getHandSpace(){return null===this._hand&&(this._hand=new np,this._hand.matrixAutoUpdate=!1,this._hand.visible=!1,this._hand.joints={},this._hand.inputState={pinching:!1}),this._hand}getTargetRaySpace(){return null===this._targetRay&&(this._targetRay=new np,this._targetRay.matrixAutoUpdate=!1,this._targetRay.visible=!1,this._targetRay.hasLinearVelocity=!1,this._targetRay.linearVelocity=new sh,this._targetRay.hasAngularVelocity=!1,this._targetRay.angularVelocity=new sh),this._targetRay}getGripSpace(){return null===this._grip&&(this._grip=new np,this._grip.matrixAutoUpdate=!1,this._grip.visible=!1,this._grip.hasLinearVelocity=!1,this._grip.linearVelocity=new sh,this._grip.hasAngularVelocity=!1,this._grip.angularVelocity=new sh),this._grip}dispatchEvent(t){return null!==this._targetRay&&this._targetRay.dispatchEvent(t),null!==this._grip&&this._grip.dispatchEvent(t),null!==this._hand&&this._hand.dispatchEvent(t),this}connect(t){if(t&&t.hand){let e=this._hand;if(e)for(let i of t.hand.values())this._getHandJoint(e,i)}return this.dispatchEvent({type:"connected",data:t}),this}disconnect(t){return this.dispatchEvent({type:"disconnected",data:t}),null!==this._targetRay&&(this._targetRay.visible=!1),null!==this._grip&&(this._grip.visible=!1),null!==this._hand&&(this._hand.visible=!1),this}update(t,e,i){let s=null,r=null,n=null,a=this._targetRay,o=this._grip,h=this._hand;if(t&&"visible-blurred"!==e.session.visibilityState){if(h&&t.hand){for(let s of(n=!0,t.hand.values())){let t=e.getJointPose(s,i),r=this._getHandJoint(h,s);null!==t&&(r.matrix.fromArray(t.transform.matrix),r.matrix.decompose(r.position,r.rotation,r.scale),r.matrixWorldNeedsUpdate=!0,r.jointRadius=t.radius),r.visible=null!==t}let s=h.joints["index-finger-tip"],r=h.joints["thumb-tip"],a=s.position.distanceTo(r.position);h.inputState.pinching&&a>.025?(h.inputState.pinching=!1,this.dispatchEvent({type:"pinchend",handedness:t.handedness,target:this})):!h.inputState.pinching&&a<=.015&&(h.inputState.pinching=!0,this.dispatchEvent({type:"pinchstart",handedness:t.handedness,target:this}))}else null!==o&&t.gripSpace&&null!==(r=e.getPose(t.gripSpace,i))&&(o.matrix.fromArray(r.transform.matrix),o.matrix.decompose(o.position,o.rotation,o.scale),o.matrixWorldNeedsUpdate=!0,r.linearVelocity?(o.hasLinearVelocity=!0,o.linearVelocity.copy(r.linearVelocity)):o.hasLinearVelocity=!1,r.angularVelocity?(o.hasAngularVelocity=!0,o.angularVelocity.copy(r.angularVelocity)):o.hasAngularVelocity=!1);null!==a&&(null===(s=e.getPose(t.targetRaySpace,i))&&null!==r&&(s=r),null!==s&&(a.matrix.fromArray(s.transform.matrix),a.matrix.decompose(a.position,a.rotation,a.scale),a.matrixWorldNeedsUpdate=!0,s.linearVelocity?(a.hasLinearVelocity=!0,a.linearVelocity.copy(s.linearVelocity)):a.hasLinearVelocity=!1,s.angularVelocity?(a.hasAngularVelocity=!0,a.angularVelocity.copy(s.angularVelocity)):a.hasAngularVelocity=!1,this.dispatchEvent(nm)))}return null!==a&&(a.visible=null!==s),null!==o&&(o.visible=null!==r),null!==h&&(h.visible=null!==n),this}_getHandJoint(t,e){if(void 0===t.joints[e.jointName]){let i=new np;i.matrixAutoUpdate=!1,i.visible=!1,t.joints[e.jointName]=i,t.add(i)}return t.joints[e.jointName]}}class nf{constructor(t,e=25e-5){this.isFogExp2=!0,this.name="",this.color=new rv(t),this.density=e}clone(){return new nf(this.color,this.density)}toJSON(){return{type:"FogExp2",name:this.name,color:this.color.getHex(),density:this.density}}}class ng{constructor(t,e=1,i=1e3){this.isFog=!0,this.name="",this.color=new rv(t),this.near=e,this.far=i}clone(){return new ng(this.color,this.near,this.far)}toJSON(){return{type:"Fog",name:this.name,color:this.color.getHex(),near:this.near,far:this.far}}}class nx extends re{constructor(){super(),this.isScene=!0,this.type="Scene",this.background=null,this.environment=null,this.fog=null,this.backgroundBlurriness=0,this.backgroundIntensity=1,this.backgroundRotation=new sZ,this.environmentIntensity=1,this.environmentRotation=new sZ,this.overrideMaterial=null,"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("observe",{detail:this}))}copy(t,e){return super.copy(t,e),null!==t.background&&(this.background=t.background.clone()),null!==t.environment&&(this.environment=t.environment.clone()),null!==t.fog&&(this.fog=t.fog.clone()),this.backgroundBlurriness=t.backgroundBlurriness,this.backgroundIntensity=t.backgroundIntensity,this.backgroundRotation.copy(t.backgroundRotation),this.environmentIntensity=t.environmentIntensity,this.environmentRotation.copy(t.environmentRotation),null!==t.overrideMaterial&&(this.overrideMaterial=t.overrideMaterial.clone()),this.matrixAutoUpdate=t.matrixAutoUpdate,this}toJSON(t){let e=super.toJSON(t);return null!==this.fog&&(e.object.fog=this.fog.toJSON()),this.backgroundBlurriness>0&&(e.object.backgroundBlurriness=this.backgroundBlurriness),1!==this.backgroundIntensity&&(e.object.backgroundIntensity=this.backgroundIntensity),e.object.backgroundRotation=this.backgroundRotation.toArray(),1!==this.environmentIntensity&&(e.object.environmentIntensity=this.environmentIntensity),e.object.environmentRotation=this.environmentRotation.toArray(),e}}class nb{constructor(t,e){this.isInterleavedBuffer=!0,this.array=t,this.stride=e,this.count=void 0!==t?t.length/e:0,this.usage=ip,this.updateRanges=[],this.version=0,this.uuid=iE()}onUploadCallback(){}set needsUpdate(t){!0===t&&this.version++}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.array=new t.array.constructor(t.array),this.count=t.count,this.stride=t.stride,this.usage=t.usage,this}copyAt(t,e,i){t*=this.stride,i*=e.stride;for(let s=0,r=this.stride;s<r;s++)this.array[t+s]=e.array[i+s];return this}set(t,e=0){return this.array.set(t,e),this}clone(t){void 0===t.arrayBuffers&&(t.arrayBuffers={}),void 0===this.array.buffer._uuid&&(this.array.buffer._uuid=iE()),void 0===t.arrayBuffers[this.array.buffer._uuid]&&(t.arrayBuffers[this.array.buffer._uuid]=this.array.slice(0).buffer);let e=new this.array.constructor(t.arrayBuffers[this.array.buffer._uuid]),i=new this.constructor(e,this.stride);return i.setUsage(this.usage),i}onUpload(t){return this.onUploadCallback=t,this}toJSON(t){return void 0===t.arrayBuffers&&(t.arrayBuffers={}),void 0===this.array.buffer._uuid&&(this.array.buffer._uuid=iE()),void 0===t.arrayBuffers[this.array.buffer._uuid]&&(t.arrayBuffers[this.array.buffer._uuid]=Array.from(new Uint32Array(this.array.buffer))),{uuid:this.uuid,buffer:this.array.buffer._uuid,type:this.array.constructor.name,stride:this.stride}}}let nv=new sh;class nw{constructor(t,e,i,s=!1){this.isInterleavedBufferAttribute=!0,this.name="",this.data=t,this.itemSize=e,this.offset=i,this.normalized=s}get count(){return this.data.count}get array(){return this.data.array}set needsUpdate(t){this.data.needsUpdate=t}applyMatrix4(t){for(let e=0,i=this.data.count;e<i;e++)nv.fromBufferAttribute(this,e),nv.applyMatrix4(t),this.setXYZ(e,nv.x,nv.y,nv.z);return this}applyNormalMatrix(t){for(let e=0,i=this.count;e<i;e++)nv.fromBufferAttribute(this,e),nv.applyNormalMatrix(t),this.setXYZ(e,nv.x,nv.y,nv.z);return this}transformDirection(t){for(let e=0,i=this.count;e<i;e++)nv.fromBufferAttribute(this,e),nv.transformDirection(t),this.setXYZ(e,nv.x,nv.y,nv.z);return this}getComponent(t,e){let i=this.array[t*this.data.stride+this.offset+e];return this.normalized&&(i=iF(i,this.array)),i}setComponent(t,e,i){return this.normalized&&(i=iV(i,this.array)),this.data.array[t*this.data.stride+this.offset+e]=i,this}setX(t,e){return this.normalized&&(e=iV(e,this.array)),this.data.array[t*this.data.stride+this.offset]=e,this}setY(t,e){return this.normalized&&(e=iV(e,this.array)),this.data.array[t*this.data.stride+this.offset+1]=e,this}setZ(t,e){return this.normalized&&(e=iV(e,this.array)),this.data.array[t*this.data.stride+this.offset+2]=e,this}setW(t,e){return this.normalized&&(e=iV(e,this.array)),this.data.array[t*this.data.stride+this.offset+3]=e,this}getX(t){let e=this.data.array[t*this.data.stride+this.offset];return this.normalized&&(e=iF(e,this.array)),e}getY(t){let e=this.data.array[t*this.data.stride+this.offset+1];return this.normalized&&(e=iF(e,this.array)),e}getZ(t){let e=this.data.array[t*this.data.stride+this.offset+2];return this.normalized&&(e=iF(e,this.array)),e}getW(t){let e=this.data.array[t*this.data.stride+this.offset+3];return this.normalized&&(e=iF(e,this.array)),e}setXY(t,e,i){return t=t*this.data.stride+this.offset,this.normalized&&(e=iV(e,this.array),i=iV(i,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this}setXYZ(t,e,i,s){return t=t*this.data.stride+this.offset,this.normalized&&(e=iV(e,this.array),i=iV(i,this.array),s=iV(s,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this.data.array[t+2]=s,this}setXYZW(t,e,i,s,r){return t=t*this.data.stride+this.offset,this.normalized&&(e=iV(e,this.array),i=iV(i,this.array),s=iV(s,this.array),r=iV(r,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this.data.array[t+2]=s,this.data.array[t+3]=r,this}clone(t){if(void 0!==t)return void 0===t.interleavedBuffers&&(t.interleavedBuffers={}),void 0===t.interleavedBuffers[this.data.uuid]&&(t.interleavedBuffers[this.data.uuid]=this.data.clone(t)),new nw(t.interleavedBuffers[this.data.uuid],this.itemSize,this.offset,this.normalized);{console.log("THREE.InterleavedBufferAttribute.clone(): Cloning an interleaved buffer attribute will de-interleave buffer data.");let t=[];for(let e=0;e<this.count;e++){let i=e*this.data.stride+this.offset;for(let e=0;e<this.itemSize;e++)t.push(this.data.array[i+e])}return new rR(new this.array.constructor(t),this.itemSize,this.normalized)}}toJSON(t){if(void 0!==t)return void 0===t.interleavedBuffers&&(t.interleavedBuffers={}),void 0===t.interleavedBuffers[this.data.uuid]&&(t.interleavedBuffers[this.data.uuid]=this.data.toJSON(t)),{isInterleavedBufferAttribute:!0,itemSize:this.itemSize,data:this.data.uuid,offset:this.offset,normalized:this.normalized};{console.log("THREE.InterleavedBufferAttribute.toJSON(): Serializing an interleaved buffer attribute will de-interleave buffer data.");let t=[];for(let e=0;e<this.count;e++){let i=e*this.data.stride+this.offset;for(let e=0;e<this.itemSize;e++)t.push(this.data.array[i+e])}return{itemSize:this.itemSize,type:this.array.constructor.name,array:t,normalized:this.normalized}}}}class nM extends rS{constructor(t){super(),this.isSpriteMaterial=!0,this.type="SpriteMaterial",this.color=new rv(0xffffff),this.map=null,this.alphaMap=null,this.rotation=0,this.sizeAttenuation=!0,this.transparent=!0,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.alphaMap=t.alphaMap,this.rotation=t.rotation,this.sizeAttenuation=t.sizeAttenuation,this.fog=t.fog,this}}let nS=new sh,n_=new sh,nA=new sh,nT=new ij,nz=new ij,nI=new sV,nC=new sh,nk=new sh,nB=new sh,nR=new ij,nE=new ij,nP=new ij;class nO extends re{constructor(t=new nM){if(super(),this.isSprite=!0,this.type="Sprite",void 0===r){r=new rY;let t=new nb(new Float32Array([-.5,-.5,0,0,0,.5,-.5,0,1,0,.5,.5,0,1,1,-.5,.5,0,0,1]),5);r.setIndex([0,1,2,0,2,3]),r.setAttribute("position",new nw(t,3,0,!1)),r.setAttribute("uv",new nw(t,2,3,!1))}this.geometry=r,this.material=t,this.center=new ij(.5,.5)}raycast(t,e){let i,s;null===t.camera&&console.error('THREE.Sprite: "Raycaster.camera" needs to be set in order to raycast against sprites.'),n_.setFromMatrixScale(this.matrixWorld),nI.copy(t.camera.matrixWorld),this.modelViewMatrix.multiplyMatrices(t.camera.matrixWorldInverse,this.matrixWorld),nA.setFromMatrixPosition(this.modelViewMatrix),t.camera.isPerspectiveCamera&&!1===this.material.sizeAttenuation&&n_.multiplyScalar(-nA.z);let r=this.material.rotation;0!==r&&(s=Math.cos(r),i=Math.sin(r));let n=this.center;nN(nC.set(-.5,-.5,0),nA,n,n_,i,s),nN(nk.set(.5,-.5,0),nA,n,n_,i,s),nN(nB.set(.5,.5,0),nA,n,n_,i,s),nR.set(0,0),nE.set(1,0),nP.set(1,1);let a=t.ray.intersectTriangle(nC,nk,nB,!1,nS);if(null===a&&(nN(nk.set(-.5,.5,0),nA,n,n_,i,s),nE.set(0,1),null===(a=t.ray.intersectTriangle(nC,nB,nk,!1,nS))))return;let o=t.ray.origin.distanceTo(nS);o<t.near||o>t.far||e.push({distance:o,point:nS.clone(),uv:ry.getInterpolation(nS,nC,nk,nB,nR,nE,nP,new ij),face:null,object:this})}copy(t,e){return super.copy(t,e),void 0!==t.center&&this.center.copy(t.center),this.material=t.material,this}}function nN(t,e,i,s,r,n){nT.subVectors(t,i).addScalar(.5).multiply(s),void 0!==r?(nz.x=n*nT.x-r*nT.y,nz.y=r*nT.x+n*nT.y):nz.copy(nT),t.copy(e),t.x+=nz.x,t.y+=nz.y,t.applyMatrix4(nI)}let nF=new sh,nV=new sh;class nL extends re{constructor(){super(),this.isLOD=!0,this._currentLevel=0,this.type="LOD",Object.defineProperties(this,{levels:{enumerable:!0,value:[]}}),this.autoUpdate=!0}copy(t){super.copy(t,!1);let e=t.levels;for(let t=0,i=e.length;t<i;t++){let i=e[t];this.addLevel(i.object.clone(),i.distance,i.hysteresis)}return this.autoUpdate=t.autoUpdate,this}addLevel(t,e=0,i=0){let s;e=Math.abs(e);let r=this.levels;for(s=0;s<r.length&&!(e<r[s].distance);s++);return r.splice(s,0,{distance:e,hysteresis:i,object:t}),this.add(t),this}removeLevel(t){let e=this.levels;for(let i=0;i<e.length;i++)if(e[i].distance===t){let t=e.splice(i,1);return this.remove(t[0].object),!0}return!1}getCurrentLevel(){return this._currentLevel}getObjectForDistance(t){let e=this.levels;if(e.length>0){let i,s;for(i=1,s=e.length;i<s;i++){let s=e[i].distance;if(e[i].object.visible&&(s-=s*e[i].hysteresis),t<s)break}return e[i-1].object}return null}raycast(t,e){if(this.levels.length>0){nF.setFromMatrixPosition(this.matrixWorld);let i=t.ray.origin.distanceTo(nF);this.getObjectForDistance(i).raycast(t,e)}}update(t){let e=this.levels;if(e.length>1){let i,s;nF.setFromMatrixPosition(t.matrixWorld),nV.setFromMatrixPosition(this.matrixWorld);let r=nF.distanceTo(nV)/t.zoom;for(i=1,e[0].object.visible=!0,s=e.length;i<s;i++){let t=e[i].distance;if(e[i].object.visible&&(t-=t*e[i].hysteresis),r>=t)e[i-1].object.visible=!1,e[i].object.visible=!0;else break}for(this._currentLevel=i-1;i<s;i++)e[i].object.visible=!1}}toJSON(t){let e=super.toJSON(t);!1===this.autoUpdate&&(e.object.autoUpdate=!1),e.object.levels=[];let i=this.levels;for(let t=0,s=i.length;t<s;t++){let s=i[t];e.object.levels.push({object:s.object.uuid,distance:s.distance,hysteresis:s.hysteresis})}return e}}let nj=new sh,nU=new st,nW=new st,nD=new sh,nH=new sV,nq=new sh,nJ=new sC,nX=new sV,nZ=new sF;class nY extends r8{constructor(t,e){super(t,e),this.isSkinnedMesh=!0,this.type="SkinnedMesh",this.bindMode=tp,this.bindMatrix=new sV,this.bindMatrixInverse=new sV,this.boundingBox=null,this.boundingSphere=null}computeBoundingBox(){let t=this.geometry;null===this.boundingBox&&(this.boundingBox=new sc),this.boundingBox.makeEmpty();let e=t.getAttribute("position");for(let t=0;t<e.count;t++)this.getVertexPosition(t,nq),this.boundingBox.expandByPoint(nq)}computeBoundingSphere(){let t=this.geometry;null===this.boundingSphere&&(this.boundingSphere=new sC),this.boundingSphere.makeEmpty();let e=t.getAttribute("position");for(let t=0;t<e.count;t++)this.getVertexPosition(t,nq),this.boundingSphere.expandByPoint(nq)}copy(t,e){return super.copy(t,e),this.bindMode=t.bindMode,this.bindMatrix.copy(t.bindMatrix),this.bindMatrixInverse.copy(t.bindMatrixInverse),this.skeleton=t.skeleton,null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this}raycast(t,e){let i=this.material,s=this.matrixWorld;if(void 0!==i)null===this.boundingSphere&&this.computeBoundingSphere(),nJ.copy(this.boundingSphere),nJ.applyMatrix4(s),!1!==t.ray.intersectsSphere(nJ)&&(nX.copy(s).invert(),nZ.copy(t.ray).applyMatrix4(nX),(null===this.boundingBox||!1!==nZ.intersectsBox(this.boundingBox))&&this._computeIntersections(t,e,nZ))}getVertexPosition(t,e){return super.getVertexPosition(t,e),this.applyBoneTransform(t,e),e}bind(t,e){this.skeleton=t,void 0===e&&(this.updateMatrixWorld(!0),this.skeleton.calculateInverses(),e=this.matrixWorld),this.bindMatrix.copy(e),this.bindMatrixInverse.copy(e).invert()}pose(){this.skeleton.pose()}normalizeSkinWeights(){let t=new st,e=this.geometry.attributes.skinWeight;for(let i=0,s=e.count;i<s;i++){t.fromBufferAttribute(e,i);let s=1/t.manhattanLength();s!==1/0?t.multiplyScalar(s):t.set(1,0,0,0),e.setXYZW(i,t.x,t.y,t.z,t.w)}}updateMatrixWorld(t){super.updateMatrixWorld(t),this.bindMode===tp?this.bindMatrixInverse.copy(this.matrixWorld).invert():this.bindMode===tm?this.bindMatrixInverse.copy(this.bindMatrix).invert():console.warn("THREE.SkinnedMesh: Unrecognized bindMode: "+this.bindMode)}applyBoneTransform(t,e){let i=this.skeleton,s=this.geometry;nU.fromBufferAttribute(s.attributes.skinIndex,t),nW.fromBufferAttribute(s.attributes.skinWeight,t),nj.copy(e).applyMatrix4(this.bindMatrix),e.set(0,0,0);for(let t=0;t<4;t++){let s=nW.getComponent(t);if(0!==s){let r=nU.getComponent(t);nH.multiplyMatrices(i.bones[r].matrixWorld,i.boneInverses[r]),e.addScaledVector(nD.copy(nj).applyMatrix4(nH),s)}}return e.applyMatrix4(this.bindMatrixInverse)}}class nG extends re{constructor(){super(),this.isBone=!0,this.type="Bone"}}class n$ extends i7{constructor(t=null,e=1,i=1,s,r,n,a,o,h=t_,l=t_,u,c){super(null,n,a,o,h,l,s,r,u,c),this.isDataTexture=!0,this.image={data:t,width:e,height:i},this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}let nQ=new sV,nK=new sV;class n0{constructor(t=[],e=[]){this.uuid=iE(),this.bones=t.slice(0),this.boneInverses=e,this.boneMatrices=null,this.boneTexture=null,this.init()}init(){let t=this.bones,e=this.boneInverses;if(this.boneMatrices=new Float32Array(16*t.length),0===e.length)this.calculateInverses();else if(t.length!==e.length){console.warn("THREE.Skeleton: Number of inverse bone matrices does not match amount of bones."),this.boneInverses=[];for(let t=0,e=this.bones.length;t<e;t++)this.boneInverses.push(new sV)}}calculateInverses(){this.boneInverses.length=0;for(let t=0,e=this.bones.length;t<e;t++){let e=new sV;this.bones[t]&&e.copy(this.bones[t].matrixWorld).invert(),this.boneInverses.push(e)}}pose(){for(let t=0,e=this.bones.length;t<e;t++){let e=this.bones[t];e&&e.matrixWorld.copy(this.boneInverses[t]).invert()}for(let t=0,e=this.bones.length;t<e;t++){let e=this.bones[t];e&&(e.parent&&e.parent.isBone?(e.matrix.copy(e.parent.matrixWorld).invert(),e.matrix.multiply(e.matrixWorld)):e.matrix.copy(e.matrixWorld),e.matrix.decompose(e.position,e.quaternion,e.scale))}}update(){let t=this.bones,e=this.boneInverses,i=this.boneMatrices,s=this.boneTexture;for(let s=0,r=t.length;s<r;s++){let r=t[s]?t[s].matrixWorld:nK;nQ.multiplyMatrices(r,e[s]),nQ.toArray(i,16*s)}null!==s&&(s.needsUpdate=!0)}clone(){return new n0(this.bones,this.boneInverses)}computeBoneTexture(){let t=Math.sqrt(4*this.bones.length),e=new Float32Array((t=Math.max(t=4*Math.ceil(t/4),4))*t*4);e.set(this.boneMatrices);let i=new n$(e,t,t,tZ,tj);return i.needsUpdate=!0,this.boneMatrices=e,this.boneTexture=i,this}getBoneByName(t){for(let e=0,i=this.bones.length;e<i;e++){let i=this.bones[e];if(i.name===t)return i}}dispose(){null!==this.boneTexture&&(this.boneTexture.dispose(),this.boneTexture=null)}fromJSON(t,e){this.uuid=t.uuid;for(let i=0,s=t.bones.length;i<s;i++){let s=t.bones[i],r=e[s];void 0===r&&(console.warn("THREE.Skeleton: No bone found with UUID:",s),r=new nG),this.bones.push(r),this.boneInverses.push(new sV().fromArray(t.boneInverses[i]))}return this.init(),this}toJSON(){let t={metadata:{version:4.6,type:"Skeleton",generator:"Skeleton.toJSON"},bones:[],boneInverses:[]};t.uuid=this.uuid;let e=this.bones,i=this.boneInverses;for(let s=0,r=e.length;s<r;s++){let r=e[s];t.bones.push(r.uuid);let n=i[s];t.boneInverses.push(n.toArray())}return t}}class n1 extends rR{constructor(t,e,i,s=1){super(t,e,i),this.isInstancedBufferAttribute=!0,this.meshPerAttribute=s}copy(t){return super.copy(t),this.meshPerAttribute=t.meshPerAttribute,this}toJSON(){let t=super.toJSON();return t.meshPerAttribute=this.meshPerAttribute,t.isInstancedBufferAttribute=!0,t}}let n2=new sV,n3=new sV,n5=[],n4=new sc,n6=new sV,n8=new r8,n9=new sC;class n7 extends r8{constructor(t,e,i){super(t,e),this.isInstancedMesh=!0,this.instanceMatrix=new n1(new Float32Array(16*i),16),this.instanceColor=null,this.morphTexture=null,this.count=i,this.boundingBox=null,this.boundingSphere=null;for(let t=0;t<i;t++)this.setMatrixAt(t,n6)}computeBoundingBox(){let t=this.geometry,e=this.count;null===this.boundingBox&&(this.boundingBox=new sc),null===t.boundingBox&&t.computeBoundingBox(),this.boundingBox.makeEmpty();for(let i=0;i<e;i++)this.getMatrixAt(i,n2),n4.copy(t.boundingBox).applyMatrix4(n2),this.boundingBox.union(n4)}computeBoundingSphere(){let t=this.geometry,e=this.count;null===this.boundingSphere&&(this.boundingSphere=new sC),null===t.boundingSphere&&t.computeBoundingSphere(),this.boundingSphere.makeEmpty();for(let i=0;i<e;i++)this.getMatrixAt(i,n2),n9.copy(t.boundingSphere).applyMatrix4(n2),this.boundingSphere.union(n9)}copy(t,e){return super.copy(t,e),this.instanceMatrix.copy(t.instanceMatrix),null!==t.morphTexture&&(this.morphTexture=t.morphTexture.clone()),null!==t.instanceColor&&(this.instanceColor=t.instanceColor.clone()),this.count=t.count,null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this}getColorAt(t,e){e.fromArray(this.instanceColor.array,3*t)}getMatrixAt(t,e){e.fromArray(this.instanceMatrix.array,16*t)}getMorphAt(t,e){let i=e.morphTargetInfluences,s=this.morphTexture.source.data.data,r=t*(i.length+1)+1;for(let t=0;t<i.length;t++)i[t]=s[r+t]}raycast(t,e){let i=this.matrixWorld,s=this.count;if((n8.geometry=this.geometry,n8.material=this.material,void 0!==n8.material)&&(null===this.boundingSphere&&this.computeBoundingSphere(),n9.copy(this.boundingSphere),n9.applyMatrix4(i),!1!==t.ray.intersectsSphere(n9)))for(let r=0;r<s;r++){this.getMatrixAt(r,n2),n3.multiplyMatrices(i,n2),n8.matrixWorld=n3,n8.raycast(t,n5);for(let t=0,i=n5.length;t<i;t++){let i=n5[t];i.instanceId=r,i.object=this,e.push(i)}n5.length=0}}setColorAt(t,e){null===this.instanceColor&&(this.instanceColor=new n1(new Float32Array(3*this.instanceMatrix.count).fill(1),3)),e.toArray(this.instanceColor.array,3*t)}setMatrixAt(t,e){e.toArray(this.instanceMatrix.array,16*t)}setMorphAt(t,e){let i=e.morphTargetInfluences,s=i.length+1;null===this.morphTexture&&(this.morphTexture=new n$(new Float32Array(s*this.count),s,this.count,tK,tj));let r=this.morphTexture.source.data.data,n=0;for(let t=0;t<i.length;t++)n+=i[t];let a=this.geometry.morphTargetsRelative?1:1-n,o=s*t;r[o]=a,r.set(i,o+1)}updateMorphTargets(){}dispose(){this.dispatchEvent({type:"dispose"}),null!==this.morphTexture&&(this.morphTexture.dispose(),this.morphTexture=null)}}let at=new sh,ae=new sh,ai=new iU;class as{constructor(t=new sh(1,0,0),e=0){this.isPlane=!0,this.normal=t,this.constant=e}set(t,e){return this.normal.copy(t),this.constant=e,this}setComponents(t,e,i,s){return this.normal.set(t,e,i),this.constant=s,this}setFromNormalAndCoplanarPoint(t,e){return this.normal.copy(t),this.constant=-e.dot(this.normal),this}setFromCoplanarPoints(t,e,i){let s=at.subVectors(i,e).cross(ae.subVectors(t,e)).normalize();return this.setFromNormalAndCoplanarPoint(s,t),this}copy(t){return this.normal.copy(t.normal),this.constant=t.constant,this}normalize(){let t=1/this.normal.length();return this.normal.multiplyScalar(t),this.constant*=t,this}negate(){return this.constant*=-1,this.normal.negate(),this}distanceToPoint(t){return this.normal.dot(t)+this.constant}distanceToSphere(t){return this.distanceToPoint(t.center)-t.radius}projectPoint(t,e){return e.copy(t).addScaledVector(this.normal,-this.distanceToPoint(t))}intersectLine(t,e){let i=t.delta(at),s=this.normal.dot(i);if(0===s)return 0===this.distanceToPoint(t.start)?e.copy(t.start):null;let r=-(t.start.dot(this.normal)+this.constant)/s;return r<0||r>1?null:e.copy(t.start).addScaledVector(i,r)}intersectsLine(t){let e=this.distanceToPoint(t.start),i=this.distanceToPoint(t.end);return e<0&&i>0||i<0&&e>0}intersectsBox(t){return t.intersectsPlane(this)}intersectsSphere(t){return t.intersectsPlane(this)}coplanarPoint(t){return t.copy(this.normal).multiplyScalar(-this.constant)}applyMatrix4(t,e){let i=e||ai.getNormalMatrix(t),s=this.coplanarPoint(at).applyMatrix4(t),r=this.normal.applyMatrix3(i).normalize();return this.constant=-s.dot(r),this}translate(t){return this.constant-=t.dot(this.normal),this}equals(t){return t.normal.equals(this.normal)&&t.constant===this.constant}clone(){return new this.constructor().copy(this)}}let ar=new sC,an=new sh;class aa{constructor(t=new as,e=new as,i=new as,s=new as,r=new as,n=new as){this.planes=[t,e,i,s,r,n]}set(t,e,i,s,r,n){let a=this.planes;return a[0].copy(t),a[1].copy(e),a[2].copy(i),a[3].copy(s),a[4].copy(r),a[5].copy(n),this}copy(t){let e=this.planes;for(let i=0;i<6;i++)e[i].copy(t.planes[i]);return this}setFromProjectionMatrix(t,e=iA){let i=this.planes,s=t.elements,r=s[0],n=s[1],a=s[2],o=s[3],h=s[4],l=s[5],u=s[6],c=s[7],d=s[8],p=s[9],m=s[10],y=s[11],f=s[12],g=s[13],x=s[14],b=s[15];if(i[0].setComponents(o-r,c-h,y-d,b-f).normalize(),i[1].setComponents(o+r,c+h,y+d,b+f).normalize(),i[2].setComponents(o+n,c+l,y+p,b+g).normalize(),i[3].setComponents(o-n,c-l,y-p,b-g).normalize(),i[4].setComponents(o-a,c-u,y-m,b-x).normalize(),e===iA)i[5].setComponents(o+a,c+u,y+m,b+x).normalize();else if(e===iT)i[5].setComponents(a,u,m,x).normalize();else throw Error("THREE.Frustum.setFromProjectionMatrix(): Invalid coordinate system: "+e);return this}intersectsObject(t){if(void 0!==t.boundingSphere)null===t.boundingSphere&&t.computeBoundingSphere(),ar.copy(t.boundingSphere).applyMatrix4(t.matrixWorld);else{let e=t.geometry;null===e.boundingSphere&&e.computeBoundingSphere(),ar.copy(e.boundingSphere).applyMatrix4(t.matrixWorld)}return this.intersectsSphere(ar)}intersectsSprite(t){return ar.center.set(0,0,0),ar.radius=.*********1865476,ar.applyMatrix4(t.matrixWorld),this.intersectsSphere(ar)}intersectsSphere(t){let e=this.planes,i=t.center,s=-t.radius;for(let t=0;t<6;t++)if(e[t].distanceToPoint(i)<s)return!1;return!0}intersectsBox(t){let e=this.planes;for(let i=0;i<6;i++){let s=e[i];if(an.x=s.normal.x>0?t.max.x:t.min.x,an.y=s.normal.y>0?t.max.y:t.min.y,an.z=s.normal.z>0?t.max.z:t.min.z,0>s.distanceToPoint(an))return!1}return!0}containsPoint(t){let e=this.planes;for(let i=0;i<6;i++)if(0>e[i].distanceToPoint(t))return!1;return!0}clone(){return new this.constructor().copy(this)}}function ao(t,e){return t-e}function ah(t,e){return t.z-e.z}function al(t,e){return e.z-t.z}class au{constructor(){this.index=0,this.pool=[],this.list=[]}push(t,e,i,s){let r=this.pool,n=this.list;this.index>=r.length&&r.push({start:-1,count:-1,z:-1,index:-1});let a=r[this.index];n.push(a),this.index++,a.start=t,a.count=e,a.z=i,a.index=s}reset(){this.list.length=0,this.index=0}}let ac=new sV,ad=new rv(1,1,1),ap=new aa,am=new sc,ay=new sC,af=new sh,ag=new sh,ax=new sh,ab=new au,av=new r8,aw=[];function aM(t,e){if(t.constructor!==e.constructor){let i=Math.min(t.length,e.length);for(let s=0;s<i;s++)e[s]=t[s]}else{let i=Math.min(t.length,e.length);e.set(new t.constructor(t.buffer,0,i))}}class aS extends r8{constructor(t,e,i=2*e,s){super(new rY,s),this.isBatchedMesh=!0,this.perObjectFrustumCulled=!0,this.sortObjects=!0,this.boundingBox=null,this.boundingSphere=null,this.customSort=null,this._instanceInfo=[],this._geometryInfo=[],this._availableInstanceIds=[],this._availableGeometryIds=[],this._nextIndexStart=0,this._nextVertexStart=0,this._geometryCount=0,this._visibilityChanged=!0,this._geometryInitialized=!1,this._maxInstanceCount=t,this._maxVertexCount=e,this._maxIndexCount=i,this._multiDrawCounts=new Int32Array(t),this._multiDrawStarts=new Int32Array(t),this._multiDrawCount=0,this._multiDrawInstances=null,this._matricesTexture=null,this._indirectTexture=null,this._colorsTexture=null,this._initMatricesTexture(),this._initIndirectTexture()}get maxInstanceCount(){return this._maxInstanceCount}get instanceCount(){return this._instanceInfo.length-this._availableInstanceIds.length}get unusedVertexCount(){return this._maxVertexCount-this._nextVertexStart}get unusedIndexCount(){return this._maxIndexCount-this._nextIndexStart}_initMatricesTexture(){let t=Math.sqrt(4*this._maxInstanceCount),e=new n$(new Float32Array((t=Math.max(t=4*Math.ceil(t/4),4))*t*4),t,t,tZ,tj);this._matricesTexture=e}_initIndirectTexture(){let t=Math.sqrt(this._maxInstanceCount),e=new n$(new Uint32Array((t=Math.ceil(t))*t),t,t,t0,tL);this._indirectTexture=e}_initColorsTexture(){let t=Math.sqrt(this._maxInstanceCount),e=new n$(new Float32Array((t=Math.ceil(t))*t*4).fill(1),t,t,tZ,tj);e.colorSpace=i1.workingColorSpace,this._colorsTexture=e}_initializeGeometry(t){let e=this.geometry,i=this._maxVertexCount,s=this._maxIndexCount;if(!1===this._geometryInitialized){for(let s in t.attributes){let{array:r,itemSize:n,normalized:a}=t.getAttribute(s),o=new rR(new r.constructor(i*n),n,a);e.setAttribute(s,o)}if(null!==t.getIndex()){let t=i>65535?new Uint32Array(s):new Uint16Array(s);e.setIndex(new rR(t,1))}this._geometryInitialized=!0}}_validateGeometry(t){let e=this.geometry;if(!!t.getIndex()!=!!e.getIndex())throw Error('THREE.BatchedMesh: All geometries must consistently have "index".');for(let i in e.attributes){if(!t.hasAttribute(i))throw Error(`THREE.BatchedMesh: Added geometry missing "${i}". All geometries must have consistent attributes.`);let s=t.getAttribute(i),r=e.getAttribute(i);if(s.itemSize!==r.itemSize||s.normalized!==r.normalized)throw Error("THREE.BatchedMesh: All attributes must have a consistent itemSize and normalized value.")}}validateInstanceId(t){let e=this._instanceInfo;if(t<0||t>=e.length||!1===e[t].active)throw Error(`THREE.BatchedMesh: Invalid instanceId ${t}. Instance is either out of range or has been deleted.`)}validateGeometryId(t){let e=this._geometryInfo;if(t<0||t>=e.length||!1===e[t].active)throw Error(`THREE.BatchedMesh: Invalid geometryId ${t}. Geometry is either out of range or has been deleted.`)}setCustomSort(t){return this.customSort=t,this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new sc);let t=this.boundingBox,e=this._instanceInfo;t.makeEmpty();for(let i=0,s=e.length;i<s;i++){if(!1===e[i].active)continue;let s=e[i].geometryIndex;this.getMatrixAt(i,ac),this.getBoundingBoxAt(s,am).applyMatrix4(ac),t.union(am)}}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new sC);let t=this.boundingSphere,e=this._instanceInfo;t.makeEmpty();for(let i=0,s=e.length;i<s;i++){if(!1===e[i].active)continue;let s=e[i].geometryIndex;this.getMatrixAt(i,ac),this.getBoundingSphereAt(s,ay).applyMatrix4(ac),t.union(ay)}}addInstance(t){if(this._instanceInfo.length>=this.maxInstanceCount&&0===this._availableInstanceIds.length)throw Error("THREE.BatchedMesh: Maximum item count reached.");let e={visible:!0,active:!0,geometryIndex:t},i=null;this._availableInstanceIds.length>0?(this._availableInstanceIds.sort(ao),i=this._availableInstanceIds.shift(),this._instanceInfo[i]=e):(i=this._instanceInfo.length,this._instanceInfo.push(e));let s=this._matricesTexture;ac.identity().toArray(s.image.data,16*i),s.needsUpdate=!0;let r=this._colorsTexture;return r&&(ad.toArray(r.image.data,4*i),r.needsUpdate=!0),this._visibilityChanged=!0,i}addGeometry(t,e=-1,i=-1){let s;this._initializeGeometry(t),this._validateGeometry(t);let r={vertexStart:-1,vertexCount:-1,reservedVertexCount:-1,indexStart:-1,indexCount:-1,reservedIndexCount:-1,start:-1,count:-1,boundingBox:null,boundingSphere:null,active:!0},n=this._geometryInfo;r.vertexStart=this._nextVertexStart,r.reservedVertexCount=-1===e?t.getAttribute("position").count:e;let a=t.getIndex();if(null!==a&&(r.indexStart=this._nextIndexStart,r.reservedIndexCount=-1===i?a.count:i),-1!==r.indexStart&&r.indexStart+r.reservedIndexCount>this._maxIndexCount||r.vertexStart+r.reservedVertexCount>this._maxVertexCount)throw Error("THREE.BatchedMesh: Reserved space request exceeds the maximum buffer size.");return this._availableGeometryIds.length>0?(this._availableGeometryIds.sort(ao),n[s=this._availableGeometryIds.shift()]=r):(s=this._geometryCount,this._geometryCount++,n.push(r)),this.setGeometryAt(s,t),this._nextIndexStart=r.indexStart+r.reservedIndexCount,this._nextVertexStart=r.vertexStart+r.reservedVertexCount,s}setGeometryAt(t,e){if(t>=this._geometryCount)throw Error("THREE.BatchedMesh: Maximum geometry count reached.");this._validateGeometry(e);let i=this.geometry,s=null!==i.getIndex(),r=i.getIndex(),n=e.getIndex(),a=this._geometryInfo[t];if(s&&n.count>a.reservedIndexCount||e.attributes.position.count>a.reservedVertexCount)throw Error("THREE.BatchedMesh: Reserved space not large enough for provided geometry.");let o=a.vertexStart,h=a.reservedVertexCount;for(let t in a.vertexCount=e.getAttribute("position").count,i.attributes){let s=e.getAttribute(t),r=i.getAttribute(t);!function(t,e,i=0){let s=e.itemSize;if(t.isInterleavedBufferAttribute||t.array.constructor!==e.array.constructor){let r=t.count;for(let n=0;n<r;n++)for(let r=0;r<s;r++)e.setComponent(n+i,r,t.getComponent(n,r))}else e.array.set(t.array,i*s);e.needsUpdate=!0}(s,r,o);let n=s.itemSize;for(let t=s.count;t<h;t++){let e=o+t;for(let t=0;t<n;t++)r.setComponent(e,t,0)}r.needsUpdate=!0,r.addUpdateRange(o*n,h*n)}if(s){let t=a.indexStart,i=a.reservedIndexCount;a.indexCount=e.getIndex().count;for(let e=0;e<n.count;e++)r.setX(t+e,o+n.getX(e));for(let e=n.count;e<i;e++)r.setX(t+e,o);r.needsUpdate=!0,r.addUpdateRange(t,a.reservedIndexCount)}return a.start=s?a.indexStart:a.vertexStart,a.count=s?a.indexCount:a.vertexCount,a.boundingBox=null,null!==e.boundingBox&&(a.boundingBox=e.boundingBox.clone()),a.boundingSphere=null,null!==e.boundingSphere&&(a.boundingSphere=e.boundingSphere.clone()),this._visibilityChanged=!0,t}deleteGeometry(t){let e=this._geometryInfo;if(t>=e.length||!1===e[t].active)return this;let i=this._instanceInfo;for(let e=0,s=i.length;e<s;e++)i[e].active&&i[e].geometryIndex===t&&this.deleteInstance(e);return e[t].active=!1,this._availableGeometryIds.push(t),this._visibilityChanged=!0,this}deleteInstance(t){return this.validateInstanceId(t),this._instanceInfo[t].active=!1,this._availableInstanceIds.push(t),this._visibilityChanged=!0,this}optimize(){let t=0,e=0,i=this._geometryInfo,s=i.map((t,e)=>e).sort((t,e)=>i[t].vertexStart-i[e].vertexStart),r=this.geometry;for(let n=0,a=i.length;n<a;n++){let a=i[s[n]];if(!1!==a.active){if(null!==r.index){if(a.indexStart!==e){let{indexStart:i,vertexStart:s,reservedIndexCount:n}=a,o=r.index,h=o.array,l=t-s;for(let t=i;t<i+n;t++)h[t]=h[t]+l;o.array.copyWithin(e,i,i+n),o.addUpdateRange(e,n),a.indexStart=e}e+=a.reservedIndexCount}if(a.vertexStart!==t){let{vertexStart:e,reservedVertexCount:i}=a,s=r.attributes;for(let r in s){let n=s[r],{array:a,itemSize:o}=n;a.copyWithin(t*o,e*o,(e+i)*o),n.addUpdateRange(t*o,i*o)}a.vertexStart=t}t+=a.reservedVertexCount,a.start=r.index?a.indexStart:a.vertexStart,this._nextIndexStart=r.index?a.indexStart+a.reservedIndexCount:0,this._nextVertexStart=a.vertexStart+a.reservedVertexCount}}return this}getBoundingBoxAt(t,e){if(t>=this._geometryCount)return null;let i=this.geometry,s=this._geometryInfo[t];if(null===s.boundingBox){let t=new sc,e=i.index,r=i.attributes.position;for(let i=s.start,n=s.start+s.count;i<n;i++){let s=i;e&&(s=e.getX(s)),t.expandByPoint(af.fromBufferAttribute(r,s))}s.boundingBox=t}return e.copy(s.boundingBox),e}getBoundingSphereAt(t,e){if(t>=this._geometryCount)return null;let i=this.geometry,s=this._geometryInfo[t];if(null===s.boundingSphere){let e=new sC;this.getBoundingBoxAt(t,am),am.getCenter(e.center);let r=i.index,n=i.attributes.position,a=0;for(let t=s.start,i=s.start+s.count;t<i;t++){let i=t;r&&(i=r.getX(i)),af.fromBufferAttribute(n,i),a=Math.max(a,e.center.distanceToSquared(af))}e.radius=Math.sqrt(a),s.boundingSphere=e}return e.copy(s.boundingSphere),e}setMatrixAt(t,e){this.validateInstanceId(t);let i=this._matricesTexture,s=this._matricesTexture.image.data;return e.toArray(s,16*t),i.needsUpdate=!0,this}getMatrixAt(t,e){return this.validateInstanceId(t),e.fromArray(this._matricesTexture.image.data,16*t)}setColorAt(t,e){return this.validateInstanceId(t),null===this._colorsTexture&&this._initColorsTexture(),e.toArray(this._colorsTexture.image.data,4*t),this._colorsTexture.needsUpdate=!0,this}getColorAt(t,e){return this.validateInstanceId(t),e.fromArray(this._colorsTexture.image.data,4*t)}setVisibleAt(t,e){return this.validateInstanceId(t),this._instanceInfo[t].visible===e||(this._instanceInfo[t].visible=e,this._visibilityChanged=!0),this}getVisibleAt(t){return this.validateInstanceId(t),this._instanceInfo[t].visible}setGeometryIdAt(t,e){return this.validateInstanceId(t),this.validateGeometryId(e),this._instanceInfo[t].geometryIndex=e,this}getGeometryIdAt(t){return this.validateInstanceId(t),this._instanceInfo[t].geometryIndex}getGeometryRangeAt(t,e={}){this.validateGeometryId(t);let i=this._geometryInfo[t];return e.vertexStart=i.vertexStart,e.vertexCount=i.vertexCount,e.reservedVertexCount=i.reservedVertexCount,e.indexStart=i.indexStart,e.indexCount=i.indexCount,e.reservedIndexCount=i.reservedIndexCount,e.start=i.start,e.count=i.count,e}setInstanceCount(t){let e=this._availableInstanceIds,i=this._instanceInfo;for(e.sort(ao);e[e.length-1]===i.length;)i.pop(),e.pop();if(t<i.length)throw Error(`BatchedMesh: Instance ids outside the range ${t} are being used. Cannot shrink instance count.`);let s=new Int32Array(t),r=new Int32Array(t);aM(this._multiDrawCounts,s),aM(this._multiDrawStarts,r),this._multiDrawCounts=s,this._multiDrawStarts=r,this._maxInstanceCount=t;let n=this._indirectTexture,a=this._matricesTexture,o=this._colorsTexture;n.dispose(),this._initIndirectTexture(),aM(n.image.data,this._indirectTexture.image.data),a.dispose(),this._initMatricesTexture(),aM(a.image.data,this._matricesTexture.image.data),o&&(o.dispose(),this._initColorsTexture(),aM(o.image.data,this._colorsTexture.image.data))}setGeometrySize(t,e){let i=[...this._geometryInfo].filter(t=>t.active);if(Math.max(...i.map(t=>t.vertexStart+t.reservedVertexCount))>t)throw Error(`BatchedMesh: Geometry vertex values are being used outside the range ${e}. Cannot shrink further.`);if(this.geometry.index&&Math.max(...i.map(t=>t.indexStart+t.reservedIndexCount))>e)throw Error(`BatchedMesh: Geometry index values are being used outside the range ${e}. Cannot shrink further.`);let s=this.geometry;s.dispose(),this._maxVertexCount=t,this._maxIndexCount=e,this._geometryInitialized&&(this._geometryInitialized=!1,this.geometry=new rY,this._initializeGeometry(s));let r=this.geometry;for(let t in s.index&&aM(s.index.array,r.index.array),s.attributes)aM(s.attributes[t].array,r.attributes[t].array)}raycast(t,e){let i=this._instanceInfo,s=this._geometryInfo,r=this.matrixWorld,n=this.geometry;av.material=this.material,av.geometry.index=n.index,av.geometry.attributes=n.attributes,null===av.geometry.boundingBox&&(av.geometry.boundingBox=new sc),null===av.geometry.boundingSphere&&(av.geometry.boundingSphere=new sC);for(let n=0,a=i.length;n<a;n++){if(!i[n].visible||!i[n].active)continue;let a=i[n].geometryIndex,o=s[a];av.geometry.setDrawRange(o.start,o.count),this.getMatrixAt(n,av.matrixWorld).premultiply(r),this.getBoundingBoxAt(a,av.geometry.boundingBox),this.getBoundingSphereAt(a,av.geometry.boundingSphere),av.raycast(t,aw);for(let t=0,i=aw.length;t<i;t++){let i=aw[t];i.object=this,i.batchId=n,e.push(i)}aw.length=0}av.material=null,av.geometry.index=null,av.geometry.attributes={},av.geometry.setDrawRange(0,1/0)}copy(t){return super.copy(t),this.geometry=t.geometry.clone(),this.perObjectFrustumCulled=t.perObjectFrustumCulled,this.sortObjects=t.sortObjects,this.boundingBox=null!==t.boundingBox?t.boundingBox.clone():null,this.boundingSphere=null!==t.boundingSphere?t.boundingSphere.clone():null,this._geometryInfo=t._geometryInfo.map(t=>({...t,boundingBox:null!==t.boundingBox?t.boundingBox.clone():null,boundingSphere:null!==t.boundingSphere?t.boundingSphere.clone():null})),this._instanceInfo=t._instanceInfo.map(t=>({...t})),this._maxInstanceCount=t._maxInstanceCount,this._maxVertexCount=t._maxVertexCount,this._maxIndexCount=t._maxIndexCount,this._geometryInitialized=t._geometryInitialized,this._geometryCount=t._geometryCount,this._multiDrawCounts=t._multiDrawCounts.slice(),this._multiDrawStarts=t._multiDrawStarts.slice(),this._matricesTexture=t._matricesTexture.clone(),this._matricesTexture.image.data=this._matricesTexture.image.data.slice(),null!==this._colorsTexture&&(this._colorsTexture=t._colorsTexture.clone(),this._colorsTexture.image.data=this._colorsTexture.image.data.slice()),this}dispose(){this.geometry.dispose(),this._matricesTexture.dispose(),this._matricesTexture=null,this._indirectTexture.dispose(),this._indirectTexture=null,null!==this._colorsTexture&&(this._colorsTexture.dispose(),this._colorsTexture=null)}onBeforeRender(t,e,i,s,r){if(!this._visibilityChanged&&!this.perObjectFrustumCulled&&!this.sortObjects)return;let n=s.getIndex(),a=null===n?1:n.array.BYTES_PER_ELEMENT,o=this._instanceInfo,h=this._multiDrawStarts,l=this._multiDrawCounts,u=this._geometryInfo,c=this.perObjectFrustumCulled,d=this._indirectTexture,p=d.image.data;c&&(ac.multiplyMatrices(i.projectionMatrix,i.matrixWorldInverse).multiply(this.matrixWorld),ap.setFromProjectionMatrix(ac,t.coordinateSystem));let m=0;if(this.sortObjects){ac.copy(this.matrixWorld).invert(),af.setFromMatrixPosition(i.matrixWorld).applyMatrix4(ac),ag.set(0,0,-1).transformDirection(i.matrixWorld).transformDirection(ac);for(let t=0,e=o.length;t<e;t++)if(o[t].visible&&o[t].active){let e=o[t].geometryIndex;this.getMatrixAt(t,ac),this.getBoundingSphereAt(e,ay).applyMatrix4(ac);let i=!1;if(c&&(i=!ap.intersectsSphere(ay)),!i){let i=u[e],s=ax.subVectors(ay.center,af).dot(ag);ab.push(i.start,i.count,s,t)}}let t=ab.list,e=this.customSort;null===e?t.sort(r.transparent?al:ah):e.call(this,t,i);for(let e=0,i=t.length;e<i;e++){let i=t[e];h[m]=i.start*a,l[m]=i.count,p[m]=i.index,m++}ab.reset()}else for(let t=0,e=o.length;t<e;t++)if(o[t].visible&&o[t].active){let e=o[t].geometryIndex,i=!1;if(c&&(this.getMatrixAt(t,ac),this.getBoundingSphereAt(e,ay).applyMatrix4(ac),i=!ap.intersectsSphere(ay)),!i){let i=u[e];h[m]=i.start*a,l[m]=i.count,p[m]=t,m++}}d.needsUpdate=!0,this._multiDrawCount=m,this._visibilityChanged=!1}onBeforeShadow(t,e,i,s,r,n){this.onBeforeRender(t,null,s,r,n)}}class a_ extends rS{constructor(t){super(),this.isLineBasicMaterial=!0,this.type="LineBasicMaterial",this.color=new rv(0xffffff),this.map=null,this.linewidth=1,this.linecap="round",this.linejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.linewidth=t.linewidth,this.linecap=t.linecap,this.linejoin=t.linejoin,this.fog=t.fog,this}}let aA=new sh,aT=new sh,az=new sV,aI=new sF,aC=new sC,ak=new sh,aB=new sh;class aR extends re{constructor(t=new rY,e=new a_){super(),this.isLine=!0,this.type="Line",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}computeLineDistances(){let t=this.geometry;if(null===t.index){let e=t.attributes.position,i=[0];for(let t=1,s=e.count;t<s;t++)aA.fromBufferAttribute(e,t-1),aT.fromBufferAttribute(e,t),i[t]=i[t-1],i[t]+=aA.distanceTo(aT);t.setAttribute("lineDistance",new rU(i,1))}else console.warn("THREE.Line.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}raycast(t,e){let i=this.geometry,s=this.matrixWorld,r=t.params.Line.threshold,n=i.drawRange;if(null===i.boundingSphere&&i.computeBoundingSphere(),aC.copy(i.boundingSphere),aC.applyMatrix4(s),aC.radius+=r,!1===t.ray.intersectsSphere(aC))return;az.copy(s).invert(),aI.copy(t.ray).applyMatrix4(az);let a=r/((this.scale.x+this.scale.y+this.scale.z)/3),o=a*a,h=this.isLineSegments?2:1,l=i.index,u=i.attributes.position;if(null!==l){let i=Math.max(0,n.start),s=Math.min(l.count,n.start+n.count);for(let r=i,n=s-1;r<n;r+=h){let i=aE(this,t,aI,o,l.getX(r),l.getX(r+1),r);i&&e.push(i)}if(this.isLineLoop){let r=aE(this,t,aI,o,l.getX(s-1),l.getX(i),s-1);r&&e.push(r)}}else{let i=Math.max(0,n.start),s=Math.min(u.count,n.start+n.count);for(let r=i,n=s-1;r<n;r+=h){let i=aE(this,t,aI,o,r,r+1,r);i&&e.push(i)}if(this.isLineLoop){let r=aE(this,t,aI,o,s-1,i,s-1);r&&e.push(r)}}}updateMorphTargets(){let t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){let i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){let e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}}function aE(t,e,i,s,r,n,a){let o=t.geometry.attributes.position;if(aA.fromBufferAttribute(o,r),aT.fromBufferAttribute(o,n),i.distanceSqToSegment(aA,aT,ak,aB)>s)return;ak.applyMatrix4(t.matrixWorld);let h=e.ray.origin.distanceTo(ak);if(!(h<e.near)&&!(h>e.far))return{distance:h,point:aB.clone().applyMatrix4(t.matrixWorld),index:a,face:null,faceIndex:null,barycoord:null,object:t}}let aP=new sh,aO=new sh;class aN extends aR{constructor(t,e){super(t,e),this.isLineSegments=!0,this.type="LineSegments"}computeLineDistances(){let t=this.geometry;if(null===t.index){let e=t.attributes.position,i=[];for(let t=0,s=e.count;t<s;t+=2)aP.fromBufferAttribute(e,t),aO.fromBufferAttribute(e,t+1),i[t]=0===t?0:i[t-1],i[t+1]=i[t]+aP.distanceTo(aO);t.setAttribute("lineDistance",new rU(i,1))}else console.warn("THREE.LineSegments.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}}class aF extends aR{constructor(t,e){super(t,e),this.isLineLoop=!0,this.type="LineLoop"}}class aV extends rS{constructor(t){super(),this.isPointsMaterial=!0,this.type="PointsMaterial",this.color=new rv(0xffffff),this.map=null,this.alphaMap=null,this.size=1,this.sizeAttenuation=!0,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.alphaMap=t.alphaMap,this.size=t.size,this.sizeAttenuation=t.sizeAttenuation,this.fog=t.fog,this}}let aL=new sV,aj=new sF,aU=new sC,aW=new sh;class aD extends re{constructor(t=new rY,e=new aV){super(),this.isPoints=!0,this.type="Points",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}raycast(t,e){let i=this.geometry,s=this.matrixWorld,r=t.params.Points.threshold,n=i.drawRange;if(null===i.boundingSphere&&i.computeBoundingSphere(),aU.copy(i.boundingSphere),aU.applyMatrix4(s),aU.radius+=r,!1===t.ray.intersectsSphere(aU))return;aL.copy(s).invert(),aj.copy(t.ray).applyMatrix4(aL);let a=r/((this.scale.x+this.scale.y+this.scale.z)/3),o=a*a,h=i.index,l=i.attributes.position;if(null!==h){let i=Math.max(0,n.start),r=Math.min(h.count,n.start+n.count);for(let n=i;n<r;n++){let i=h.getX(n);aW.fromBufferAttribute(l,i),aH(aW,i,o,s,t,e,this)}}else{let i=Math.max(0,n.start),r=Math.min(l.count,n.start+n.count);for(let n=i;n<r;n++)aW.fromBufferAttribute(l,n),aH(aW,n,o,s,t,e,this)}}updateMorphTargets(){let t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){let i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){let e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}}function aH(t,e,i,s,r,n,a){let o=aj.distanceSqToPoint(t);if(o<i){let i=new sh;aj.closestPointToPoint(t,i),i.applyMatrix4(s);let h=r.ray.origin.distanceTo(i);if(h<r.near||h>r.far)return;n.push({distance:h,distanceToRay:Math.sqrt(o),point:i,index:e,face:null,faceIndex:null,barycoord:null,object:a})}}class aq extends i7{constructor(t,e,i,s,r=tC,n=tC,a,o,h){super(t,e,i,s,r,n,a,o,h),this.isVideoTexture=!0,this.generateMipmaps=!1;let l=this;"requestVideoFrameCallback"in t&&t.requestVideoFrameCallback(function e(){l.needsUpdate=!0,t.requestVideoFrameCallback(e)})}clone(){return new this.constructor(this.image).copy(this)}update(){let t=this.image;!1=="requestVideoFrameCallback"in t&&t.readyState>=t.HAVE_CURRENT_DATA&&(this.needsUpdate=!0)}}class aJ extends aq{constructor(t,e,i,s,r,n,a,o){super({},t,e,i,s,r,n,a,o),this.isVideoFrameTexture=!0}update(){}clone(){return new this.constructor().copy(this)}setFrame(t){this.image=t,this.needsUpdate=!0}}class aX extends i7{constructor(t,e){super({width:t,height:e}),this.isFramebufferTexture=!0,this.magFilter=t_,this.minFilter=t_,this.generateMipmaps=!1,this.needsUpdate=!0}}class aZ extends i7{constructor(t,e,i,s,r,n,a,o,h,l,u,c){super(null,n,a,o,h,l,s,r,u,c),this.isCompressedTexture=!0,this.image={width:e,height:i},this.mipmaps=t,this.flipY=!1,this.generateMipmaps=!1}}class aY extends aZ{constructor(t,e,i,s,r,n){super(t,e,i,r,n),this.isCompressedArrayTexture=!0,this.image.depth=s,this.wrapR=tM,this.layerUpdates=new Set}addLayerUpdate(t){this.layerUpdates.add(t)}clearLayerUpdates(){this.layerUpdates.clear()}}class aG extends aZ{constructor(t,e,i){super(void 0,t[0].width,t[0].height,e,i,tf),this.isCompressedCubeTexture=!0,this.isCubeTexture=!0,this.image=t}}class a$ extends i7{constructor(t,e,i,s,r,n,a,o,h){super(t,e,i,s,r,n,a,o,h),this.isCanvasTexture=!0,this.needsUpdate=!0}}class aQ extends i7{constructor(t,e,i=tL,s,r,n,a=t_,o=t_,h,l=t$){if(l!==t$&&l!==tQ)throw Error("DepthTexture format must be either THREE.DepthFormat or THREE.DepthStencilFormat");super(null,s,r,n,a,o,l,i,h),this.isDepthTexture=!0,this.image={width:t,height:e},this.flipY=!1,this.generateMipmaps=!1,this.compareFunction=null}copy(t){return super.copy(t),this.source=new i6(Object.assign({},t.image)),this.compareFunction=t.compareFunction,this}toJSON(t){let e=super.toJSON(t);return null!==this.compareFunction&&(e.compareFunction=this.compareFunction),e}}class aK{constructor(){this.type="Curve",this.arcLengthDivisions=200,this.needsUpdate=!1,this.cacheArcLengths=null}getPoint(){console.warn("THREE.Curve: .getPoint() not implemented.")}getPointAt(t,e){let i=this.getUtoTmapping(t);return this.getPoint(i,e)}getPoints(t=5){let e=[];for(let i=0;i<=t;i++)e.push(this.getPoint(i/t));return e}getSpacedPoints(t=5){let e=[];for(let i=0;i<=t;i++)e.push(this.getPointAt(i/t));return e}getLength(){let t=this.getLengths();return t[t.length-1]}getLengths(t=this.arcLengthDivisions){if(this.cacheArcLengths&&this.cacheArcLengths.length===t+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;let e=[],i,s=this.getPoint(0),r=0;e.push(0);for(let n=1;n<=t;n++)e.push(r+=(i=this.getPoint(n/t)).distanceTo(s)),s=i;return this.cacheArcLengths=e,e}updateArcLengths(){this.needsUpdate=!0,this.getLengths()}getUtoTmapping(t,e=null){let i,s=this.getLengths(),r=0,n=s.length;i=e||t*s[n-1];let a=0,o=n-1,h;for(;a<=o;)if((h=s[r=Math.floor(a+(o-a)/2)]-i)<0)a=r+1;else if(h>0)o=r-1;else{o=r;break}if(s[r=o]===i)return r/(n-1);let l=s[r],u=s[r+1];return(r+(i-l)/(u-l))/(n-1)}getTangent(t,e){let i=t-1e-4,s=t+1e-4;i<0&&(i=0),s>1&&(s=1);let r=this.getPoint(i),n=this.getPoint(s),a=e||(r.isVector2?new ij:new sh);return a.copy(n).sub(r).normalize(),a}getTangentAt(t,e){let i=this.getUtoTmapping(t);return this.getTangent(i,e)}computeFrenetFrames(t,e=!1){let i=new sh,s=[],r=[],n=[],a=new sh,o=new sV;for(let e=0;e<=t;e++){let i=e/t;s[e]=this.getTangentAt(i,new sh)}r[0]=new sh,n[0]=new sh;let h=Number.MAX_VALUE,l=Math.abs(s[0].x),u=Math.abs(s[0].y),c=Math.abs(s[0].z);l<=h&&(h=l,i.set(1,0,0)),u<=h&&(h=u,i.set(0,1,0)),c<=h&&i.set(0,0,1),a.crossVectors(s[0],i).normalize(),r[0].crossVectors(s[0],a),n[0].crossVectors(s[0],r[0]);for(let e=1;e<=t;e++){if(r[e]=r[e-1].clone(),n[e]=n[e-1].clone(),a.crossVectors(s[e-1],s[e]),a.length()>Number.EPSILON){a.normalize();let t=Math.acos(iP(s[e-1].dot(s[e]),-1,1));r[e].applyMatrix4(o.makeRotationAxis(a,t))}n[e].crossVectors(s[e],r[e])}if(!0===e){let e=Math.acos(iP(r[0].dot(r[t]),-1,1));e/=t,s[0].dot(a.crossVectors(r[0],r[t]))>0&&(e=-e);for(let i=1;i<=t;i++)r[i].applyMatrix4(o.makeRotationAxis(s[i],e*i)),n[i].crossVectors(s[i],r[i])}return{tangents:s,normals:r,binormals:n}}clone(){return new this.constructor().copy(this)}copy(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}toJSON(){let t={metadata:{version:4.6,type:"Curve",generator:"Curve.toJSON"}};return t.arcLengthDivisions=this.arcLengthDivisions,t.type=this.type,t}fromJSON(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}}class a0 extends aK{constructor(t=0,e=0,i=1,s=1,r=0,n=2*Math.PI,a=!1,o=0){super(),this.isEllipseCurve=!0,this.type="EllipseCurve",this.aX=t,this.aY=e,this.xRadius=i,this.yRadius=s,this.aStartAngle=r,this.aEndAngle=n,this.aClockwise=a,this.aRotation=o}getPoint(t,e=new ij){let i=2*Math.PI,s=this.aEndAngle-this.aStartAngle,r=Math.abs(s)<Number.EPSILON;for(;s<0;)s+=i;for(;s>i;)s-=i;s<Number.EPSILON&&(s=r?0:i),!0!==this.aClockwise||r||(s===i?s=-i:s-=i);let n=this.aStartAngle+t*s,a=this.aX+this.xRadius*Math.cos(n),o=this.aY+this.yRadius*Math.sin(n);if(0!==this.aRotation){let t=Math.cos(this.aRotation),e=Math.sin(this.aRotation),i=a-this.aX,s=o-this.aY;a=i*t-s*e+this.aX,o=i*e+s*t+this.aY}return e.set(a,o)}copy(t){return super.copy(t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this}toJSON(){let t=super.toJSON();return t.aX=this.aX,t.aY=this.aY,t.xRadius=this.xRadius,t.yRadius=this.yRadius,t.aStartAngle=this.aStartAngle,t.aEndAngle=this.aEndAngle,t.aClockwise=this.aClockwise,t.aRotation=this.aRotation,t}fromJSON(t){return super.fromJSON(t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this}}class a1 extends a0{constructor(t,e,i,s,r,n){super(t,e,i,i,s,r,n),this.isArcCurve=!0,this.type="ArcCurve"}}function a2(){let t=0,e=0,i=0,s=0;function r(r,n,a,o){t=r,e=a,i=-3*r+3*n-2*a-o,s=2*r-2*n+a+o}return{initCatmullRom:function(t,e,i,s,n){r(e,i,n*(i-t),n*(s-e))},initNonuniformCatmullRom:function(t,e,i,s,n,a,o){let h=(e-t)/n-(i-t)/(n+a)+(i-e)/a,l=(i-e)/a-(s-e)/(a+o)+(s-i)/o;r(e,i,h*=a,l*=a)},calc:function(r){let n=r*r;return t+e*r+i*n+n*r*s}}}let a3=new sh,a5=new a2,a4=new a2,a6=new a2;class a8 extends aK{constructor(t=[],e=!1,i="centripetal",s=.5){super(),this.isCatmullRomCurve3=!0,this.type="CatmullRomCurve3",this.points=t,this.closed=e,this.curveType=i,this.tension=s}getPoint(t,e=new sh){let i,s,r=this.points,n=r.length,a=(n-!this.closed)*t,o=Math.floor(a),h=a-o;this.closed?o+=o>0?0:(Math.floor(Math.abs(o)/n)+1)*n:0===h&&o===n-1&&(o=n-2,h=1),this.closed||o>0?i=r[(o-1)%n]:(a3.subVectors(r[0],r[1]).add(r[0]),i=a3);let l=r[o%n],u=r[(o+1)%n];if(this.closed||o+2<n?s=r[(o+2)%n]:(a3.subVectors(r[n-1],r[n-2]).add(r[n-1]),s=a3),"centripetal"===this.curveType||"chordal"===this.curveType){let t="chordal"===this.curveType?.5:.25,e=Math.pow(i.distanceToSquared(l),t),r=Math.pow(l.distanceToSquared(u),t),n=Math.pow(u.distanceToSquared(s),t);r<1e-4&&(r=1),e<1e-4&&(e=r),n<1e-4&&(n=r),a5.initNonuniformCatmullRom(i.x,l.x,u.x,s.x,e,r,n),a4.initNonuniformCatmullRom(i.y,l.y,u.y,s.y,e,r,n),a6.initNonuniformCatmullRom(i.z,l.z,u.z,s.z,e,r,n)}else"catmullrom"===this.curveType&&(a5.initCatmullRom(i.x,l.x,u.x,s.x,this.tension),a4.initCatmullRom(i.y,l.y,u.y,s.y,this.tension),a6.initCatmullRom(i.z,l.z,u.z,s.z,this.tension));return e.set(a5.calc(h),a4.calc(h),a6.calc(h)),e}copy(t){super.copy(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(i.clone())}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this}toJSON(){let t=super.toJSON();t.points=[];for(let e=0,i=this.points.length;e<i;e++){let i=this.points[e];t.points.push(i.toArray())}return t.closed=this.closed,t.curveType=this.curveType,t.tension=this.tension,t}fromJSON(t){super.fromJSON(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(new sh().fromArray(i))}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this}}function a9(t,e,i,s,r){let n=(s-e)*.5,a=(r-i)*.5,o=t*t;return t*o*(2*i-2*s+n+a)+(-3*i+3*s-2*n-a)*o+n*t+i}function a7(t,e,i,s){return function(t,e){let i=1-t;return i*i*e}(t,e)+2*(1-t)*t*i+t*t*s}function ot(t,e,i,s,r){return function(t,e){let i=1-t;return i*i*i*e}(t,e)+function(t,e){let i=1-t;return 3*i*i*t*e}(t,i)+3*(1-t)*t*t*s+t*t*t*r}class oe extends aK{constructor(t=new ij,e=new ij,i=new ij,s=new ij){super(),this.isCubicBezierCurve=!0,this.type="CubicBezierCurve",this.v0=t,this.v1=e,this.v2=i,this.v3=s}getPoint(t,e=new ij){let i=this.v0,s=this.v1,r=this.v2,n=this.v3;return e.set(ot(t,i.x,s.x,r.x,n.x),ot(t,i.y,s.y,r.y,n.y)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this}}class oi extends aK{constructor(t=new sh,e=new sh,i=new sh,s=new sh){super(),this.isCubicBezierCurve3=!0,this.type="CubicBezierCurve3",this.v0=t,this.v1=e,this.v2=i,this.v3=s}getPoint(t,e=new sh){let i=this.v0,s=this.v1,r=this.v2,n=this.v3;return e.set(ot(t,i.x,s.x,r.x,n.x),ot(t,i.y,s.y,r.y,n.y),ot(t,i.z,s.z,r.z,n.z)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this}}class os extends aK{constructor(t=new ij,e=new ij){super(),this.isLineCurve=!0,this.type="LineCurve",this.v1=t,this.v2=e}getPoint(t,e=new ij){return 1===t?e.copy(this.v2):(e.copy(this.v2).sub(this.v1),e.multiplyScalar(t).add(this.v1)),e}getPointAt(t,e){return this.getPoint(t,e)}getTangent(t,e=new ij){return e.subVectors(this.v2,this.v1).normalize()}getTangentAt(t,e){return this.getTangent(t,e)}copy(t){return super.copy(t),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class or extends aK{constructor(t=new sh,e=new sh){super(),this.isLineCurve3=!0,this.type="LineCurve3",this.v1=t,this.v2=e}getPoint(t,e=new sh){return 1===t?e.copy(this.v2):(e.copy(this.v2).sub(this.v1),e.multiplyScalar(t).add(this.v1)),e}getPointAt(t,e){return this.getPoint(t,e)}getTangent(t,e=new sh){return e.subVectors(this.v2,this.v1).normalize()}getTangentAt(t,e){return this.getTangent(t,e)}copy(t){return super.copy(t),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class on extends aK{constructor(t=new ij,e=new ij,i=new ij){super(),this.isQuadraticBezierCurve=!0,this.type="QuadraticBezierCurve",this.v0=t,this.v1=e,this.v2=i}getPoint(t,e=new ij){let i=this.v0,s=this.v1,r=this.v2;return e.set(a7(t,i.x,s.x,r.x),a7(t,i.y,s.y,r.y)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class oa extends aK{constructor(t=new sh,e=new sh,i=new sh){super(),this.isQuadraticBezierCurve3=!0,this.type="QuadraticBezierCurve3",this.v0=t,this.v1=e,this.v2=i}getPoint(t,e=new sh){let i=this.v0,s=this.v1,r=this.v2;return e.set(a7(t,i.x,s.x,r.x),a7(t,i.y,s.y,r.y),a7(t,i.z,s.z,r.z)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class oo extends aK{constructor(t=[]){super(),this.isSplineCurve=!0,this.type="SplineCurve",this.points=t}getPoint(t,e=new ij){let i=this.points,s=(i.length-1)*t,r=Math.floor(s),n=s-r,a=i[0===r?r:r-1],o=i[r],h=i[r>i.length-2?i.length-1:r+1],l=i[r>i.length-3?i.length-1:r+2];return e.set(a9(n,a.x,o.x,h.x,l.x),a9(n,a.y,o.y,h.y,l.y)),e}copy(t){super.copy(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(i.clone())}return this}toJSON(){let t=super.toJSON();t.points=[];for(let e=0,i=this.points.length;e<i;e++){let i=this.points[e];t.points.push(i.toArray())}return t}fromJSON(t){super.fromJSON(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(new ij().fromArray(i))}return this}}var oh=Object.freeze({__proto__:null,ArcCurve:a1,CatmullRomCurve3:a8,CubicBezierCurve:oe,CubicBezierCurve3:oi,EllipseCurve:a0,LineCurve:os,LineCurve3:or,QuadraticBezierCurve:on,QuadraticBezierCurve3:oa,SplineCurve:oo});class ol extends aK{constructor(){super(),this.type="CurvePath",this.curves=[],this.autoClose=!1}add(t){this.curves.push(t)}closePath(){let t=this.curves[0].getPoint(0),e=this.curves[this.curves.length-1].getPoint(1);if(!t.equals(e)){let i=!0===t.isVector2?"LineCurve":"LineCurve3";this.curves.push(new oh[i](e,t))}return this}getPoint(t,e){let i=t*this.getLength(),s=this.getCurveLengths(),r=0;for(;r<s.length;){if(s[r]>=i){let t=s[r]-i,n=this.curves[r],a=n.getLength(),o=0===a?0:1-t/a;return n.getPointAt(o,e)}r++}return null}getLength(){let t=this.getCurveLengths();return t[t.length-1]}updateArcLengths(){this.needsUpdate=!0,this.cacheLengths=null,this.getCurveLengths()}getCurveLengths(){if(this.cacheLengths&&this.cacheLengths.length===this.curves.length)return this.cacheLengths;let t=[],e=0;for(let i=0,s=this.curves.length;i<s;i++)t.push(e+=this.curves[i].getLength());return this.cacheLengths=t,t}getSpacedPoints(t=40){let e=[];for(let i=0;i<=t;i++)e.push(this.getPoint(i/t));return this.autoClose&&e.push(e[0]),e}getPoints(t=12){let e,i=[];for(let s=0,r=this.curves;s<r.length;s++){let n=r[s],a=n.isEllipseCurve?2*t:n.isLineCurve||n.isLineCurve3?1:n.isSplineCurve?t*n.points.length:t,o=n.getPoints(a);for(let t=0;t<o.length;t++){let s=o[t];e&&e.equals(s)||(i.push(s),e=s)}}return this.autoClose&&i.length>1&&!i[i.length-1].equals(i[0])&&i.push(i[0]),i}copy(t){super.copy(t),this.curves=[];for(let e=0,i=t.curves.length;e<i;e++){let i=t.curves[e];this.curves.push(i.clone())}return this.autoClose=t.autoClose,this}toJSON(){let t=super.toJSON();t.autoClose=this.autoClose,t.curves=[];for(let e=0,i=this.curves.length;e<i;e++){let i=this.curves[e];t.curves.push(i.toJSON())}return t}fromJSON(t){super.fromJSON(t),this.autoClose=t.autoClose,this.curves=[];for(let e=0,i=t.curves.length;e<i;e++){let i=t.curves[e];this.curves.push(new oh[i.type]().fromJSON(i))}return this}}class ou extends ol{constructor(t){super(),this.type="Path",this.currentPoint=new ij,t&&this.setFromPoints(t)}setFromPoints(t){this.moveTo(t[0].x,t[0].y);for(let e=1,i=t.length;e<i;e++)this.lineTo(t[e].x,t[e].y);return this}moveTo(t,e){return this.currentPoint.set(t,e),this}lineTo(t,e){let i=new os(this.currentPoint.clone(),new ij(t,e));return this.curves.push(i),this.currentPoint.set(t,e),this}quadraticCurveTo(t,e,i,s){let r=new on(this.currentPoint.clone(),new ij(t,e),new ij(i,s));return this.curves.push(r),this.currentPoint.set(i,s),this}bezierCurveTo(t,e,i,s,r,n){let a=new oe(this.currentPoint.clone(),new ij(t,e),new ij(i,s),new ij(r,n));return this.curves.push(a),this.currentPoint.set(r,n),this}splineThru(t){let e=new oo([this.currentPoint.clone()].concat(t));return this.curves.push(e),this.currentPoint.copy(t[t.length-1]),this}arc(t,e,i,s,r,n){let a=this.currentPoint.x,o=this.currentPoint.y;return this.absarc(t+a,e+o,i,s,r,n),this}absarc(t,e,i,s,r,n){return this.absellipse(t,e,i,i,s,r,n),this}ellipse(t,e,i,s,r,n,a,o){let h=this.currentPoint.x,l=this.currentPoint.y;return this.absellipse(t+h,e+l,i,s,r,n,a,o),this}absellipse(t,e,i,s,r,n,a,o){let h=new a0(t,e,i,s,r,n,a,o);if(this.curves.length>0){let t=h.getPoint(0);t.equals(this.currentPoint)||this.lineTo(t.x,t.y)}this.curves.push(h);let l=h.getPoint(1);return this.currentPoint.copy(l),this}copy(t){return super.copy(t),this.currentPoint.copy(t.currentPoint),this}toJSON(){let t=super.toJSON();return t.currentPoint=this.currentPoint.toArray(),t}fromJSON(t){return super.fromJSON(t),this.currentPoint.fromArray(t.currentPoint),this}}class oc extends rY{constructor(t=[new ij(0,-.5),new ij(.5,0),new ij(0,.5)],e=12,i=0,s=2*Math.PI){super(),this.type="LatheGeometry",this.parameters={points:t,segments:e,phiStart:i,phiLength:s},e=Math.floor(e),s=iP(s,0,2*Math.PI);let r=[],n=[],a=[],o=[],h=[],l=1/e,u=new sh,c=new ij,d=new sh,p=new sh,m=new sh,y=0,f=0;for(let e=0;e<=t.length-1;e++)switch(e){case 0:y=t[e+1].x-t[e].x,d.x=+(f=t[e+1].y-t[e].y),d.y=-y,d.z=0*f,m.copy(d),d.normalize(),o.push(d.x,d.y,d.z);break;case t.length-1:o.push(m.x,m.y,m.z);break;default:y=t[e+1].x-t[e].x,d.x=+(f=t[e+1].y-t[e].y),d.y=-y,d.z=0*f,p.copy(d),d.x+=m.x,d.y+=m.y,d.z+=m.z,d.normalize(),o.push(d.x,d.y,d.z),m.copy(p)}for(let r=0;r<=e;r++){let d=i+r*l*s,p=Math.sin(d),m=Math.cos(d);for(let i=0;i<=t.length-1;i++){u.x=t[i].x*p,u.y=t[i].y,u.z=t[i].x*m,n.push(u.x,u.y,u.z),c.x=r/e,c.y=i/(t.length-1),a.push(c.x,c.y);let s=o[3*i+0]*p,l=o[3*i+1],d=o[3*i+0]*m;h.push(s,l,d)}}for(let i=0;i<e;i++)for(let e=0;e<t.length-1;e++){let s=e+i*t.length,n=s+t.length,a=s+t.length+1,o=s+1;r.push(s,n,o),r.push(a,o,n)}this.setIndex(r),this.setAttribute("position",new rU(n,3)),this.setAttribute("uv",new rU(a,2)),this.setAttribute("normal",new rU(h,3))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new oc(t.points,t.segments,t.phiStart,t.phiLength)}}class od extends oc{constructor(t=1,e=1,i=4,s=8){let r=new ou;r.absarc(0,-e/2,t,1.5*Math.PI,0),r.absarc(0,e/2,t,0,.5*Math.PI),super(r.getPoints(i),s),this.type="CapsuleGeometry",this.parameters={radius:t,length:e,capSegments:i,radialSegments:s}}static fromJSON(t){return new od(t.radius,t.length,t.capSegments,t.radialSegments)}}class op extends rY{constructor(t=1,e=32,i=0,s=2*Math.PI){super(),this.type="CircleGeometry",this.parameters={radius:t,segments:e,thetaStart:i,thetaLength:s},e=Math.max(3,e);let r=[],n=[],a=[],o=[],h=new sh,l=new ij;n.push(0,0,0),a.push(0,0,1),o.push(.5,.5);for(let r=0,u=3;r<=e;r++,u+=3){let c=i+r/e*s;h.x=t*Math.cos(c),h.y=t*Math.sin(c),n.push(h.x,h.y,h.z),a.push(0,0,1),l.x=(n[u]/t+1)/2,l.y=(n[u+1]/t+1)/2,o.push(l.x,l.y)}for(let t=1;t<=e;t++)r.push(t,t+1,0);this.setIndex(r),this.setAttribute("position",new rU(n,3)),this.setAttribute("normal",new rU(a,3)),this.setAttribute("uv",new rU(o,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new op(t.radius,t.segments,t.thetaStart,t.thetaLength)}}class om extends rY{constructor(t=1,e=1,i=1,s=32,r=1,n=!1,a=0,o=2*Math.PI){super(),this.type="CylinderGeometry",this.parameters={radiusTop:t,radiusBottom:e,height:i,radialSegments:s,heightSegments:r,openEnded:n,thetaStart:a,thetaLength:o};let h=this;s=Math.floor(s),r=Math.floor(r);let l=[],u=[],c=[],d=[],p=0,m=[],y=i/2,f=0;function g(i){let r=p,n=new ij,m=new sh,g=0,x=!0===i?t:e,b=!0===i?1:-1;for(let t=1;t<=s;t++)u.push(0,y*b,0),c.push(0,b,0),d.push(.5,.5),p++;let v=p;for(let t=0;t<=s;t++){let e=t/s*o+a,i=Math.cos(e),r=Math.sin(e);m.x=x*r,m.y=y*b,m.z=x*i,u.push(m.x,m.y,m.z),c.push(0,b,0),n.x=.5*i+.5,n.y=.5*r*b+.5,d.push(n.x,n.y),p++}for(let t=0;t<s;t++){let e=r+t,s=v+t;!0===i?l.push(s,s+1,e):l.push(s+1,s,e),g+=3}h.addGroup(f,g,!0===i?1:2),f+=g}(function(){let n=new sh,g=new sh,x=0,b=(e-t)/i;for(let h=0;h<=r;h++){let l=[],f=h/r,x=f*(e-t)+t;for(let t=0;t<=s;t++){let e=t/s,r=e*o+a,h=Math.sin(r),m=Math.cos(r);g.x=x*h,g.y=-f*i+y,g.z=x*m,u.push(g.x,g.y,g.z),n.set(h,b,m).normalize(),c.push(n.x,n.y,n.z),d.push(e,1-f),l.push(p++)}m.push(l)}for(let i=0;i<s;i++)for(let s=0;s<r;s++){let n=m[s][i],a=m[s+1][i],o=m[s+1][i+1],h=m[s][i+1];(t>0||0!==s)&&(l.push(n,a,h),x+=3),(e>0||s!==r-1)&&(l.push(a,o,h),x+=3)}h.addGroup(f,x,0),f+=x})(),!1===n&&(t>0&&g(!0),e>0&&g(!1)),this.setIndex(l),this.setAttribute("position",new rU(u,3)),this.setAttribute("normal",new rU(c,3)),this.setAttribute("uv",new rU(d,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new om(t.radiusTop,t.radiusBottom,t.height,t.radialSegments,t.heightSegments,t.openEnded,t.thetaStart,t.thetaLength)}}class oy extends om{constructor(t=1,e=1,i=32,s=1,r=!1,n=0,a=2*Math.PI){super(0,t,e,i,s,r,n,a),this.type="ConeGeometry",this.parameters={radius:t,height:e,radialSegments:i,heightSegments:s,openEnded:r,thetaStart:n,thetaLength:a}}static fromJSON(t){return new oy(t.radius,t.height,t.radialSegments,t.heightSegments,t.openEnded,t.thetaStart,t.thetaLength)}}class of extends rY{constructor(t=[],e=[],i=1,s=0){super(),this.type="PolyhedronGeometry",this.parameters={vertices:t,indices:e,radius:i,detail:s};let r=[],n=[];function a(t){r.push(t.x,t.y,t.z)}function o(e,i){let s=3*e;i.x=t[s+0],i.y=t[s+1],i.z=t[s+2]}function h(t,e,i,s){s<0&&1===t.x&&(n[e]=t.x-1),0===i.x&&0===i.z&&(n[e]=s/2/Math.PI+.5)}function l(t){return Math.atan2(t.z,-t.x)}(function(t){let i=new sh,s=new sh,r=new sh;for(let n=0;n<e.length;n+=3)o(e[n+0],i),o(e[n+1],s),o(e[n+2],r),function(t,e,i,s){let r=s+1,n=[];for(let s=0;s<=r;s++){n[s]=[];let a=t.clone().lerp(i,s/r),o=e.clone().lerp(i,s/r),h=r-s;for(let t=0;t<=h;t++)0===t&&s===r?n[s][t]=a:n[s][t]=a.clone().lerp(o,t/h)}for(let t=0;t<r;t++)for(let e=0;e<2*(r-t)-1;e++){let i=Math.floor(e/2);e%2==0?(a(n[t][i+1]),a(n[t+1][i]),a(n[t][i])):(a(n[t][i+1]),a(n[t+1][i+1]),a(n[t+1][i]))}}(i,s,r,t)})(s),function(t){let e=new sh;for(let i=0;i<r.length;i+=3)e.x=r[i+0],e.y=r[i+1],e.z=r[i+2],e.normalize().multiplyScalar(t),r[i+0]=e.x,r[i+1]=e.y,r[i+2]=e.z}(i),function(){let t=new sh;for(let i=0;i<r.length;i+=3){var e;t.x=r[i+0],t.y=r[i+1],t.z=r[i+2];let s=l(t)/2/Math.PI+.5,a=Math.atan2(-(e=t).y,Math.sqrt(e.x*e.x+e.z*e.z))/Math.PI+.5;n.push(s,1-a)}(function(){let t=new sh,e=new sh,i=new sh,s=new sh,a=new ij,o=new ij,u=new ij;for(let c=0,d=0;c<r.length;c+=9,d+=6){t.set(r[c+0],r[c+1],r[c+2]),e.set(r[c+3],r[c+4],r[c+5]),i.set(r[c+6],r[c+7],r[c+8]),a.set(n[d+0],n[d+1]),o.set(n[d+2],n[d+3]),u.set(n[d+4],n[d+5]),s.copy(t).add(e).add(i).divideScalar(3);let p=l(s);h(a,d+0,t,p),h(o,d+2,e,p),h(u,d+4,i,p)}})(),function(){for(let t=0;t<n.length;t+=6){let e=n[t+0],i=n[t+2],s=n[t+4],r=Math.max(e,i,s),a=Math.min(e,i,s);r>.9&&a<.1&&(e<.2&&(n[t+0]+=1),i<.2&&(n[t+2]+=1),s<.2&&(n[t+4]+=1))}}()}(),this.setAttribute("position",new rU(r,3)),this.setAttribute("normal",new rU(r.slice(),3)),this.setAttribute("uv",new rU(n,2)),0===s?this.computeVertexNormals():this.normalizeNormals()}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new of(t.vertices,t.indices,t.radius,t.details)}}class og extends of{constructor(t=1,e=0){let i=(1+Math.sqrt(5))/2,s=1/i;super([-1,-1,-1,-1,-1,1,-1,1,-1,-1,1,1,1,-1,-1,1,-1,1,1,1,-1,1,1,1,0,-s,-i,0,-s,i,0,s,-i,0,s,i,-s,-i,0,-s,i,0,s,-i,0,s,i,0,-i,0,-s,i,0,-s,-i,0,s,i,0,s],[3,11,7,3,7,15,3,15,13,7,19,17,7,17,6,7,6,15,17,4,8,17,8,10,17,10,6,8,0,16,8,16,2,8,2,10,0,12,1,0,1,18,0,18,16,6,10,2,6,2,13,6,13,15,2,16,18,2,18,3,2,3,13,18,1,9,18,9,11,18,11,3,4,14,12,4,12,0,4,0,8,11,9,5,11,5,19,11,19,7,19,5,14,19,14,4,19,4,17,1,12,14,1,14,5,1,5,9],t,e),this.type="DodecahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new og(t.radius,t.detail)}}let ox=new sh,ob=new sh,ov=new sh,ow=new ry;class oM extends rY{constructor(t=null,e=1){if(super(),this.type="EdgesGeometry",this.parameters={geometry:t,thresholdAngle:e},null!==t){let i=Math.cos(iB*e),s=t.getIndex(),r=t.getAttribute("position"),n=s?s.count:r.count,a=[0,0,0],o=["a","b","c"],h=[,,,],l={},u=[];for(let t=0;t<n;t+=3){s?(a[0]=s.getX(t),a[1]=s.getX(t+1),a[2]=s.getX(t+2)):(a[0]=t,a[1]=t+1,a[2]=t+2);let{a:e,b:n,c}=ow;if(e.fromBufferAttribute(r,a[0]),n.fromBufferAttribute(r,a[1]),c.fromBufferAttribute(r,a[2]),ow.getNormal(ov),h[0]=`${Math.round(1e4*e.x)},${Math.round(1e4*e.y)},${Math.round(1e4*e.z)}`,h[1]=`${Math.round(1e4*n.x)},${Math.round(1e4*n.y)},${Math.round(1e4*n.z)}`,h[2]=`${Math.round(1e4*c.x)},${Math.round(1e4*c.y)},${Math.round(1e4*c.z)}`,h[0]!==h[1]&&h[1]!==h[2]&&h[2]!==h[0])for(let t=0;t<3;t++){let e=(t+1)%3,s=h[t],r=h[e],n=ow[o[t]],c=ow[o[e]],d=`${s}_${r}`,p=`${r}_${s}`;p in l&&l[p]?(ov.dot(l[p].normal)<=i&&(u.push(n.x,n.y,n.z),u.push(c.x,c.y,c.z)),l[p]=null):d in l||(l[d]={index0:a[t],index1:a[e],normal:ov.clone()})}}for(let t in l)if(l[t]){let{index0:e,index1:i}=l[t];ox.fromBufferAttribute(r,e),ob.fromBufferAttribute(r,i),u.push(ox.x,ox.y,ox.z),u.push(ob.x,ob.y,ob.z)}this.setAttribute("position",new rU(u,3))}}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}}class oS extends ou{constructor(t){super(t),this.uuid=iE(),this.type="Shape",this.holes=[]}getPointsHoles(t){let e=[];for(let i=0,s=this.holes.length;i<s;i++)e[i]=this.holes[i].getPoints(t);return e}extractPoints(t){return{shape:this.getPoints(t),holes:this.getPointsHoles(t)}}copy(t){super.copy(t),this.holes=[];for(let e=0,i=t.holes.length;e<i;e++){let i=t.holes[e];this.holes.push(i.clone())}return this}toJSON(){let t=super.toJSON();t.uuid=this.uuid,t.holes=[];for(let e=0,i=this.holes.length;e<i;e++){let i=this.holes[e];t.holes.push(i.toJSON())}return t}fromJSON(t){super.fromJSON(t),this.uuid=t.uuid,this.holes=[];for(let e=0,i=t.holes.length;e<i;e++){let i=t.holes[e];this.holes.push(new ou().fromJSON(i))}return this}}function o_(t,e,i,s,r){let n;if(r===function(t,e,i,s){let r=0;for(let n=e,a=i-s;n<i;n+=s)r+=(t[a]-t[n])*(t[n+1]+t[a+1]),a=n;return r}(t,e,i,s)>0)for(let r=e;r<i;r+=s)n=oF(r/s|0,t[r],t[r+1],n);else for(let r=i-s;r>=e;r-=s)n=oF(r/s|0,t[r],t[r+1],n);return n&&oB(n,n.next)&&(oV(n),n=n.next),n}function oA(t,e){if(!t)return t;e||(e=t);let i=t,s;do if(s=!1,!i.steiner&&(oB(i,i.next)||0===ok(i.prev,i,i.next))){if(oV(i),(i=e=i.prev)===i.next)break;s=!0}else i=i.next;while(s||i!==e);return e}function oT(t,e){let i=t.x-e.x;return 0===i&&0==(i=t.y-e.y)&&(i=(t.next.y-t.y)/(t.next.x-t.x)-(e.next.y-e.y)/(e.next.x-e.x)),i}function oz(t,e,i,s,r){return(t=((t=((t=((t=((t=(t-i)*r|0)|t<<8)&0xff00ff)|t<<4)&0xf0f0f0f)|t<<2)&0x33333333)|t<<1)&0x55555555)|(e=((e=((e=((e=((e=(e-s)*r|0)|e<<8)&0xff00ff)|e<<4)&0xf0f0f0f)|e<<2)&0x33333333)|e<<1)&0x55555555)<<1}function oI(t,e,i,s,r,n,a,o){return(r-a)*(e-o)>=(t-a)*(n-o)&&(t-a)*(s-o)>=(i-a)*(e-o)&&(i-a)*(n-o)>=(r-a)*(s-o)}function oC(t,e,i,s,r,n,a,o){return(t!==a||e!==o)&&oI(t,e,i,s,r,n,a,o)}function ok(t,e,i){return(e.y-t.y)*(i.x-e.x)-(e.x-t.x)*(i.y-e.y)}function oB(t,e){return t.x===e.x&&t.y===e.y}function oR(t,e,i,s){let r=oP(ok(t,e,i)),n=oP(ok(t,e,s)),a=oP(ok(i,s,t)),o=oP(ok(i,s,e));return!!(r!==n&&a!==o||0===r&&oE(t,i,e)||0===n&&oE(t,s,e)||0===a&&oE(i,t,s)||0===o&&oE(i,e,s))}function oE(t,e,i){return e.x<=Math.max(t.x,i.x)&&e.x>=Math.min(t.x,i.x)&&e.y<=Math.max(t.y,i.y)&&e.y>=Math.min(t.y,i.y)}function oP(t){return t>0?1:t<0?-1:0}function oO(t,e){return 0>ok(t.prev,t,t.next)?ok(t,e,t.next)>=0&&ok(t,t.prev,e)>=0:0>ok(t,e,t.prev)||0>ok(t,t.next,e)}function oN(t,e){let i=oL(t.i,t.x,t.y),s=oL(e.i,e.x,e.y),r=t.next,n=e.prev;return t.next=e,e.prev=t,i.next=r,r.prev=i,s.next=i,i.prev=s,n.next=s,s.prev=n,s}function oF(t,e,i,s){let r=oL(t,e,i);return s?(r.next=s.next,r.prev=s,s.next.prev=r,s.next=r):(r.prev=r,r.next=r),r}function oV(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function oL(t,e,i){return{i:t,x:e,y:i,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}class oj{static triangulate(t,e,i=2){return function(t,e,i=2){let s,r,n,a=e&&e.length,o=a?e[0]*i:t.length,h=o_(t,0,o,i,!0),l=[];if(!h||h.next===h.prev)return l;if(a&&(h=function(t,e,i,s){let r=[];for(let i=0,n=e.length;i<n;i++){let a=e[i]*s,o=i<n-1?e[i+1]*s:t.length,h=o_(t,a,o,s,!1);h===h.next&&(h.steiner=!0),r.push(function(t){let e=t,i=t;do(e.x<i.x||e.x===i.x&&e.y<i.y)&&(i=e),e=e.next;while(e!==t);return i}(h))}r.sort(oT);for(let t=0;t<r.length;t++)i=function(t,e){let i=function(t,e){let i,s=e,r=t.x,n=t.y,a=-1/0;if(oB(t,s))return s;do{if(oB(t,s.next))return s.next;if(n<=s.y&&n>=s.next.y&&s.next.y!==s.y){let t=s.x+(n-s.y)*(s.next.x-s.x)/(s.next.y-s.y);if(t<=r&&t>a&&(a=t,i=s.x<s.next.x?s:s.next,t===r))return i}s=s.next}while(s!==e);if(!i)return null;let o=i,h=i.x,l=i.y,u=1/0;s=i;do{if(r>=s.x&&s.x>=h&&r!==s.x&&oI(n<l?r:a,n,h,l,n<l?a:r,n,s.x,s.y)){var c,d;let e=Math.abs(n-s.y)/(r-s.x);oO(s,t)&&(e<u||e===u&&(s.x>i.x||s.x===i.x&&(c=i,d=s,0>ok(c.prev,c,d.prev)&&0>ok(d.next,c,c.next))))&&(i=s,u=e)}s=s.next}while(s!==o);return i}(t,e);if(!i)return e;let s=oN(i,t);return oA(s,s.next),oA(i,i.next)}(r[t],i);return i}(t,e,h,i)),t.length>80*i){s=1/0,r=1/0;let e=-1/0,a=-1/0;for(let n=i;n<o;n+=i){let i=t[n],o=t[n+1];i<s&&(s=i),o<r&&(r=o),i>e&&(e=i),o>a&&(a=o)}n=0!==(n=Math.max(e-s,a-r))?32767/n:0}return function t(e,i,s,r,n,a,o){if(!e)return;!o&&a&&function(t,e,i,s){let r=t;do 0===r.z&&(r.z=oz(r.x,r.y,e,i,s)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next;while(r!==t);r.prevZ.nextZ=null,r.prevZ=null,function(t){let e,i=1;do{let s,r=t;t=null;let n=null;for(e=0;r;){e++;let a=r,o=0;for(let t=0;t<i&&(o++,a=a.nextZ);t++);let h=i;for(;o>0||h>0&&a;)0!==o&&(0===h||!a||r.z<=a.z)?(s=r,r=r.nextZ,o--):(s=a,a=a.nextZ,h--),n?n.nextZ=s:t=s,s.prevZ=n,n=s;r=a}n.nextZ=null,i*=2}while(e>1)}(r)}(e,r,n,a);let h=e;for(;e.prev!==e.next;){let l=e.prev,u=e.next;if(a?function(t,e,i,s){let r=t.prev,n=t.next;if(ok(r,t,n)>=0)return!1;let a=r.x,o=t.x,h=n.x,l=r.y,u=t.y,c=n.y,d=Math.min(a,o,h),p=Math.min(l,u,c),m=Math.max(a,o,h),y=Math.max(l,u,c),f=oz(d,p,e,i,s),g=oz(m,y,e,i,s),x=t.prevZ,b=t.nextZ;for(;x&&x.z>=f&&b&&b.z<=g;){if(x.x>=d&&x.x<=m&&x.y>=p&&x.y<=y&&x!==r&&x!==n&&oC(a,l,o,u,h,c,x.x,x.y)&&ok(x.prev,x,x.next)>=0||(x=x.prevZ,b.x>=d&&b.x<=m&&b.y>=p&&b.y<=y&&b!==r&&b!==n&&oC(a,l,o,u,h,c,b.x,b.y)&&ok(b.prev,b,b.next)>=0))return!1;b=b.nextZ}for(;x&&x.z>=f;){if(x.x>=d&&x.x<=m&&x.y>=p&&x.y<=y&&x!==r&&x!==n&&oC(a,l,o,u,h,c,x.x,x.y)&&ok(x.prev,x,x.next)>=0)return!1;x=x.prevZ}for(;b&&b.z<=g;){if(b.x>=d&&b.x<=m&&b.y>=p&&b.y<=y&&b!==r&&b!==n&&oC(a,l,o,u,h,c,b.x,b.y)&&ok(b.prev,b,b.next)>=0)return!1;b=b.nextZ}return!0}(e,r,n,a):function(t){let e=t.prev,i=t.next;if(ok(e,t,i)>=0)return!1;let s=e.x,r=t.x,n=i.x,a=e.y,o=t.y,h=i.y,l=Math.min(s,r,n),u=Math.min(a,o,h),c=Math.max(s,r,n),d=Math.max(a,o,h),p=i.next;for(;p!==e;){if(p.x>=l&&p.x<=c&&p.y>=u&&p.y<=d&&oC(s,a,r,o,n,h,p.x,p.y)&&ok(p.prev,p,p.next)>=0)return!1;p=p.next}return!0}(e)){i.push(l.i,e.i,u.i),oV(e),e=u.next,h=u.next;continue}if((e=u)===h){o?1===o?t(e=function(t,e){let i=t;do{let s=i.prev,r=i.next.next;!oB(s,r)&&oR(s,i,i.next,r)&&oO(s,r)&&oO(r,s)&&(e.push(s.i,i.i,r.i),oV(i),oV(i.next),i=t=r),i=i.next}while(i!==t);return oA(i)}(oA(e),i),i,s,r,n,a,2):2===o&&function(e,i,s,r,n,a){let o=e;do{let e=o.next.next;for(;e!==o.prev;){var h,l;if(o.i!==e.i&&(h=o,l=e,h.next.i!==l.i&&h.prev.i!==l.i&&!function(t,e){let i=t;do{if(i.i!==t.i&&i.next.i!==t.i&&i.i!==e.i&&i.next.i!==e.i&&oR(i,i.next,t,e))return!0;i=i.next}while(i!==t);return!1}(h,l)&&(oO(h,l)&&oO(l,h)&&function(t,e){let i=t,s=!1,r=(t.x+e.x)/2,n=(t.y+e.y)/2;do i.y>n!=i.next.y>n&&i.next.y!==i.y&&r<(i.next.x-i.x)*(n-i.y)/(i.next.y-i.y)+i.x&&(s=!s),i=i.next;while(i!==t);return s}(h,l)&&(ok(h.prev,h,l.prev)||ok(h,l.prev,l))||oB(h,l)&&ok(h.prev,h,h.next)>0&&ok(l.prev,l,l.next)>0))){let h=oN(o,e);o=oA(o,o.next),h=oA(h,h.next),t(o,i,s,r,n,a,0),t(h,i,s,r,n,a,0);return}e=e.next}o=o.next}while(o!==e)}(e,i,s,r,n,a):t(oA(e),i,s,r,n,a,1);break}}}(h,l,i,s,r,n,0),l}(t,e,i)}}class oU{static area(t){let e=t.length,i=0;for(let s=e-1,r=0;r<e;s=r++)i+=t[s].x*t[r].y-t[r].x*t[s].y;return .5*i}static isClockWise(t){return 0>oU.area(t)}static triangulateShape(t,e){let i=[],s=[],r=[];oW(t),oD(i,t);let n=t.length;e.forEach(oW);for(let t=0;t<e.length;t++)s.push(n),n+=e[t].length,oD(i,e[t]);let a=oj.triangulate(i,s);for(let t=0;t<a.length;t+=3)r.push(a.slice(t,t+3));return r}}function oW(t){let e=t.length;e>2&&t[e-1].equals(t[0])&&t.pop()}function oD(t,e){for(let i=0;i<e.length;i++)t.push(e[i].x),t.push(e[i].y)}class oH extends rY{constructor(t=new oS([new ij(.5,.5),new ij(-.5,.5),new ij(-.5,-.5),new ij(.5,-.5)]),e={}){super(),this.type="ExtrudeGeometry",this.parameters={shapes:t,options:e},t=Array.isArray(t)?t:[t];let i=this,s=[],r=[];for(let n=0,a=t.length;n<a;n++)!function(t){let n,a,o,h,l=[],u=void 0!==e.curveSegments?e.curveSegments:12,c=void 0!==e.steps?e.steps:1,d=void 0!==e.depth?e.depth:1,p=void 0===e.bevelEnabled||e.bevelEnabled,m=void 0!==e.bevelThickness?e.bevelThickness:.2,y=void 0!==e.bevelSize?e.bevelSize:m-.1,f=void 0!==e.bevelOffset?e.bevelOffset:0,g=void 0!==e.bevelSegments?e.bevelSegments:3,x=e.extrudePath,b=void 0!==e.UVGenerator?e.UVGenerator:oq,v,w=!1;x&&(v=x.getSpacedPoints(c),w=!0,p=!1,n=x.computeFrenetFrames(c,!1),a=new sh,o=new sh,h=new sh),p||(g=0,m=0,y=0,f=0);let M=t.extractPoints(u),S=M.shape,_=M.holes;if(!oU.isClockWise(S)){S=S.reverse();for(let t=0,e=_.length;t<e;t++){let e=_[t];oU.isClockWise(e)&&(_[t]=e.reverse())}}function A(t){let e=1e-10*1e-10,i=t[0];for(let s=1;s<=t.length;s++){let r=s%t.length,n=t[r],a=n.x-i.x,o=n.y-i.y,h=a*a+o*o,l=Math.max(Math.abs(n.x),Math.abs(n.y),Math.abs(i.x),Math.abs(i.y));if(h<=e*l*l){t.splice(r,1),s--;continue}i=n}}A(S),_.forEach(A);let T=_.length,z=S;for(let t=0;t<T;t++){let e=_[t];S=S.concat(e)}function I(t,e,i){return e||console.error("THREE.ExtrudeGeometry: vec does not exist"),t.clone().addScaledVector(e,i)}let C=S.length;function k(t,e,i){let s,r,n,a=t.x-e.x,o=t.y-e.y,h=i.x-t.x,l=i.y-t.y,u=a*a+o*o;if(Math.abs(a*l-o*h)>Number.EPSILON){let c=Math.sqrt(u),d=Math.sqrt(h*h+l*l),p=e.x-o/c,m=e.y+a/c,y=((i.x-l/d-p)*l-(i.y+h/d-m)*h)/(a*l-o*h),f=(s=p+a*y-t.x)*s+(r=m+o*y-t.y)*r;if(f<=2)return new ij(s,r);n=Math.sqrt(f/2)}else{let t=!1;a>Number.EPSILON?h>Number.EPSILON&&(t=!0):a<-Number.EPSILON?h<-Number.EPSILON&&(t=!0):Math.sign(o)===Math.sign(l)&&(t=!0),t?(s=-o,r=a,n=Math.sqrt(u)):(s=a,r=o,n=Math.sqrt(u/2))}return new ij(s/n,r/n)}let B=[];for(let t=0,e=z.length,i=e-1,s=t+1;t<e;t++,i++,s++)i===e&&(i=0),s===e&&(s=0),B[t]=k(z[t],z[i],z[s]);let R=[],E,P=B.concat();for(let t=0;t<T;t++){let e=_[t];E=[];for(let t=0,i=e.length,s=i-1,r=t+1;t<i;t++,s++,r++)s===i&&(s=0),r===i&&(r=0),E[t]=k(e[t],e[s],e[r]);R.push(E),P=P.concat(E)}let O=[],N=[];for(let t=0;t<g;t++){let e=t/g,i=m*Math.cos(e*Math.PI/2),s=y*Math.sin(e*Math.PI/2)+f;for(let t=0,r=z.length;t<r;t++){let r=I(z[t],B[t],s);U(r.x,r.y,-i),0==e&&O.push(r)}for(let t=0;t<T;t++){let r=_[t];E=R[t];let n=[];for(let t=0,a=r.length;t<a;t++){let a=I(r[t],E[t],s);U(a.x,a.y,-i),0==e&&n.push(a)}0==e&&N.push(n)}}let F=oU.triangulateShape(O,N),V=F.length,L=y+f;for(let t=0;t<C;t++){let e=p?I(S[t],P[t],L):S[t];w?(o.copy(n.normals[0]).multiplyScalar(e.x),a.copy(n.binormals[0]).multiplyScalar(e.y),h.copy(v[0]).add(o).add(a),U(h.x,h.y,h.z)):U(e.x,e.y,0)}for(let t=1;t<=c;t++)for(let e=0;e<C;e++){let i=p?I(S[e],P[e],L):S[e];w?(o.copy(n.normals[t]).multiplyScalar(i.x),a.copy(n.binormals[t]).multiplyScalar(i.y),h.copy(v[t]).add(o).add(a),U(h.x,h.y,h.z)):U(i.x,i.y,d/c*t)}for(let t=g-1;t>=0;t--){let e=t/g,i=m*Math.cos(e*Math.PI/2),s=y*Math.sin(e*Math.PI/2)+f;for(let t=0,e=z.length;t<e;t++){let e=I(z[t],B[t],s);U(e.x,e.y,d+i)}for(let t=0,e=_.length;t<e;t++){let e=_[t];E=R[t];for(let t=0,r=e.length;t<r;t++){let r=I(e[t],E[t],s);w?U(r.x,r.y+v[c-1].y,v[c-1].x+i):U(r.x,r.y,d+i)}}}function j(t,e){let r=t.length;for(;--r>=0;){let n=r,a=r-1;a<0&&(a=t.length-1);for(let t=0,r=c+2*g;t<r;t++){let r=C*t,o=C*(t+1);!function(t,e,r,n){D(t),D(e),D(n),D(e),D(r),D(n);let a=s.length/3,o=b.generateSideWallUV(i,s,a-6,a-3,a-2,a-1);H(o[0]),H(o[1]),H(o[3]),H(o[1]),H(o[2]),H(o[3])}(e+n+r,e+a+r,e+a+o,e+n+o)}}}function U(t,e,i){l.push(t),l.push(e),l.push(i)}function W(t,e,r){D(t),D(e),D(r);let n=s.length/3,a=b.generateTopUV(i,s,n-3,n-2,n-1);H(a[0]),H(a[1]),H(a[2])}function D(t){s.push(l[3*t+0]),s.push(l[3*t+1]),s.push(l[3*t+2])}function H(t){r.push(t.x),r.push(t.y)}(function(){let t=s.length/3;if(p){let t=0,e=0*C;for(let t=0;t<V;t++){let i=F[t];W(i[2]+e,i[1]+e,i[0]+e)}e=C*(c+2*g);for(let t=0;t<V;t++){let i=F[t];W(i[0]+e,i[1]+e,i[2]+e)}}else{for(let t=0;t<V;t++){let e=F[t];W(e[2],e[1],e[0])}for(let t=0;t<V;t++){let e=F[t];W(e[0]+C*c,e[1]+C*c,e[2]+C*c)}}i.addGroup(t,s.length/3-t,0)})(),function(){let t=s.length/3,e=0;j(z,0),e+=z.length;for(let t=0,i=_.length;t<i;t++){let i=_[t];j(i,e),e+=i.length}i.addGroup(t,s.length/3-t,1)}()}(t[n]);this.setAttribute("position",new rU(s,3)),this.setAttribute("uv",new rU(r,2)),this.computeVertexNormals()}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){let t=super.toJSON();return function(t,e,i){if(i.shapes=[],Array.isArray(t))for(let e=0,s=t.length;e<s;e++){let s=t[e];i.shapes.push(s.uuid)}else i.shapes.push(t.uuid);return i.options=Object.assign({},e),void 0!==e.extrudePath&&(i.options.extrudePath=e.extrudePath.toJSON()),i}(this.parameters.shapes,this.parameters.options,t)}static fromJSON(t,e){let i=[];for(let s=0,r=t.shapes.length;s<r;s++){let r=e[t.shapes[s]];i.push(r)}let s=t.options.extrudePath;return void 0!==s&&(t.options.extrudePath=new oh[s.type]().fromJSON(s)),new oH(i,t.options)}}let oq={generateTopUV:function(t,e,i,s,r){let n=e[3*i],a=e[3*i+1],o=e[3*s],h=e[3*s+1],l=e[3*r],u=e[3*r+1];return[new ij(n,a),new ij(o,h),new ij(l,u)]},generateSideWallUV:function(t,e,i,s,r,n){let a=e[3*i],o=e[3*i+1],h=e[3*i+2],l=e[3*s],u=e[3*s+1],c=e[3*s+2],d=e[3*r],p=e[3*r+1],m=e[3*r+2],y=e[3*n],f=e[3*n+1],g=e[3*n+2];return Math.abs(o-u)<Math.abs(a-l)?[new ij(a,1-h),new ij(l,1-c),new ij(d,1-m),new ij(y,1-g)]:[new ij(o,1-h),new ij(u,1-c),new ij(p,1-m),new ij(f,1-g)]}};class oJ extends of{constructor(t=1,e=0){let i=(1+Math.sqrt(5))/2;super([-1,i,0,1,i,0,-1,-i,0,1,-i,0,0,-1,i,0,1,i,0,-1,-i,0,1,-i,i,0,-1,i,0,1,-i,0,-1,-i,0,1],[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1],t,e),this.type="IcosahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new oJ(t.radius,t.detail)}}class oX extends of{constructor(t=1,e=0){super([1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1],[0,2,4,0,4,3,0,3,5,0,5,2,1,2,5,1,5,3,1,3,4,1,4,2],t,e),this.type="OctahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new oX(t.radius,t.detail)}}class oZ extends rY{constructor(t=1,e=1,i=1,s=1){super(),this.type="PlaneGeometry",this.parameters={width:t,height:e,widthSegments:i,heightSegments:s};let r=t/2,n=e/2,a=Math.floor(i),o=Math.floor(s),h=a+1,l=o+1,u=t/a,c=e/o,d=[],p=[],m=[],y=[];for(let t=0;t<l;t++){let e=t*c-n;for(let i=0;i<h;i++){let s=i*u-r;p.push(s,-e,0),m.push(0,0,1),y.push(i/a),y.push(1-t/o)}}for(let t=0;t<o;t++)for(let e=0;e<a;e++){let i=e+h*t,s=e+h*(t+1),r=e+1+h*(t+1),n=e+1+h*t;d.push(i,s,n),d.push(s,r,n)}this.setIndex(d),this.setAttribute("position",new rU(p,3)),this.setAttribute("normal",new rU(m,3)),this.setAttribute("uv",new rU(y,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new oZ(t.width,t.height,t.widthSegments,t.heightSegments)}}class oY extends rY{constructor(t=.5,e=1,i=32,s=1,r=0,n=2*Math.PI){super(),this.type="RingGeometry",this.parameters={innerRadius:t,outerRadius:e,thetaSegments:i,phiSegments:s,thetaStart:r,thetaLength:n},i=Math.max(3,i);let a=[],o=[],h=[],l=[],u=t,c=(e-t)/(s=Math.max(1,s)),d=new sh,p=new ij;for(let t=0;t<=s;t++){for(let t=0;t<=i;t++){let s=r+t/i*n;d.x=u*Math.cos(s),d.y=u*Math.sin(s),o.push(d.x,d.y,d.z),h.push(0,0,1),p.x=(d.x/e+1)/2,p.y=(d.y/e+1)/2,l.push(p.x,p.y)}u+=c}for(let t=0;t<s;t++){let e=t*(i+1);for(let t=0;t<i;t++){let s=t+e,r=s+i+1,n=s+i+2,o=s+1;a.push(s,r,o),a.push(r,n,o)}}this.setIndex(a),this.setAttribute("position",new rU(o,3)),this.setAttribute("normal",new rU(h,3)),this.setAttribute("uv",new rU(l,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new oY(t.innerRadius,t.outerRadius,t.thetaSegments,t.phiSegments,t.thetaStart,t.thetaLength)}}class oG extends rY{constructor(t=new oS([new ij(0,.5),new ij(-.5,-.5),new ij(.5,-.5)]),e=12){super(),this.type="ShapeGeometry",this.parameters={shapes:t,curveSegments:e};let i=[],s=[],r=[],n=[],a=0,o=0;if(!1===Array.isArray(t))h(t);else for(let e=0;e<t.length;e++)h(t[e]),this.addGroup(a,o,e),a+=o,o=0;function h(t){let a=s.length/3,h=t.extractPoints(e),l=h.shape,u=h.holes;!1===oU.isClockWise(l)&&(l=l.reverse());for(let t=0,e=u.length;t<e;t++){let e=u[t];!0===oU.isClockWise(e)&&(u[t]=e.reverse())}let c=oU.triangulateShape(l,u);for(let t=0,e=u.length;t<e;t++){let e=u[t];l=l.concat(e)}for(let t=0,e=l.length;t<e;t++){let e=l[t];s.push(e.x,e.y,0),r.push(0,0,1),n.push(e.x,e.y)}for(let t=0,e=c.length;t<e;t++){let e=c[t],s=e[0]+a,r=e[1]+a,n=e[2]+a;i.push(s,r,n),o+=3}}this.setIndex(i),this.setAttribute("position",new rU(s,3)),this.setAttribute("normal",new rU(r,3)),this.setAttribute("uv",new rU(n,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){let t=super.toJSON();return function(t,e){if(e.shapes=[],Array.isArray(t))for(let i=0,s=t.length;i<s;i++){let s=t[i];e.shapes.push(s.uuid)}else e.shapes.push(t.uuid);return e}(this.parameters.shapes,t)}static fromJSON(t,e){let i=[];for(let s=0,r=t.shapes.length;s<r;s++){let r=e[t.shapes[s]];i.push(r)}return new oG(i,t.curveSegments)}}class o$ extends rY{constructor(t=1,e=32,i=16,s=0,r=2*Math.PI,n=0,a=Math.PI){super(),this.type="SphereGeometry",this.parameters={radius:t,widthSegments:e,heightSegments:i,phiStart:s,phiLength:r,thetaStart:n,thetaLength:a},e=Math.max(3,Math.floor(e)),i=Math.max(2,Math.floor(i));let o=Math.min(n+a,Math.PI),h=0,l=[],u=new sh,c=new sh,d=[],p=[],m=[],y=[];for(let d=0;d<=i;d++){let f=[],g=d/i,x=0;0===d&&0===n?x=.5/e:d===i&&o===Math.PI&&(x=-.5/e);for(let i=0;i<=e;i++){let o=i/e;u.x=-t*Math.cos(s+o*r)*Math.sin(n+g*a),u.y=t*Math.cos(n+g*a),u.z=t*Math.sin(s+o*r)*Math.sin(n+g*a),p.push(u.x,u.y,u.z),c.copy(u).normalize(),m.push(c.x,c.y,c.z),y.push(o+x,1-g),f.push(h++)}l.push(f)}for(let t=0;t<i;t++)for(let s=0;s<e;s++){let e=l[t][s+1],r=l[t][s],a=l[t+1][s],h=l[t+1][s+1];(0!==t||n>0)&&d.push(e,r,h),(t!==i-1||o<Math.PI)&&d.push(r,a,h)}this.setIndex(d),this.setAttribute("position",new rU(p,3)),this.setAttribute("normal",new rU(m,3)),this.setAttribute("uv",new rU(y,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new o$(t.radius,t.widthSegments,t.heightSegments,t.phiStart,t.phiLength,t.thetaStart,t.thetaLength)}}class oQ extends of{constructor(t=1,e=0){super([1,1,1,-1,-1,1,-1,1,-1,1,-1,-1],[2,1,0,0,3,2,1,3,0,2,3,1],t,e),this.type="TetrahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new oQ(t.radius,t.detail)}}class oK extends rY{constructor(t=1,e=.4,i=12,s=48,r=2*Math.PI){super(),this.type="TorusGeometry",this.parameters={radius:t,tube:e,radialSegments:i,tubularSegments:s,arc:r},i=Math.floor(i),s=Math.floor(s);let n=[],a=[],o=[],h=[],l=new sh,u=new sh,c=new sh;for(let n=0;n<=i;n++)for(let d=0;d<=s;d++){let p=d/s*r,m=n/i*Math.PI*2;u.x=(t+e*Math.cos(m))*Math.cos(p),u.y=(t+e*Math.cos(m))*Math.sin(p),u.z=e*Math.sin(m),a.push(u.x,u.y,u.z),l.x=t*Math.cos(p),l.y=t*Math.sin(p),c.subVectors(u,l).normalize(),o.push(c.x,c.y,c.z),h.push(d/s),h.push(n/i)}for(let t=1;t<=i;t++)for(let e=1;e<=s;e++){let i=(s+1)*t+e-1,r=(s+1)*(t-1)+e-1,a=(s+1)*(t-1)+e,o=(s+1)*t+e;n.push(i,r,o),n.push(r,a,o)}this.setIndex(n),this.setAttribute("position",new rU(a,3)),this.setAttribute("normal",new rU(o,3)),this.setAttribute("uv",new rU(h,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new oK(t.radius,t.tube,t.radialSegments,t.tubularSegments,t.arc)}}class o0 extends rY{constructor(t=1,e=.4,i=64,s=8,r=2,n=3){super(),this.type="TorusKnotGeometry",this.parameters={radius:t,tube:e,tubularSegments:i,radialSegments:s,p:r,q:n},i=Math.floor(i),s=Math.floor(s);let a=[],o=[],h=[],l=[],u=new sh,c=new sh,d=new sh,p=new sh,m=new sh,y=new sh,f=new sh;for(let a=0;a<=i;++a){let x=a/i*r*Math.PI*2;g(x,r,n,t,d),g(x+.01,r,n,t,p),y.subVectors(p,d),f.addVectors(p,d),m.crossVectors(y,f),f.crossVectors(m,y),m.normalize(),f.normalize();for(let t=0;t<=s;++t){let r=t/s*Math.PI*2,n=-e*Math.cos(r),p=e*Math.sin(r);u.x=d.x+(n*f.x+p*m.x),u.y=d.y+(n*f.y+p*m.y),u.z=d.z+(n*f.z+p*m.z),o.push(u.x,u.y,u.z),c.subVectors(u,d).normalize(),h.push(c.x,c.y,c.z),l.push(a/i),l.push(t/s)}}for(let t=1;t<=i;t++)for(let e=1;e<=s;e++){let i=(s+1)*(t-1)+(e-1),r=(s+1)*t+(e-1),n=(s+1)*t+e,o=(s+1)*(t-1)+e;a.push(i,r,o),a.push(r,n,o)}function g(t,e,i,s,r){let n=Math.cos(t),a=Math.sin(t),o=i/e*t,h=Math.cos(o);r.x=s*(2+h)*.5*n,r.y=s*(2+h)*a*.5,r.z=s*Math.sin(o)*.5}this.setIndex(a),this.setAttribute("position",new rU(o,3)),this.setAttribute("normal",new rU(h,3)),this.setAttribute("uv",new rU(l,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new o0(t.radius,t.tube,t.tubularSegments,t.radialSegments,t.p,t.q)}}class o1 extends rY{constructor(t=new oa(new sh(-1,-1,0),new sh(-1,1,0),new sh(1,1,0)),e=64,i=1,s=8,r=!1){super(),this.type="TubeGeometry",this.parameters={path:t,tubularSegments:e,radius:i,radialSegments:s,closed:r};let n=t.computeFrenetFrames(e,r);this.tangents=n.tangents,this.normals=n.normals,this.binormals=n.binormals;let a=new sh,o=new sh,h=new ij,l=new sh,u=[],c=[],d=[],p=[];function m(r){l=t.getPointAt(r/e,l);let h=n.normals[r],d=n.binormals[r];for(let t=0;t<=s;t++){let e=t/s*Math.PI*2,r=Math.sin(e),n=-Math.cos(e);o.x=n*h.x+r*d.x,o.y=n*h.y+r*d.y,o.z=n*h.z+r*d.z,o.normalize(),c.push(o.x,o.y,o.z),a.x=l.x+i*o.x,a.y=l.y+i*o.y,a.z=l.z+i*o.z,u.push(a.x,a.y,a.z)}}(function(){for(let t=0;t<e;t++)m(t);m(!1===r?e:0),function(){for(let t=0;t<=e;t++)for(let i=0;i<=s;i++)h.x=t/e,h.y=i/s,d.push(h.x,h.y)}(),function(){for(let t=1;t<=e;t++)for(let e=1;e<=s;e++){let i=(s+1)*(t-1)+(e-1),r=(s+1)*t+(e-1),n=(s+1)*t+e,a=(s+1)*(t-1)+e;p.push(i,r,a),p.push(r,n,a)}}()})(),this.setIndex(p),this.setAttribute("position",new rU(u,3)),this.setAttribute("normal",new rU(c,3)),this.setAttribute("uv",new rU(d,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){let t=super.toJSON();return t.path=this.parameters.path.toJSON(),t}static fromJSON(t){return new o1(new oh[t.path.type]().fromJSON(t.path),t.tubularSegments,t.radius,t.radialSegments,t.closed)}}class o2 extends rY{constructor(t=null){if(super(),this.type="WireframeGeometry",this.parameters={geometry:t},null!==t){let e=[],i=new Set,s=new sh,r=new sh;if(null!==t.index){let n=t.attributes.position,a=t.index,o=t.groups;0===o.length&&(o=[{start:0,count:a.count,materialIndex:0}]);for(let t=0,h=o.length;t<h;++t){let h=o[t],l=h.start,u=h.count;for(let t=l,o=l+u;t<o;t+=3)for(let o=0;o<3;o++){let h=a.getX(t+o),l=a.getX(t+(o+1)%3);s.fromBufferAttribute(n,h),r.fromBufferAttribute(n,l),!0===o3(s,r,i)&&(e.push(s.x,s.y,s.z),e.push(r.x,r.y,r.z))}}}else{let n=t.attributes.position;for(let t=0,a=n.count/3;t<a;t++)for(let a=0;a<3;a++){let o=3*t+a,h=3*t+(a+1)%3;s.fromBufferAttribute(n,o),r.fromBufferAttribute(n,h),!0===o3(s,r,i)&&(e.push(s.x,s.y,s.z),e.push(r.x,r.y,r.z))}}this.setAttribute("position",new rU(e,3))}}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}}function o3(t,e,i){let s=`${t.x},${t.y},${t.z}-${e.x},${e.y},${e.z}`,r=`${e.x},${e.y},${e.z}-${t.x},${t.y},${t.z}`;return!0!==i.has(s)&&!0!==i.has(r)&&(i.add(s),i.add(r),!0)}var o5=Object.freeze({__proto__:null,BoxGeometry:r7,CapsuleGeometry:od,CircleGeometry:op,ConeGeometry:oy,CylinderGeometry:om,DodecahedronGeometry:og,EdgesGeometry:oM,ExtrudeGeometry:oH,IcosahedronGeometry:oJ,LatheGeometry:oc,OctahedronGeometry:oX,PlaneGeometry:oZ,PolyhedronGeometry:of,RingGeometry:oY,ShapeGeometry:oG,SphereGeometry:o$,TetrahedronGeometry:oQ,TorusGeometry:oK,TorusKnotGeometry:o0,TubeGeometry:o1,WireframeGeometry:o2});class o4 extends rS{constructor(t){super(),this.isShadowMaterial=!0,this.type="ShadowMaterial",this.color=new rv(0),this.transparent=!0,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.fog=t.fog,this}}class o6 extends nr{constructor(t){super(t),this.isRawShaderMaterial=!0,this.type="RawShaderMaterial"}}class o8 extends rS{constructor(t){super(),this.isMeshStandardMaterial=!0,this.type="MeshStandardMaterial",this.defines={STANDARD:""},this.color=new rv(0xffffff),this.roughness=1,this.metalness=0,this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new rv(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=eq,this.normalScale=new ij(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.roughnessMap=null,this.metalnessMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new sZ,this.envMapIntensity=1,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.defines={STANDARD:""},this.color.copy(t.color),this.roughness=t.roughness,this.metalness=t.metalness,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.roughnessMap=t.roughnessMap,this.metalnessMap=t.metalnessMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.envMapIntensity=t.envMapIntensity,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}}class o9 extends o8{constructor(t){super(),this.isMeshPhysicalMaterial=!0,this.defines={STANDARD:"",PHYSICAL:""},this.type="MeshPhysicalMaterial",this.anisotropyRotation=0,this.anisotropyMap=null,this.clearcoatMap=null,this.clearcoatRoughness=0,this.clearcoatRoughnessMap=null,this.clearcoatNormalScale=new ij(1,1),this.clearcoatNormalMap=null,this.ior=1.5,Object.defineProperty(this,"reflectivity",{get:function(){return iP(2.5*(this.ior-1)/(this.ior+1),0,1)},set:function(t){this.ior=(1+.4*t)/(1-.4*t)}}),this.iridescenceMap=null,this.iridescenceIOR=1.3,this.iridescenceThicknessRange=[100,400],this.iridescenceThicknessMap=null,this.sheenColor=new rv(0),this.sheenColorMap=null,this.sheenRoughness=1,this.sheenRoughnessMap=null,this.transmissionMap=null,this.thickness=0,this.thicknessMap=null,this.attenuationDistance=1/0,this.attenuationColor=new rv(1,1,1),this.specularIntensity=1,this.specularIntensityMap=null,this.specularColor=new rv(1,1,1),this.specularColorMap=null,this._anisotropy=0,this._clearcoat=0,this._dispersion=0,this._iridescence=0,this._sheen=0,this._transmission=0,this.setValues(t)}get anisotropy(){return this._anisotropy}set anisotropy(t){this._anisotropy>0!=t>0&&this.version++,this._anisotropy=t}get clearcoat(){return this._clearcoat}set clearcoat(t){this._clearcoat>0!=t>0&&this.version++,this._clearcoat=t}get iridescence(){return this._iridescence}set iridescence(t){this._iridescence>0!=t>0&&this.version++,this._iridescence=t}get dispersion(){return this._dispersion}set dispersion(t){this._dispersion>0!=t>0&&this.version++,this._dispersion=t}get sheen(){return this._sheen}set sheen(t){this._sheen>0!=t>0&&this.version++,this._sheen=t}get transmission(){return this._transmission}set transmission(t){this._transmission>0!=t>0&&this.version++,this._transmission=t}copy(t){return super.copy(t),this.defines={STANDARD:"",PHYSICAL:""},this.anisotropy=t.anisotropy,this.anisotropyRotation=t.anisotropyRotation,this.anisotropyMap=t.anisotropyMap,this.clearcoat=t.clearcoat,this.clearcoatMap=t.clearcoatMap,this.clearcoatRoughness=t.clearcoatRoughness,this.clearcoatRoughnessMap=t.clearcoatRoughnessMap,this.clearcoatNormalMap=t.clearcoatNormalMap,this.clearcoatNormalScale.copy(t.clearcoatNormalScale),this.dispersion=t.dispersion,this.ior=t.ior,this.iridescence=t.iridescence,this.iridescenceMap=t.iridescenceMap,this.iridescenceIOR=t.iridescenceIOR,this.iridescenceThicknessRange=[...t.iridescenceThicknessRange],this.iridescenceThicknessMap=t.iridescenceThicknessMap,this.sheen=t.sheen,this.sheenColor.copy(t.sheenColor),this.sheenColorMap=t.sheenColorMap,this.sheenRoughness=t.sheenRoughness,this.sheenRoughnessMap=t.sheenRoughnessMap,this.transmission=t.transmission,this.transmissionMap=t.transmissionMap,this.thickness=t.thickness,this.thicknessMap=t.thicknessMap,this.attenuationDistance=t.attenuationDistance,this.attenuationColor.copy(t.attenuationColor),this.specularIntensity=t.specularIntensity,this.specularIntensityMap=t.specularIntensityMap,this.specularColor.copy(t.specularColor),this.specularColorMap=t.specularColorMap,this}}class o7 extends rS{constructor(t){super(),this.isMeshPhongMaterial=!0,this.type="MeshPhongMaterial",this.color=new rv(0xffffff),this.specular=new rv(1118481),this.shininess=30,this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new rv(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=eq,this.normalScale=new ij(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new sZ,this.combine=ti,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.specular.copy(t.specular),this.shininess=t.shininess,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}}class ht extends rS{constructor(t){super(),this.isMeshToonMaterial=!0,this.defines={TOON:""},this.type="MeshToonMaterial",this.color=new rv(0xffffff),this.map=null,this.gradientMap=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new rv(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=eq,this.normalScale=new ij(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.alphaMap=null,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.gradientMap=t.gradientMap,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.alphaMap=t.alphaMap,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.fog=t.fog,this}}class he extends rS{constructor(t){super(),this.isMeshNormalMaterial=!0,this.type="MeshNormalMaterial",this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=eq,this.normalScale=new ij(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.flatShading=!1,this.setValues(t)}copy(t){return super.copy(t),this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.flatShading=t.flatShading,this}}class hi extends rS{constructor(t){super(),this.isMeshLambertMaterial=!0,this.type="MeshLambertMaterial",this.color=new rv(0xffffff),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new rv(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=eq,this.normalScale=new ij(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new sZ,this.combine=ti,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}}class hs extends rS{constructor(t){super(),this.isMeshDepthMaterial=!0,this.type="MeshDepthMaterial",this.depthPacking=eU,this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.setValues(t)}copy(t){return super.copy(t),this.depthPacking=t.depthPacking,this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this}}class hr extends rS{constructor(t){super(),this.isMeshDistanceMaterial=!0,this.type="MeshDistanceMaterial",this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.setValues(t)}copy(t){return super.copy(t),this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this}}class hn extends rS{constructor(t){super(),this.isMeshMatcapMaterial=!0,this.defines={MATCAP:""},this.type="MeshMatcapMaterial",this.color=new rv(0xffffff),this.matcap=null,this.map=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=eq,this.normalScale=new ij(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.alphaMap=null,this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.defines={MATCAP:""},this.color.copy(t.color),this.matcap=t.matcap,this.map=t.map,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.alphaMap=t.alphaMap,this.flatShading=t.flatShading,this.fog=t.fog,this}}class ha extends a_{constructor(t){super(),this.isLineDashedMaterial=!0,this.type="LineDashedMaterial",this.scale=1,this.dashSize=3,this.gapSize=1,this.setValues(t)}copy(t){return super.copy(t),this.scale=t.scale,this.dashSize=t.dashSize,this.gapSize=t.gapSize,this}}function ho(t,e){return t&&t.constructor!==e?"number"==typeof e.BYTES_PER_ELEMENT?new e(t):Array.prototype.slice.call(t):t}function hh(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function hl(t){let e=t.length,i=Array(e);for(let t=0;t!==e;++t)i[t]=t;return i.sort(function(e,i){return t[e]-t[i]}),i}function hu(t,e,i){let s=t.length,r=new t.constructor(s);for(let n=0,a=0;a!==s;++n){let s=i[n]*e;for(let i=0;i!==e;++i)r[a++]=t[s+i]}return r}function hc(t,e,i,s){let r=1,n=t[0];for(;void 0!==n&&void 0===n[s];)n=t[r++];if(void 0===n)return;let a=n[s];if(void 0!==a)if(Array.isArray(a))do void 0!==(a=n[s])&&(e.push(n.time),i.push(...a)),n=t[r++];while(void 0!==n);else if(void 0!==a.toArray)do void 0!==(a=n[s])&&(e.push(n.time),a.toArray(i,i.length)),n=t[r++];while(void 0!==n);else do void 0!==(a=n[s])&&(e.push(n.time),i.push(a)),n=t[r++];while(void 0!==n)}class hd{static convertArray(t,e){return ho(t,e)}static isTypedArray(t){return hh(t)}static getKeyframeOrder(t){return hl(t)}static sortedArray(t,e,i){return hu(t,e,i)}static flattenJSON(t,e,i,s){hc(t,e,i,s)}static subclip(t,e,i,s,r=30){return function(t,e,i,s,r=30){let n=t.clone();n.name=e;let a=[];for(let t=0;t<n.tracks.length;++t){let e=n.tracks[t],o=e.getValueSize(),h=[],l=[];for(let t=0;t<e.times.length;++t){let n=e.times[t]*r;if(!(n<i)&&!(n>=s)){h.push(e.times[t]);for(let i=0;i<o;++i)l.push(e.values[t*o+i])}}0!==h.length&&(e.times=ho(h,e.times.constructor),e.values=ho(l,e.values.constructor),a.push(e))}n.tracks=a;let o=1/0;for(let t=0;t<n.tracks.length;++t)o>n.tracks[t].times[0]&&(o=n.tracks[t].times[0]);for(let t=0;t<n.tracks.length;++t)n.tracks[t].shift(-1*o);return n.resetDuration(),n}(t,e,i,s,r)}static makeClipAdditive(t,e=0,i=t,s=30){return function(t,e=0,i=t,s=30){s<=0&&(s=30);let r=i.tracks.length,n=e/s;for(let e=0;e<r;++e){let s,r=i.tracks[e],a=r.ValueTypeName;if("bool"===a||"string"===a)continue;let o=t.tracks.find(function(t){return t.name===r.name&&t.ValueTypeName===a});if(void 0===o)continue;let h=0,l=r.getValueSize();r.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline&&(h=l/3);let u=0,c=o.getValueSize();o.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline&&(u=c/3);let d=r.times.length-1;if(n<=r.times[0]){let t=h,e=l-h;s=r.values.slice(t,e)}else if(n>=r.times[d]){let t=d*l+h,e=t+l-h;s=r.values.slice(t,e)}else{let t=r.createInterpolant(),e=h,i=l-h;t.evaluate(n),s=t.resultBuffer.slice(e,i)}"quaternion"===a&&new so().fromArray(s).normalize().conjugate().toArray(s);let p=o.times.length;for(let t=0;t<p;++t){let e=t*c+u;if("quaternion"===a)so.multiplyQuaternionsFlat(o.values,e,s,0,o.values,e);else{let t=c-2*u;for(let i=0;i<t;++i)o.values[e+i]-=s[i]}}}return t.blendMode=eF,t}(t,e,i,s)}}class hp{constructor(t,e,i,s){this.parameterPositions=t,this._cachedIndex=0,this.resultBuffer=void 0!==s?s:new e.constructor(i),this.sampleValues=e,this.valueSize=i,this.settings=null,this.DefaultSettings_={}}evaluate(t){let e=this.parameterPositions,i=this._cachedIndex,s=e[i],r=e[i-1];t:{e:{let n;i:{s:if(!(t<s)){for(let n=i+2;;){if(void 0===s){if(t<r)break s;return i=e.length,this._cachedIndex=i,this.copySampleValue_(i-1)}if(i===n)break;if(r=s,t<(s=e[++i]))break e}n=e.length;break i}if(!(t>=r)){let a=e[1];t<a&&(i=2,r=a);for(let n=i-2;;){if(void 0===r)return this._cachedIndex=0,this.copySampleValue_(0);if(i===n)break;if(s=r,t>=(r=e[--i-1]))break e}n=i,i=0;break i}break t}for(;i<n;){let s=i+n>>>1;t<e[s]?n=s:i=s+1}if(s=e[i],void 0===(r=e[i-1]))return this._cachedIndex=0,this.copySampleValue_(0);if(void 0===s)return i=e.length,this._cachedIndex=i,this.copySampleValue_(i-1)}this._cachedIndex=i,this.intervalChanged_(i,r,s)}return this.interpolate_(i,r,t,s)}getSettings_(){return this.settings||this.DefaultSettings_}copySampleValue_(t){let e=this.resultBuffer,i=this.sampleValues,s=this.valueSize,r=t*s;for(let t=0;t!==s;++t)e[t]=i[r+t];return e}interpolate_(){throw Error("call to abstract method")}intervalChanged_(){}}class hm extends hp{constructor(t,e,i,s){super(t,e,i,s),this._weightPrev=-0,this._offsetPrev=-0,this._weightNext=-0,this._offsetNext=-0,this.DefaultSettings_={endingStart:eE,endingEnd:eE}}intervalChanged_(t,e,i){let s=this.parameterPositions,r=t-2,n=t+1,a=s[r],o=s[n];if(void 0===a)switch(this.getSettings_().endingStart){case eP:r=t,a=2*e-i;break;case eO:r=s.length-2,a=e+s[r]-s[r+1];break;default:r=t,a=i}if(void 0===o)switch(this.getSettings_().endingEnd){case eP:n=t,o=2*i-e;break;case eO:n=1,o=i+s[1]-s[0];break;default:n=t-1,o=e}let h=(i-e)*.5,l=this.valueSize;this._weightPrev=h/(e-a),this._weightNext=h/(o-i),this._offsetPrev=r*l,this._offsetNext=n*l}interpolate_(t,e,i,s){let r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=t*a,h=o-a,l=this._offsetPrev,u=this._offsetNext,c=this._weightPrev,d=this._weightNext,p=(i-e)/(s-e),m=p*p,y=m*p,f=-c*y+2*c*m-c*p,g=(1+c)*y+(-1.5-2*c)*m+(-.5+c)*p+1,x=(-1-d)*y+(1.5+d)*m+.5*p,b=d*y-d*m;for(let t=0;t!==a;++t)r[t]=f*n[l+t]+g*n[h+t]+x*n[o+t]+b*n[u+t];return r}}class hy extends hp{constructor(t,e,i,s){super(t,e,i,s)}interpolate_(t,e,i,s){let r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=t*a,h=o-a,l=(i-e)/(s-e),u=1-l;for(let t=0;t!==a;++t)r[t]=n[h+t]*u+n[o+t]*l;return r}}class hf extends hp{constructor(t,e,i,s){super(t,e,i,s)}interpolate_(t){return this.copySampleValue_(t-1)}}class hg{constructor(t,e,i,s){if(void 0===t)throw Error("THREE.KeyframeTrack: track name is undefined");if(void 0===e||0===e.length)throw Error("THREE.KeyframeTrack: no keyframes in track named "+t);this.name=t,this.times=ho(e,this.TimeBufferType),this.values=ho(i,this.ValueBufferType),this.setInterpolation(s||this.DefaultInterpolation)}static toJSON(t){let e,i=t.constructor;if(i.toJSON!==this.toJSON)e=i.toJSON(t);else{e={name:t.name,times:ho(t.times,Array),values:ho(t.values,Array)};let i=t.getInterpolation();i!==t.DefaultInterpolation&&(e.interpolation=i)}return e.type=t.ValueTypeName,e}InterpolantFactoryMethodDiscrete(t){return new hf(this.times,this.values,this.getValueSize(),t)}InterpolantFactoryMethodLinear(t){return new hy(this.times,this.values,this.getValueSize(),t)}InterpolantFactoryMethodSmooth(t){return new hm(this.times,this.values,this.getValueSize(),t)}setInterpolation(t){let e;switch(t){case ek:e=this.InterpolantFactoryMethodDiscrete;break;case eB:e=this.InterpolantFactoryMethodLinear;break;case eR:e=this.InterpolantFactoryMethodSmooth}if(void 0===e){let e="unsupported interpolation for "+this.ValueTypeName+" keyframe track named "+this.name;if(void 0===this.createInterpolant)if(t!==this.DefaultInterpolation)this.setInterpolation(this.DefaultInterpolation);else throw Error(e);return console.warn("THREE.KeyframeTrack:",e),this}return this.createInterpolant=e,this}getInterpolation(){switch(this.createInterpolant){case this.InterpolantFactoryMethodDiscrete:return ek;case this.InterpolantFactoryMethodLinear:return eB;case this.InterpolantFactoryMethodSmooth:return eR}}getValueSize(){return this.values.length/this.times.length}shift(t){if(0!==t){let e=this.times;for(let i=0,s=e.length;i!==s;++i)e[i]+=t}return this}scale(t){if(1!==t){let e=this.times;for(let i=0,s=e.length;i!==s;++i)e[i]*=t}return this}trim(t,e){let i=this.times,s=i.length,r=0,n=s-1;for(;r!==s&&i[r]<t;)++r;for(;-1!==n&&i[n]>e;)--n;if(++n,0!==r||n!==s){r>=n&&(r=(n=Math.max(n,1))-1);let t=this.getValueSize();this.times=i.slice(r,n),this.values=this.values.slice(r*t,n*t)}return this}validate(){let t=!0,e=this.getValueSize();e-Math.floor(e)!=0&&(console.error("THREE.KeyframeTrack: Invalid value size in track.",this),t=!1);let i=this.times,s=this.values,r=i.length;0===r&&(console.error("THREE.KeyframeTrack: Track is empty.",this),t=!1);let n=null;for(let e=0;e!==r;e++){let s=i[e];if("number"==typeof s&&isNaN(s)){console.error("THREE.KeyframeTrack: Time is not a valid number.",this,e,s),t=!1;break}if(null!==n&&n>s){console.error("THREE.KeyframeTrack: Out of order keys.",this,e,s,n),t=!1;break}n=s}if(void 0!==s&&hh(s))for(let e=0,i=s.length;e!==i;++e){let i=s[e];if(isNaN(i)){console.error("THREE.KeyframeTrack: Value is not a valid number.",this,e,i),t=!1;break}}return t}optimize(){let t=this.times.slice(),e=this.values.slice(),i=this.getValueSize(),s=this.getInterpolation()===eR,r=t.length-1,n=1;for(let a=1;a<r;++a){let r=!1,o=t[a];if(o!==t[a+1]&&(1!==a||o!==t[0]))if(s)r=!0;else{let t=a*i,s=t-i,n=t+i;for(let a=0;a!==i;++a){let i=e[t+a];if(i!==e[s+a]||i!==e[n+a]){r=!0;break}}}if(r){if(a!==n){t[n]=t[a];let s=a*i,r=n*i;for(let t=0;t!==i;++t)e[r+t]=e[s+t]}++n}}if(r>0){t[n]=t[r];for(let t=r*i,s=n*i,a=0;a!==i;++a)e[s+a]=e[t+a];++n}return n!==t.length?(this.times=t.slice(0,n),this.values=e.slice(0,n*i)):(this.times=t,this.values=e),this}clone(){let t=this.times.slice(),e=this.values.slice(),i=new this.constructor(this.name,t,e);return i.createInterpolant=this.createInterpolant,i}}hg.prototype.ValueTypeName="",hg.prototype.TimeBufferType=Float32Array,hg.prototype.ValueBufferType=Float32Array,hg.prototype.DefaultInterpolation=eB;class hx extends hg{constructor(t,e,i){super(t,e,i)}}hx.prototype.ValueTypeName="bool",hx.prototype.ValueBufferType=Array,hx.prototype.DefaultInterpolation=ek,hx.prototype.InterpolantFactoryMethodLinear=void 0,hx.prototype.InterpolantFactoryMethodSmooth=void 0;class hb extends hg{constructor(t,e,i,s){super(t,e,i,s)}}hb.prototype.ValueTypeName="color";class hv extends hg{constructor(t,e,i,s){super(t,e,i,s)}}hv.prototype.ValueTypeName="number";class hw extends hp{constructor(t,e,i,s){super(t,e,i,s)}interpolate_(t,e,i,s){let r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=(i-e)/(s-e),h=t*a;for(let t=h+a;h!==t;h+=4)so.slerpFlat(r,0,n,h-a,n,h,o);return r}}class hM extends hg{constructor(t,e,i,s){super(t,e,i,s)}InterpolantFactoryMethodLinear(t){return new hw(this.times,this.values,this.getValueSize(),t)}}hM.prototype.ValueTypeName="quaternion",hM.prototype.InterpolantFactoryMethodSmooth=void 0;class hS extends hg{constructor(t,e,i){super(t,e,i)}}hS.prototype.ValueTypeName="string",hS.prototype.ValueBufferType=Array,hS.prototype.DefaultInterpolation=ek,hS.prototype.InterpolantFactoryMethodLinear=void 0,hS.prototype.InterpolantFactoryMethodSmooth=void 0;class h_ extends hg{constructor(t,e,i,s){super(t,e,i,s)}}h_.prototype.ValueTypeName="vector";class hA{constructor(t="",e=-1,i=[],s=eN){this.name=t,this.tracks=i,this.duration=e,this.blendMode=s,this.uuid=iE(),this.duration<0&&this.resetDuration()}static parse(t){let e=[],i=t.tracks,s=1/(t.fps||1);for(let t=0,r=i.length;t!==r;++t)e.push((function(t){if(void 0===t.type)throw Error("THREE.KeyframeTrack: track type undefined, can not parse");let e=function(t){switch(t.toLowerCase()){case"scalar":case"double":case"float":case"number":case"integer":return hv;case"vector":case"vector2":case"vector3":case"vector4":return h_;case"color":return hb;case"quaternion":return hM;case"bool":case"boolean":return hx;case"string":return hS}throw Error("THREE.KeyframeTrack: Unsupported typeName: "+t)}(t.type);if(void 0===t.times){let e=[],i=[];hc(t.keys,e,i,"value"),t.times=e,t.values=i}return void 0!==e.parse?e.parse(t):new e(t.name,t.times,t.values,t.interpolation)})(i[t]).scale(s));let r=new this(t.name,t.duration,e,t.blendMode);return r.uuid=t.uuid,r}static toJSON(t){let e=[],i=t.tracks,s={name:t.name,duration:t.duration,tracks:e,uuid:t.uuid,blendMode:t.blendMode};for(let t=0,s=i.length;t!==s;++t)e.push(hg.toJSON(i[t]));return s}static CreateFromMorphTargetSequence(t,e,i,s){let r=e.length,n=[];for(let t=0;t<r;t++){let a=[],o=[];a.push((t+r-1)%r,t,(t+1)%r),o.push(0,1,0);let h=hl(a);a=hu(a,1,h),o=hu(o,1,h),s||0!==a[0]||(a.push(r),o.push(o[0])),n.push(new hv(".morphTargetInfluences["+e[t].name+"]",a,o).scale(1/i))}return new this(t,-1,n)}static findByName(t,e){let i=t;Array.isArray(t)||(i=t.geometry&&t.geometry.animations||t.animations);for(let t=0;t<i.length;t++)if(i[t].name===e)return i[t];return null}static CreateClipsFromMorphTargetSequences(t,e,i){let s={},r=/^([\w-]*?)([\d]+)$/;for(let e=0,i=t.length;e<i;e++){let i=t[e],n=i.name.match(r);if(n&&n.length>1){let t=n[1],e=s[t];e||(s[t]=e=[]),e.push(i)}}let n=[];for(let t in s)n.push(this.CreateFromMorphTargetSequence(t,s[t],e,i));return n}static parseAnimation(t,e){if(console.warn("THREE.AnimationClip: parseAnimation() is deprecated and will be removed with r185"),!t)return console.error("THREE.AnimationClip: No animation in JSONLoader data."),null;let i=function(t,e,i,s,r){if(0!==i.length){let n=[],a=[];hc(i,n,a,s),0!==n.length&&r.push(new t(e,n,a))}},s=[],r=t.name||"default",n=t.fps||30,a=t.blendMode,o=t.length||-1,h=t.hierarchy||[];for(let t=0;t<h.length;t++){let r=h[t].keys;if(r&&0!==r.length)if(r[0].morphTargets){let t,e={};for(t=0;t<r.length;t++)if(r[t].morphTargets)for(let i=0;i<r[t].morphTargets.length;i++)e[r[t].morphTargets[i]]=-1;for(let i in e){let e=[],n=[];for(let s=0;s!==r[t].morphTargets.length;++s){let s=r[t];e.push(s.time),n.push(+(s.morphTarget===i))}s.push(new hv(".morphTargetInfluence["+i+"]",e,n))}o=e.length*n}else{let n=".bones["+e[t].name+"]";i(h_,n+".position",r,"pos",s),i(hM,n+".quaternion",r,"rot",s),i(h_,n+".scale",r,"scl",s)}}return 0===s.length?null:new this(r,o,s,a)}resetDuration(){let t=this.tracks,e=0;for(let i=0,s=t.length;i!==s;++i){let t=this.tracks[i];e=Math.max(e,t.times[t.times.length-1])}return this.duration=e,this}trim(){for(let t=0;t<this.tracks.length;t++)this.tracks[t].trim(0,this.duration);return this}validate(){let t=!0;for(let e=0;e<this.tracks.length;e++)t=t&&this.tracks[e].validate();return t}optimize(){for(let t=0;t<this.tracks.length;t++)this.tracks[t].optimize();return this}clone(){let t=[];for(let e=0;e<this.tracks.length;e++)t.push(this.tracks[e].clone());return new this.constructor(this.name,this.duration,t,this.blendMode)}toJSON(){return this.constructor.toJSON(this)}}let hT={enabled:!1,files:{},add:function(t,e){!1!==this.enabled&&(this.files[t]=e)},get:function(t){if(!1!==this.enabled)return this.files[t]},remove:function(t){delete this.files[t]},clear:function(){this.files={}}};class hz{constructor(t,e,i){let s,r=this,n=!1,a=0,o=0,h=[];this.onStart=void 0,this.onLoad=t,this.onProgress=e,this.onError=i,this.itemStart=function(t){o++,!1===n&&void 0!==r.onStart&&r.onStart(t,a,o),n=!0},this.itemEnd=function(t){a++,void 0!==r.onProgress&&r.onProgress(t,a,o),a===o&&(n=!1,void 0!==r.onLoad&&r.onLoad())},this.itemError=function(t){void 0!==r.onError&&r.onError(t)},this.resolveURL=function(t){return s?s(t):t},this.setURLModifier=function(t){return s=t,this},this.addHandler=function(t,e){return h.push(t,e),this},this.removeHandler=function(t){let e=h.indexOf(t);return -1!==e&&h.splice(e,2),this},this.getHandler=function(t){for(let e=0,i=h.length;e<i;e+=2){let i=h[e],s=h[e+1];if(i.global&&(i.lastIndex=0),i.test(t))return s}return null}}}let hI=new hz;class hC{constructor(t){this.manager=void 0!==t?t:hI,this.crossOrigin="anonymous",this.withCredentials=!1,this.path="",this.resourcePath="",this.requestHeader={}}load(){}loadAsync(t,e){let i=this;return new Promise(function(s,r){i.load(t,s,e,r)})}parse(){}setCrossOrigin(t){return this.crossOrigin=t,this}setWithCredentials(t){return this.withCredentials=t,this}setPath(t){return this.path=t,this}setResourcePath(t){return this.resourcePath=t,this}setRequestHeader(t){return this.requestHeader=t,this}}hC.DEFAULT_MATERIAL_NAME="__DEFAULT";let hk={};class hB extends Error{constructor(t,e){super(t),this.response=e}}class hR extends hC{constructor(t){super(t),this.mimeType="",this.responseType=""}load(t,e,i,s){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);let r=hT.get(t);if(void 0!==r)return this.manager.itemStart(t),setTimeout(()=>{e&&e(r),this.manager.itemEnd(t)},0),r;if(void 0!==hk[t])return void hk[t].push({onLoad:e,onProgress:i,onError:s});hk[t]=[],hk[t].push({onLoad:e,onProgress:i,onError:s});let n=new Request(t,{headers:new Headers(this.requestHeader),credentials:this.withCredentials?"include":"same-origin"}),a=this.mimeType,o=this.responseType;fetch(n).then(e=>{if(200===e.status||0===e.status){if(0===e.status&&console.warn("THREE.FileLoader: HTTP Status 0 received."),"undefined"==typeof ReadableStream||void 0===e.body||void 0===e.body.getReader)return e;let i=hk[t],s=e.body.getReader(),r=e.headers.get("X-File-Size")||e.headers.get("Content-Length"),n=r?parseInt(r):0,a=0!==n,o=0;return new Response(new ReadableStream({start(t){!function e(){s.read().then(({done:s,value:r})=>{if(s)t.close();else{let s=new ProgressEvent("progress",{lengthComputable:a,loaded:o+=r.byteLength,total:n});for(let t=0,e=i.length;t<e;t++){let e=i[t];e.onProgress&&e.onProgress(s)}t.enqueue(r),e()}},e=>{t.error(e)})}()}}))}throw new hB(`fetch for "${e.url}" responded with ${e.status}: ${e.statusText}`,e)}).then(t=>{switch(o){case"arraybuffer":return t.arrayBuffer();case"blob":return t.blob();case"document":return t.text().then(t=>new DOMParser().parseFromString(t,a));case"json":return t.json();default:if(""===a)return t.text();{let e=/charset="?([^;"\s]*)"?/i.exec(a),i=new TextDecoder(e&&e[1]?e[1].toLowerCase():void 0);return t.arrayBuffer().then(t=>i.decode(t))}}}).then(e=>{hT.add(t,e);let i=hk[t];delete hk[t];for(let t=0,s=i.length;t<s;t++){let s=i[t];s.onLoad&&s.onLoad(e)}}).catch(e=>{let i=hk[t];if(void 0===i)throw this.manager.itemError(t),e;delete hk[t];for(let t=0,s=i.length;t<s;t++){let s=i[t];s.onError&&s.onError(e)}this.manager.itemError(t)}).finally(()=>{this.manager.itemEnd(t)}),this.manager.itemStart(t)}setResponseType(t){return this.responseType=t,this}setMimeType(t){return this.mimeType=t,this}}class hE extends hC{constructor(t){super(t)}load(t,e,i,s){let r=this,n=new hR(this.manager);n.setPath(this.path),n.setRequestHeader(this.requestHeader),n.setWithCredentials(this.withCredentials),n.load(t,function(i){try{e(r.parse(JSON.parse(i)))}catch(e){s?s(e):console.error(e),r.manager.itemError(t)}},i,s)}parse(t){let e=[];for(let i=0;i<t.length;i++){let s=hA.parse(t[i]);e.push(s)}return e}}class hP extends hC{constructor(t){super(t)}load(t,e,i,s){let r=this,n=[],a=new aZ,o=new hR(this.manager);o.setPath(this.path),o.setResponseType("arraybuffer"),o.setRequestHeader(this.requestHeader),o.setWithCredentials(r.withCredentials);let h=0;if(Array.isArray(t))for(let l=0,u=t.length;l<u;++l)!function(l){o.load(t[l],function(t){let i=r.parse(t,!0);n[l]={width:i.width,height:i.height,format:i.format,mipmaps:i.mipmaps},6===(h+=1)&&(1===i.mipmapCount&&(a.minFilter=tC),a.image=n,a.format=i.format,a.needsUpdate=!0,e&&e(a))},i,s)}(l);else o.load(t,function(t){let i=r.parse(t,!0);if(i.isCubemap){let t=i.mipmaps.length/i.mipmapCount;for(let e=0;e<t;e++){n[e]={mipmaps:[]};for(let t=0;t<i.mipmapCount;t++)n[e].mipmaps.push(i.mipmaps[e*i.mipmapCount+t]),n[e].format=i.format,n[e].width=i.width,n[e].height=i.height}a.image=n}else a.image.width=i.width,a.image.height=i.height,a.mipmaps=i.mipmaps;1===i.mipmapCount&&(a.minFilter=tC),a.format=i.format,a.needsUpdate=!0,e&&e(a)},i,s);return a}}class hO extends hC{constructor(t){super(t)}load(t,e,i,s){void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);let r=this,n=hT.get(t);if(void 0!==n)return r.manager.itemStart(t),setTimeout(function(){e&&e(n),r.manager.itemEnd(t)},0),n;let a=iJ("img");function o(){l(),hT.add(t,this),e&&e(this),r.manager.itemEnd(t)}function h(e){l(),s&&s(e),r.manager.itemError(t),r.manager.itemEnd(t)}function l(){a.removeEventListener("load",o,!1),a.removeEventListener("error",h,!1)}return a.addEventListener("load",o,!1),a.addEventListener("error",h,!1),"data:"!==t.slice(0,5)&&void 0!==this.crossOrigin&&(a.crossOrigin=this.crossOrigin),r.manager.itemStart(t),a.src=t,a}}class hN extends hC{constructor(t){super(t)}load(t,e,i,s){let r=new nc;r.colorSpace=eZ;let n=new hO(this.manager);n.setCrossOrigin(this.crossOrigin),n.setPath(this.path);let a=0;for(let i=0;i<t.length;++i)!function(i){n.load(t[i],function(t){r.images[i]=t,6==++a&&(r.needsUpdate=!0,e&&e(r))},void 0,s)}(i);return r}}class hF extends hC{constructor(t){super(t)}load(t,e,i,s){let r=this,n=new n$,a=new hR(this.manager);return a.setResponseType("arraybuffer"),a.setRequestHeader(this.requestHeader),a.setPath(this.path),a.setWithCredentials(r.withCredentials),a.load(t,function(t){let i;try{i=r.parse(t)}catch(t){if(void 0===s)return void console.error(t);s(t)}void 0!==i.image?n.image=i.image:void 0!==i.data&&(n.image.width=i.width,n.image.height=i.height,n.image.data=i.data),n.wrapS=void 0!==i.wrapS?i.wrapS:tM,n.wrapT=void 0!==i.wrapT?i.wrapT:tM,n.magFilter=void 0!==i.magFilter?i.magFilter:tC,n.minFilter=void 0!==i.minFilter?i.minFilter:tC,n.anisotropy=void 0!==i.anisotropy?i.anisotropy:1,void 0!==i.colorSpace&&(n.colorSpace=i.colorSpace),void 0!==i.flipY&&(n.flipY=i.flipY),void 0!==i.format&&(n.format=i.format),void 0!==i.type&&(n.type=i.type),void 0!==i.mipmaps&&(n.mipmaps=i.mipmaps,n.minFilter=tR),1===i.mipmapCount&&(n.minFilter=tC),void 0!==i.generateMipmaps&&(n.generateMipmaps=i.generateMipmaps),n.needsUpdate=!0,e&&e(n,i)},i,s),n}}class hV extends hC{constructor(t){super(t)}load(t,e,i,s){let r=new i7,n=new hO(this.manager);return n.setCrossOrigin(this.crossOrigin),n.setPath(this.path),n.load(t,function(t){r.image=t,r.needsUpdate=!0,void 0!==e&&e(r)},i,s),r}}class hL extends re{constructor(t,e=1){super(),this.isLight=!0,this.type="Light",this.color=new rv(t),this.intensity=e}dispose(){}copy(t,e){return super.copy(t,e),this.color.copy(t.color),this.intensity=t.intensity,this}toJSON(t){let e=super.toJSON(t);return e.object.color=this.color.getHex(),e.object.intensity=this.intensity,void 0!==this.groundColor&&(e.object.groundColor=this.groundColor.getHex()),void 0!==this.distance&&(e.object.distance=this.distance),void 0!==this.angle&&(e.object.angle=this.angle),void 0!==this.decay&&(e.object.decay=this.decay),void 0!==this.penumbra&&(e.object.penumbra=this.penumbra),void 0!==this.shadow&&(e.object.shadow=this.shadow.toJSON()),void 0!==this.target&&(e.object.target=this.target.uuid),e}}class hj extends hL{constructor(t,e,i){super(t,i),this.isHemisphereLight=!0,this.type="HemisphereLight",this.position.copy(re.DEFAULT_UP),this.updateMatrix(),this.groundColor=new rv(e)}copy(t,e){return super.copy(t,e),this.groundColor.copy(t.groundColor),this}}let hU=new sV,hW=new sh,hD=new sh;class hH{constructor(t){this.camera=t,this.intensity=1,this.bias=0,this.normalBias=0,this.radius=1,this.blurSamples=8,this.mapSize=new ij(512,512),this.map=null,this.mapPass=null,this.matrix=new sV,this.autoUpdate=!0,this.needsUpdate=!1,this._frustum=new aa,this._frameExtents=new ij(1,1),this._viewportCount=1,this._viewports=[new st(0,0,1,1)]}getViewportCount(){return this._viewportCount}getFrustum(){return this._frustum}updateMatrices(t){let e=this.camera,i=this.matrix;hW.setFromMatrixPosition(t.matrixWorld),e.position.copy(hW),hD.setFromMatrixPosition(t.target.matrixWorld),e.lookAt(hD),e.updateMatrixWorld(),hU.multiplyMatrices(e.projectionMatrix,e.matrixWorldInverse),this._frustum.setFromProjectionMatrix(hU),i.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),i.multiply(hU)}getViewport(t){return this._viewports[t]}getFrameExtents(){return this._frameExtents}dispose(){this.map&&this.map.dispose(),this.mapPass&&this.mapPass.dispose()}copy(t){return this.camera=t.camera.clone(),this.intensity=t.intensity,this.bias=t.bias,this.radius=t.radius,this.mapSize.copy(t.mapSize),this}clone(){return new this.constructor().copy(this)}toJSON(){let t={};return 1!==this.intensity&&(t.intensity=this.intensity),0!==this.bias&&(t.bias=this.bias),0!==this.normalBias&&(t.normalBias=this.normalBias),1!==this.radius&&(t.radius=this.radius),(512!==this.mapSize.x||512!==this.mapSize.y)&&(t.mapSize=this.mapSize.toArray()),t.camera=this.camera.toJSON(!1).object,delete t.camera.matrix,t}}class hq extends hH{constructor(){super(new nl(50,1,.5,500)),this.isSpotLightShadow=!0,this.focus=1}updateMatrices(t){let e=this.camera,i=2*iR*t.angle*this.focus,s=this.mapSize.width/this.mapSize.height,r=t.distance||e.far;(i!==e.fov||s!==e.aspect||r!==e.far)&&(e.fov=i,e.aspect=s,e.far=r,e.updateProjectionMatrix()),super.updateMatrices(t)}copy(t){return super.copy(t),this.focus=t.focus,this}}class hJ extends hL{constructor(t,e,i=0,s=Math.PI/3,r=0,n=2){super(t,e),this.isSpotLight=!0,this.type="SpotLight",this.position.copy(re.DEFAULT_UP),this.updateMatrix(),this.target=new re,this.distance=i,this.angle=s,this.penumbra=r,this.decay=n,this.map=null,this.shadow=new hq}get power(){return this.intensity*Math.PI}set power(t){this.intensity=t/Math.PI}dispose(){this.shadow.dispose()}copy(t,e){return super.copy(t,e),this.distance=t.distance,this.angle=t.angle,this.penumbra=t.penumbra,this.decay=t.decay,this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}}let hX=new sV,hZ=new sh,hY=new sh;class hG extends hH{constructor(){super(new nl(90,1,.5,500)),this.isPointLightShadow=!0,this._frameExtents=new ij(4,2),this._viewportCount=6,this._viewports=[new st(2,1,1,1),new st(0,1,1,1),new st(3,1,1,1),new st(1,1,1,1),new st(3,0,1,1),new st(1,0,1,1)],this._cubeDirections=[new sh(1,0,0),new sh(-1,0,0),new sh(0,0,1),new sh(0,0,-1),new sh(0,1,0),new sh(0,-1,0)],this._cubeUps=[new sh(0,1,0),new sh(0,1,0),new sh(0,1,0),new sh(0,1,0),new sh(0,0,1),new sh(0,0,-1)]}updateMatrices(t,e=0){let i=this.camera,s=this.matrix,r=t.distance||i.far;r!==i.far&&(i.far=r,i.updateProjectionMatrix()),hZ.setFromMatrixPosition(t.matrixWorld),i.position.copy(hZ),hY.copy(i.position),hY.add(this._cubeDirections[e]),i.up.copy(this._cubeUps[e]),i.lookAt(hY),i.updateMatrixWorld(),s.makeTranslation(-hZ.x,-hZ.y,-hZ.z),hX.multiplyMatrices(i.projectionMatrix,i.matrixWorldInverse),this._frustum.setFromProjectionMatrix(hX)}}class h$ extends hL{constructor(t,e,i=0,s=2){super(t,e),this.isPointLight=!0,this.type="PointLight",this.distance=i,this.decay=s,this.shadow=new hG}get power(){return 4*this.intensity*Math.PI}set power(t){this.intensity=t/(4*Math.PI)}dispose(){this.shadow.dispose()}copy(t,e){return super.copy(t,e),this.distance=t.distance,this.decay=t.decay,this.shadow=t.shadow.clone(),this}}class hQ extends nn{constructor(t=-1,e=1,i=1,s=-1,r=.1,n=2e3){super(),this.isOrthographicCamera=!0,this.type="OrthographicCamera",this.zoom=1,this.view=null,this.left=t,this.right=e,this.top=i,this.bottom=s,this.near=r,this.far=n,this.updateProjectionMatrix()}copy(t,e){return super.copy(t,e),this.left=t.left,this.right=t.right,this.top=t.top,this.bottom=t.bottom,this.near=t.near,this.far=t.far,this.zoom=t.zoom,this.view=null===t.view?null:Object.assign({},t.view),this}setViewOffset(t,e,i,s,r,n){null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=s,this.view.width=r,this.view.height=n,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){let t=(this.right-this.left)/(2*this.zoom),e=(this.top-this.bottom)/(2*this.zoom),i=(this.right+this.left)/2,s=(this.top+this.bottom)/2,r=i-t,n=i+t,a=s+e,o=s-e;if(null!==this.view&&this.view.enabled){let t=(this.right-this.left)/this.view.fullWidth/this.zoom,e=(this.top-this.bottom)/this.view.fullHeight/this.zoom;r+=t*this.view.offsetX,n=r+t*this.view.width,a-=e*this.view.offsetY,o=a-e*this.view.height}this.projectionMatrix.makeOrthographic(r,n,a,o,this.near,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(t){let e=super.toJSON(t);return e.object.zoom=this.zoom,e.object.left=this.left,e.object.right=this.right,e.object.top=this.top,e.object.bottom=this.bottom,e.object.near=this.near,e.object.far=this.far,null!==this.view&&(e.object.view=Object.assign({},this.view)),e}}class hK extends hH{constructor(){super(new hQ(-5,5,5,-5,.5,500)),this.isDirectionalLightShadow=!0}}class h0 extends hL{constructor(t,e){super(t,e),this.isDirectionalLight=!0,this.type="DirectionalLight",this.position.copy(re.DEFAULT_UP),this.updateMatrix(),this.target=new re,this.shadow=new hK}dispose(){this.shadow.dispose()}copy(t){return super.copy(t),this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}}class h1 extends hL{constructor(t,e){super(t,e),this.isAmbientLight=!0,this.type="AmbientLight"}}class h2 extends hL{constructor(t,e,i=10,s=10){super(t,e),this.isRectAreaLight=!0,this.type="RectAreaLight",this.width=i,this.height=s}get power(){return this.intensity*this.width*this.height*Math.PI}set power(t){this.intensity=t/(this.width*this.height*Math.PI)}copy(t){return super.copy(t),this.width=t.width,this.height=t.height,this}toJSON(t){let e=super.toJSON(t);return e.object.width=this.width,e.object.height=this.height,e}}class h3{constructor(){this.isSphericalHarmonics3=!0,this.coefficients=[];for(let t=0;t<9;t++)this.coefficients.push(new sh)}set(t){for(let e=0;e<9;e++)this.coefficients[e].copy(t[e]);return this}zero(){for(let t=0;t<9;t++)this.coefficients[t].set(0,0,0);return this}getAt(t,e){let i=t.x,s=t.y,r=t.z,n=this.coefficients;return e.copy(n[0]).multiplyScalar(.282095),e.addScaledVector(n[1],.488603*s),e.addScaledVector(n[2],.488603*r),e.addScaledVector(n[3],.488603*i),e.addScaledVector(n[4],i*s*1.092548),e.addScaledVector(n[5],s*r*1.092548),e.addScaledVector(n[6],.315392*(3*r*r-1)),e.addScaledVector(n[7],i*r*1.092548),e.addScaledVector(n[8],.546274*(i*i-s*s)),e}getIrradianceAt(t,e){let i=t.x,s=t.y,r=t.z,n=this.coefficients;return e.copy(n[0]).multiplyScalar(.886227),e.addScaledVector(n[1],1.023328*s),e.addScaledVector(n[2],1.023328*r),e.addScaledVector(n[3],1.023328*i),e.addScaledVector(n[4],.858086*i*s),e.addScaledVector(n[5],.858086*s*r),e.addScaledVector(n[6],.743125*r*r-.247708),e.addScaledVector(n[7],.858086*i*r),e.addScaledVector(n[8],.429043*(i*i-s*s)),e}add(t){for(let e=0;e<9;e++)this.coefficients[e].add(t.coefficients[e]);return this}addScaledSH(t,e){for(let i=0;i<9;i++)this.coefficients[i].addScaledVector(t.coefficients[i],e);return this}scale(t){for(let e=0;e<9;e++)this.coefficients[e].multiplyScalar(t);return this}lerp(t,e){for(let i=0;i<9;i++)this.coefficients[i].lerp(t.coefficients[i],e);return this}equals(t){for(let e=0;e<9;e++)if(!this.coefficients[e].equals(t.coefficients[e]))return!1;return!0}copy(t){return this.set(t.coefficients)}clone(){return new this.constructor().copy(this)}fromArray(t,e=0){let i=this.coefficients;for(let s=0;s<9;s++)i[s].fromArray(t,e+3*s);return this}toArray(t=[],e=0){let i=this.coefficients;for(let s=0;s<9;s++)i[s].toArray(t,e+3*s);return t}static getBasisAt(t,e){let i=t.x,s=t.y,r=t.z;e[0]=.282095,e[1]=.488603*s,e[2]=.488603*r,e[3]=.488603*i,e[4]=1.092548*i*s,e[5]=1.092548*s*r,e[6]=.315392*(3*r*r-1),e[7]=1.092548*i*r,e[8]=.546274*(i*i-s*s)}}class h5 extends hL{constructor(t=new h3,e=1){super(void 0,e),this.isLightProbe=!0,this.sh=t}copy(t){return super.copy(t),this.sh.copy(t.sh),this}fromJSON(t){return this.intensity=t.intensity,this.sh.fromArray(t.sh),this}toJSON(t){let e=super.toJSON(t);return e.object.sh=this.sh.toArray(),e}}class h4 extends hC{constructor(t){super(t),this.textures={}}load(t,e,i,s){let r=this,n=new hR(r.manager);n.setPath(r.path),n.setRequestHeader(r.requestHeader),n.setWithCredentials(r.withCredentials),n.load(t,function(i){try{e(r.parse(JSON.parse(i)))}catch(e){s?s(e):console.error(e),r.manager.itemError(t)}},i,s)}parse(t){let e=this.textures;function i(t){return void 0===e[t]&&console.warn("THREE.MaterialLoader: Undefined texture",t),e[t]}let s=this.createMaterialFromType(t.type);if(void 0!==t.uuid&&(s.uuid=t.uuid),void 0!==t.name&&(s.name=t.name),void 0!==t.color&&void 0!==s.color&&s.color.setHex(t.color),void 0!==t.roughness&&(s.roughness=t.roughness),void 0!==t.metalness&&(s.metalness=t.metalness),void 0!==t.sheen&&(s.sheen=t.sheen),void 0!==t.sheenColor&&(s.sheenColor=new rv().setHex(t.sheenColor)),void 0!==t.sheenRoughness&&(s.sheenRoughness=t.sheenRoughness),void 0!==t.emissive&&void 0!==s.emissive&&s.emissive.setHex(t.emissive),void 0!==t.specular&&void 0!==s.specular&&s.specular.setHex(t.specular),void 0!==t.specularIntensity&&(s.specularIntensity=t.specularIntensity),void 0!==t.specularColor&&void 0!==s.specularColor&&s.specularColor.setHex(t.specularColor),void 0!==t.shininess&&(s.shininess=t.shininess),void 0!==t.clearcoat&&(s.clearcoat=t.clearcoat),void 0!==t.clearcoatRoughness&&(s.clearcoatRoughness=t.clearcoatRoughness),void 0!==t.dispersion&&(s.dispersion=t.dispersion),void 0!==t.iridescence&&(s.iridescence=t.iridescence),void 0!==t.iridescenceIOR&&(s.iridescenceIOR=t.iridescenceIOR),void 0!==t.iridescenceThicknessRange&&(s.iridescenceThicknessRange=t.iridescenceThicknessRange),void 0!==t.transmission&&(s.transmission=t.transmission),void 0!==t.thickness&&(s.thickness=t.thickness),void 0!==t.attenuationDistance&&(s.attenuationDistance=t.attenuationDistance),void 0!==t.attenuationColor&&void 0!==s.attenuationColor&&s.attenuationColor.setHex(t.attenuationColor),void 0!==t.anisotropy&&(s.anisotropy=t.anisotropy),void 0!==t.anisotropyRotation&&(s.anisotropyRotation=t.anisotropyRotation),void 0!==t.fog&&(s.fog=t.fog),void 0!==t.flatShading&&(s.flatShading=t.flatShading),void 0!==t.blending&&(s.blending=t.blending),void 0!==t.combine&&(s.combine=t.combine),void 0!==t.side&&(s.side=t.side),void 0!==t.shadowSide&&(s.shadowSide=t.shadowSide),void 0!==t.opacity&&(s.opacity=t.opacity),void 0!==t.transparent&&(s.transparent=t.transparent),void 0!==t.alphaTest&&(s.alphaTest=t.alphaTest),void 0!==t.alphaHash&&(s.alphaHash=t.alphaHash),void 0!==t.depthFunc&&(s.depthFunc=t.depthFunc),void 0!==t.depthTest&&(s.depthTest=t.depthTest),void 0!==t.depthWrite&&(s.depthWrite=t.depthWrite),void 0!==t.colorWrite&&(s.colorWrite=t.colorWrite),void 0!==t.blendSrc&&(s.blendSrc=t.blendSrc),void 0!==t.blendDst&&(s.blendDst=t.blendDst),void 0!==t.blendEquation&&(s.blendEquation=t.blendEquation),void 0!==t.blendSrcAlpha&&(s.blendSrcAlpha=t.blendSrcAlpha),void 0!==t.blendDstAlpha&&(s.blendDstAlpha=t.blendDstAlpha),void 0!==t.blendEquationAlpha&&(s.blendEquationAlpha=t.blendEquationAlpha),void 0!==t.blendColor&&void 0!==s.blendColor&&s.blendColor.setHex(t.blendColor),void 0!==t.blendAlpha&&(s.blendAlpha=t.blendAlpha),void 0!==t.stencilWriteMask&&(s.stencilWriteMask=t.stencilWriteMask),void 0!==t.stencilFunc&&(s.stencilFunc=t.stencilFunc),void 0!==t.stencilRef&&(s.stencilRef=t.stencilRef),void 0!==t.stencilFuncMask&&(s.stencilFuncMask=t.stencilFuncMask),void 0!==t.stencilFail&&(s.stencilFail=t.stencilFail),void 0!==t.stencilZFail&&(s.stencilZFail=t.stencilZFail),void 0!==t.stencilZPass&&(s.stencilZPass=t.stencilZPass),void 0!==t.stencilWrite&&(s.stencilWrite=t.stencilWrite),void 0!==t.wireframe&&(s.wireframe=t.wireframe),void 0!==t.wireframeLinewidth&&(s.wireframeLinewidth=t.wireframeLinewidth),void 0!==t.wireframeLinecap&&(s.wireframeLinecap=t.wireframeLinecap),void 0!==t.wireframeLinejoin&&(s.wireframeLinejoin=t.wireframeLinejoin),void 0!==t.rotation&&(s.rotation=t.rotation),void 0!==t.linewidth&&(s.linewidth=t.linewidth),void 0!==t.dashSize&&(s.dashSize=t.dashSize),void 0!==t.gapSize&&(s.gapSize=t.gapSize),void 0!==t.scale&&(s.scale=t.scale),void 0!==t.polygonOffset&&(s.polygonOffset=t.polygonOffset),void 0!==t.polygonOffsetFactor&&(s.polygonOffsetFactor=t.polygonOffsetFactor),void 0!==t.polygonOffsetUnits&&(s.polygonOffsetUnits=t.polygonOffsetUnits),void 0!==t.dithering&&(s.dithering=t.dithering),void 0!==t.alphaToCoverage&&(s.alphaToCoverage=t.alphaToCoverage),void 0!==t.premultipliedAlpha&&(s.premultipliedAlpha=t.premultipliedAlpha),void 0!==t.forceSinglePass&&(s.forceSinglePass=t.forceSinglePass),void 0!==t.visible&&(s.visible=t.visible),void 0!==t.toneMapped&&(s.toneMapped=t.toneMapped),void 0!==t.userData&&(s.userData=t.userData),void 0!==t.vertexColors&&("number"==typeof t.vertexColors?s.vertexColors=t.vertexColors>0:s.vertexColors=t.vertexColors),void 0!==t.uniforms)for(let e in t.uniforms){let r=t.uniforms[e];switch(s.uniforms[e]={},r.type){case"t":s.uniforms[e].value=i(r.value);break;case"c":s.uniforms[e].value=new rv().setHex(r.value);break;case"v2":s.uniforms[e].value=new ij().fromArray(r.value);break;case"v3":s.uniforms[e].value=new sh().fromArray(r.value);break;case"v4":s.uniforms[e].value=new st().fromArray(r.value);break;case"m3":s.uniforms[e].value=new iU().fromArray(r.value);break;case"m4":s.uniforms[e].value=new sV().fromArray(r.value);break;default:s.uniforms[e].value=r.value}}if(void 0!==t.defines&&(s.defines=t.defines),void 0!==t.vertexShader&&(s.vertexShader=t.vertexShader),void 0!==t.fragmentShader&&(s.fragmentShader=t.fragmentShader),void 0!==t.glslVersion&&(s.glslVersion=t.glslVersion),void 0!==t.extensions)for(let e in t.extensions)s.extensions[e]=t.extensions[e];if(void 0!==t.lights&&(s.lights=t.lights),void 0!==t.clipping&&(s.clipping=t.clipping),void 0!==t.size&&(s.size=t.size),void 0!==t.sizeAttenuation&&(s.sizeAttenuation=t.sizeAttenuation),void 0!==t.map&&(s.map=i(t.map)),void 0!==t.matcap&&(s.matcap=i(t.matcap)),void 0!==t.alphaMap&&(s.alphaMap=i(t.alphaMap)),void 0!==t.bumpMap&&(s.bumpMap=i(t.bumpMap)),void 0!==t.bumpScale&&(s.bumpScale=t.bumpScale),void 0!==t.normalMap&&(s.normalMap=i(t.normalMap)),void 0!==t.normalMapType&&(s.normalMapType=t.normalMapType),void 0!==t.normalScale){let e=t.normalScale;!1===Array.isArray(e)&&(e=[e,e]),s.normalScale=new ij().fromArray(e)}return void 0!==t.displacementMap&&(s.displacementMap=i(t.displacementMap)),void 0!==t.displacementScale&&(s.displacementScale=t.displacementScale),void 0!==t.displacementBias&&(s.displacementBias=t.displacementBias),void 0!==t.roughnessMap&&(s.roughnessMap=i(t.roughnessMap)),void 0!==t.metalnessMap&&(s.metalnessMap=i(t.metalnessMap)),void 0!==t.emissiveMap&&(s.emissiveMap=i(t.emissiveMap)),void 0!==t.emissiveIntensity&&(s.emissiveIntensity=t.emissiveIntensity),void 0!==t.specularMap&&(s.specularMap=i(t.specularMap)),void 0!==t.specularIntensityMap&&(s.specularIntensityMap=i(t.specularIntensityMap)),void 0!==t.specularColorMap&&(s.specularColorMap=i(t.specularColorMap)),void 0!==t.envMap&&(s.envMap=i(t.envMap)),void 0!==t.envMapRotation&&s.envMapRotation.fromArray(t.envMapRotation),void 0!==t.envMapIntensity&&(s.envMapIntensity=t.envMapIntensity),void 0!==t.reflectivity&&(s.reflectivity=t.reflectivity),void 0!==t.refractionRatio&&(s.refractionRatio=t.refractionRatio),void 0!==t.lightMap&&(s.lightMap=i(t.lightMap)),void 0!==t.lightMapIntensity&&(s.lightMapIntensity=t.lightMapIntensity),void 0!==t.aoMap&&(s.aoMap=i(t.aoMap)),void 0!==t.aoMapIntensity&&(s.aoMapIntensity=t.aoMapIntensity),void 0!==t.gradientMap&&(s.gradientMap=i(t.gradientMap)),void 0!==t.clearcoatMap&&(s.clearcoatMap=i(t.clearcoatMap)),void 0!==t.clearcoatRoughnessMap&&(s.clearcoatRoughnessMap=i(t.clearcoatRoughnessMap)),void 0!==t.clearcoatNormalMap&&(s.clearcoatNormalMap=i(t.clearcoatNormalMap)),void 0!==t.clearcoatNormalScale&&(s.clearcoatNormalScale=new ij().fromArray(t.clearcoatNormalScale)),void 0!==t.iridescenceMap&&(s.iridescenceMap=i(t.iridescenceMap)),void 0!==t.iridescenceThicknessMap&&(s.iridescenceThicknessMap=i(t.iridescenceThicknessMap)),void 0!==t.transmissionMap&&(s.transmissionMap=i(t.transmissionMap)),void 0!==t.thicknessMap&&(s.thicknessMap=i(t.thicknessMap)),void 0!==t.anisotropyMap&&(s.anisotropyMap=i(t.anisotropyMap)),void 0!==t.sheenColorMap&&(s.sheenColorMap=i(t.sheenColorMap)),void 0!==t.sheenRoughnessMap&&(s.sheenRoughnessMap=i(t.sheenRoughnessMap)),s}setTextures(t){return this.textures=t,this}createMaterialFromType(t){return h4.createMaterialFromType(t)}static createMaterialFromType(t){return new({ShadowMaterial:o4,SpriteMaterial:nM,RawShaderMaterial:o6,ShaderMaterial:nr,PointsMaterial:aV,MeshPhysicalMaterial:o9,MeshStandardMaterial:o8,MeshPhongMaterial:o7,MeshToonMaterial:ht,MeshNormalMaterial:he,MeshLambertMaterial:hi,MeshDepthMaterial:hs,MeshDistanceMaterial:hr,MeshBasicMaterial:r_,MeshMatcapMaterial:hn,LineDashedMaterial:ha,LineBasicMaterial:a_,Material:rS})[t]}}class h6{static extractUrlBase(t){let e=t.lastIndexOf("/");return -1===e?"./":t.slice(0,e+1)}static resolveURL(t,e){return"string"!=typeof t||""===t?"":(/^https?:\/\//i.test(e)&&/^\//.test(t)&&(e=e.replace(/(^https?:\/\/[^\/]+).*/i,"$1")),/^(https?:)?\/\//i.test(t)||/^data:.*,.*$/i.test(t)||/^blob:.*$/i.test(t))?t:e+t}}class h8 extends rY{constructor(){super(),this.isInstancedBufferGeometry=!0,this.type="InstancedBufferGeometry",this.instanceCount=1/0}copy(t){return super.copy(t),this.instanceCount=t.instanceCount,this}toJSON(){let t=super.toJSON();return t.instanceCount=this.instanceCount,t.isInstancedBufferGeometry=!0,t}}class h9 extends hC{constructor(t){super(t)}load(t,e,i,s){let r=this,n=new hR(r.manager);n.setPath(r.path),n.setRequestHeader(r.requestHeader),n.setWithCredentials(r.withCredentials),n.load(t,function(i){try{e(r.parse(JSON.parse(i)))}catch(e){s?s(e):console.error(e),r.manager.itemError(t)}},i,s)}parse(t){let e={},i={};function s(t,s){if(void 0!==e[s])return e[s];let r=t.interleavedBuffers[s],n=function(t,e){if(void 0!==i[e])return i[e];let s=new Uint32Array(t.arrayBuffers[e]).buffer;return i[e]=s,s}(t,r.buffer),a=new nb(iq(r.type,n),r.stride);return a.uuid=r.uuid,e[s]=a,a}let r=t.isInstancedBufferGeometry?new h8:new rY,n=t.data.index;if(void 0!==n){let t=iq(n.type,n.array);r.setIndex(new rR(t,1))}let a=t.data.attributes;for(let e in a){let i,n=a[e];if(n.isInterleavedBufferAttribute)i=new nw(s(t.data,n.data),n.itemSize,n.offset,n.normalized);else{let t=iq(n.type,n.array);i=new(n.isInstancedBufferAttribute?n1:rR)(t,n.itemSize,n.normalized)}void 0!==n.name&&(i.name=n.name),void 0!==n.usage&&i.setUsage(n.usage),r.setAttribute(e,i)}let o=t.data.morphAttributes;if(o)for(let e in o){let i=o[e],n=[];for(let e=0,r=i.length;e<r;e++){let r,a=i[e];r=a.isInterleavedBufferAttribute?new nw(s(t.data,a.data),a.itemSize,a.offset,a.normalized):new rR(iq(a.type,a.array),a.itemSize,a.normalized),void 0!==a.name&&(r.name=a.name),n.push(r)}r.morphAttributes[e]=n}t.data.morphTargetsRelative&&(r.morphTargetsRelative=!0);let h=t.data.groups||t.data.drawcalls||t.data.offsets;if(void 0!==h)for(let t=0,e=h.length;t!==e;++t){let e=h[t];r.addGroup(e.start,e.count,e.materialIndex)}let l=t.data.boundingSphere;if(void 0!==l){let t=new sh;void 0!==l.center&&t.fromArray(l.center),r.boundingSphere=new sC(t,l.radius)}return t.name&&(r.name=t.name),t.userData&&(r.userData=t.userData),r}}class h7 extends hC{constructor(t){super(t)}load(t,e,i,s){let r=this,n=""===this.path?h6.extractUrlBase(t):this.path;this.resourcePath=this.resourcePath||n;let a=new hR(this.manager);a.setPath(this.path),a.setRequestHeader(this.requestHeader),a.setWithCredentials(this.withCredentials),a.load(t,function(i){let n=null;try{n=JSON.parse(i)}catch(e){void 0!==s&&s(e),console.error("THREE:ObjectLoader: Can't parse "+t+".",e.message);return}let a=n.metadata;if(void 0===a||void 0===a.type||"geometry"===a.type.toLowerCase()){void 0!==s&&s(Error("THREE.ObjectLoader: Can't load "+t)),console.error("THREE.ObjectLoader: Can't load "+t);return}r.parse(n,e)},i,s)}async loadAsync(t,e){let i=""===this.path?h6.extractUrlBase(t):this.path;this.resourcePath=this.resourcePath||i;let s=new hR(this.manager);s.setPath(this.path),s.setRequestHeader(this.requestHeader),s.setWithCredentials(this.withCredentials);let r=JSON.parse(await s.loadAsync(t,e)),n=r.metadata;if(void 0===n||void 0===n.type||"geometry"===n.type.toLowerCase())throw Error("THREE.ObjectLoader: Can't load "+t);return await this.parseAsync(r)}parse(t,e){let i=this.parseAnimations(t.animations),s=this.parseShapes(t.shapes),r=this.parseGeometries(t.geometries,s),n=this.parseImages(t.images,function(){void 0!==e&&e(h)}),a=this.parseTextures(t.textures,n),o=this.parseMaterials(t.materials,a),h=this.parseObject(t.object,r,o,a,i),l=this.parseSkeletons(t.skeletons,h);if(this.bindSkeletons(h,l),this.bindLightTargets(h),void 0!==e){let t=!1;for(let e in n)if(n[e].data instanceof HTMLImageElement){t=!0;break}!1===t&&e(h)}return h}async parseAsync(t){let e=this.parseAnimations(t.animations),i=this.parseShapes(t.shapes),s=this.parseGeometries(t.geometries,i),r=await this.parseImagesAsync(t.images),n=this.parseTextures(t.textures,r),a=this.parseMaterials(t.materials,n),o=this.parseObject(t.object,s,a,n,e),h=this.parseSkeletons(t.skeletons,o);return this.bindSkeletons(o,h),this.bindLightTargets(o),o}parseShapes(t){let e={};if(void 0!==t)for(let i=0,s=t.length;i<s;i++){let s=new oS().fromJSON(t[i]);e[s.uuid]=s}return e}parseSkeletons(t,e){let i={},s={};if(e.traverse(function(t){t.isBone&&(s[t.uuid]=t)}),void 0!==t)for(let e=0,r=t.length;e<r;e++){let r=new n0().fromJSON(t[e],s);i[r.uuid]=r}return i}parseGeometries(t,e){let i={};if(void 0!==t){let s=new h9;for(let r=0,n=t.length;r<n;r++){let n,a=t[r];switch(a.type){case"BufferGeometry":case"InstancedBufferGeometry":n=s.parse(a);break;default:a.type in o5?n=o5[a.type].fromJSON(a,e):console.warn(`THREE.ObjectLoader: Unsupported geometry type "${a.type}"`)}n.uuid=a.uuid,void 0!==a.name&&(n.name=a.name),void 0!==a.userData&&(n.userData=a.userData),i[a.uuid]=n}}return i}parseMaterials(t,e){let i={},s={};if(void 0!==t){let r=new h4;r.setTextures(e);for(let e=0,n=t.length;e<n;e++){let n=t[e];void 0===i[n.uuid]&&(i[n.uuid]=r.parse(n)),s[n.uuid]=i[n.uuid]}}return s}parseAnimations(t){let e={};if(void 0!==t)for(let i=0;i<t.length;i++){let s=t[i],r=hA.parse(s);e[r.uuid]=r}return e}parseImages(t,e){let i,s=this,r={};function n(t){if("string"==typeof t){var e;return e=/^(\/\/)|([a-z]+:(\/\/)?)/i.test(t)?t:s.resourcePath+t,s.manager.itemStart(e),i.load(e,function(){s.manager.itemEnd(e)},void 0,function(){s.manager.itemError(e),s.manager.itemEnd(e)})}return t.data?{data:iq(t.type,t.data),width:t.width,height:t.height}:null}if(void 0!==t&&t.length>0){(i=new hO(new hz(e))).setCrossOrigin(this.crossOrigin);for(let e=0,i=t.length;e<i;e++){let i=t[e],s=i.url;if(Array.isArray(s)){let t=[];for(let e=0,i=s.length;e<i;e++){let i=n(s[e]);null!==i&&(i instanceof HTMLImageElement?t.push(i):t.push(new n$(i.data,i.width,i.height)))}r[i.uuid]=new i6(t)}else{let t=n(i.url);r[i.uuid]=new i6(t)}}}return r}async parseImagesAsync(t){let e,i=this,s={};async function r(t){if("string"==typeof t){let s=/^(\/\/)|([a-z]+:(\/\/)?)/i.test(t)?t:i.resourcePath+t;return await e.loadAsync(s)}return t.data?{data:iq(t.type,t.data),width:t.width,height:t.height}:null}if(void 0!==t&&t.length>0){(e=new hO(this.manager)).setCrossOrigin(this.crossOrigin);for(let e=0,i=t.length;e<i;e++){let i=t[e],n=i.url;if(Array.isArray(n)){let t=[];for(let e=0,i=n.length;e<i;e++){let i=n[e],s=await r(i);null!==s&&(s instanceof HTMLImageElement?t.push(s):t.push(new n$(s.data,s.width,s.height)))}s[i.uuid]=new i6(t)}else{let t=await r(i.url);s[i.uuid]=new i6(t)}}}return s}parseTextures(t,e){function i(t,e){return"number"==typeof t?t:(console.warn("THREE.ObjectLoader.parseTexture: Constant should be in numeric form.",t),e[t])}let s={};if(void 0!==t)for(let r=0,n=t.length;r<n;r++){let n,a=t[r];void 0===a.image&&console.warn('THREE.ObjectLoader: No "image" specified for',a.uuid),void 0===e[a.image]&&console.warn("THREE.ObjectLoader: Undefined image",a.image);let o=e[a.image],h=o.data;Array.isArray(h)?(n=new nc,6===h.length&&(n.needsUpdate=!0)):(n=h&&h.data?new n$:new i7,h&&(n.needsUpdate=!0)),n.source=o,n.uuid=a.uuid,void 0!==a.name&&(n.name=a.name),void 0!==a.mapping&&(n.mapping=i(a.mapping,lt)),void 0!==a.channel&&(n.channel=a.channel),void 0!==a.offset&&n.offset.fromArray(a.offset),void 0!==a.repeat&&n.repeat.fromArray(a.repeat),void 0!==a.center&&n.center.fromArray(a.center),void 0!==a.rotation&&(n.rotation=a.rotation),void 0!==a.wrap&&(n.wrapS=i(a.wrap[0],le),n.wrapT=i(a.wrap[1],le)),void 0!==a.format&&(n.format=a.format),void 0!==a.internalFormat&&(n.internalFormat=a.internalFormat),void 0!==a.type&&(n.type=a.type),void 0!==a.colorSpace&&(n.colorSpace=a.colorSpace),void 0!==a.minFilter&&(n.minFilter=i(a.minFilter,li)),void 0!==a.magFilter&&(n.magFilter=i(a.magFilter,li)),void 0!==a.anisotropy&&(n.anisotropy=a.anisotropy),void 0!==a.flipY&&(n.flipY=a.flipY),void 0!==a.generateMipmaps&&(n.generateMipmaps=a.generateMipmaps),void 0!==a.premultiplyAlpha&&(n.premultiplyAlpha=a.premultiplyAlpha),void 0!==a.unpackAlignment&&(n.unpackAlignment=a.unpackAlignment),void 0!==a.compareFunction&&(n.compareFunction=a.compareFunction),void 0!==a.userData&&(n.userData=a.userData),s[a.uuid]=n}return s}parseObject(t,e,i,s,r){let n,a,o;function h(t){return void 0===e[t]&&console.warn("THREE.ObjectLoader: Undefined geometry",t),e[t]}function l(t){if(void 0!==t){if(Array.isArray(t)){let e=[];for(let s=0,r=t.length;s<r;s++){let r=t[s];void 0===i[r]&&console.warn("THREE.ObjectLoader: Undefined material",r),e.push(i[r])}return e}return void 0===i[t]&&console.warn("THREE.ObjectLoader: Undefined material",t),i[t]}}function u(t){return void 0===s[t]&&console.warn("THREE.ObjectLoader: Undefined texture",t),s[t]}switch(t.type){case"Scene":n=new nx,void 0!==t.background&&(Number.isInteger(t.background)?n.background=new rv(t.background):n.background=u(t.background)),void 0!==t.environment&&(n.environment=u(t.environment)),void 0!==t.fog&&("Fog"===t.fog.type?n.fog=new ng(t.fog.color,t.fog.near,t.fog.far):"FogExp2"===t.fog.type&&(n.fog=new nf(t.fog.color,t.fog.density)),""!==t.fog.name&&(n.fog.name=t.fog.name)),void 0!==t.backgroundBlurriness&&(n.backgroundBlurriness=t.backgroundBlurriness),void 0!==t.backgroundIntensity&&(n.backgroundIntensity=t.backgroundIntensity),void 0!==t.backgroundRotation&&n.backgroundRotation.fromArray(t.backgroundRotation),void 0!==t.environmentIntensity&&(n.environmentIntensity=t.environmentIntensity),void 0!==t.environmentRotation&&n.environmentRotation.fromArray(t.environmentRotation);break;case"PerspectiveCamera":n=new nl(t.fov,t.aspect,t.near,t.far),void 0!==t.focus&&(n.focus=t.focus),void 0!==t.zoom&&(n.zoom=t.zoom),void 0!==t.filmGauge&&(n.filmGauge=t.filmGauge),void 0!==t.filmOffset&&(n.filmOffset=t.filmOffset),void 0!==t.view&&(n.view=Object.assign({},t.view));break;case"OrthographicCamera":n=new hQ(t.left,t.right,t.top,t.bottom,t.near,t.far),void 0!==t.zoom&&(n.zoom=t.zoom),void 0!==t.view&&(n.view=Object.assign({},t.view));break;case"AmbientLight":n=new h1(t.color,t.intensity);break;case"DirectionalLight":(n=new h0(t.color,t.intensity)).target=t.target||"";break;case"PointLight":n=new h$(t.color,t.intensity,t.distance,t.decay);break;case"RectAreaLight":n=new h2(t.color,t.intensity,t.width,t.height);break;case"SpotLight":(n=new hJ(t.color,t.intensity,t.distance,t.angle,t.penumbra,t.decay)).target=t.target||"";break;case"HemisphereLight":n=new hj(t.color,t.groundColor,t.intensity);break;case"LightProbe":n=new h5().fromJSON(t);break;case"SkinnedMesh":n=new nY(a=h(t.geometry),o=l(t.material)),void 0!==t.bindMode&&(n.bindMode=t.bindMode),void 0!==t.bindMatrix&&n.bindMatrix.fromArray(t.bindMatrix),void 0!==t.skeleton&&(n.skeleton=t.skeleton);break;case"Mesh":n=new r8(a=h(t.geometry),o=l(t.material));break;case"InstancedMesh":a=h(t.geometry),o=l(t.material);let c=t.count,d=t.instanceMatrix,p=t.instanceColor;(n=new n7(a,o,c)).instanceMatrix=new n1(new Float32Array(d.array),16),void 0!==p&&(n.instanceColor=new n1(new Float32Array(p.array),p.itemSize));break;case"BatchedMesh":a=h(t.geometry),o=l(t.material),(n=new aS(t.maxInstanceCount,t.maxVertexCount,t.maxIndexCount,o)).geometry=a,n.perObjectFrustumCulled=t.perObjectFrustumCulled,n.sortObjects=t.sortObjects,n._drawRanges=t.drawRanges,n._reservedRanges=t.reservedRanges,n._visibility=t.visibility,n._active=t.active,n._bounds=t.bounds.map(t=>{let e=new sc;e.min.fromArray(t.boxMin),e.max.fromArray(t.boxMax);let i=new sC;return i.radius=t.sphereRadius,i.center.fromArray(t.sphereCenter),{boxInitialized:t.boxInitialized,box:e,sphereInitialized:t.sphereInitialized,sphere:i}}),n._maxInstanceCount=t.maxInstanceCount,n._maxVertexCount=t.maxVertexCount,n._maxIndexCount=t.maxIndexCount,n._geometryInitialized=t.geometryInitialized,n._geometryCount=t.geometryCount,n._matricesTexture=u(t.matricesTexture.uuid),void 0!==t.colorsTexture&&(n._colorsTexture=u(t.colorsTexture.uuid));break;case"LOD":n=new nL;break;case"Line":n=new aR(h(t.geometry),l(t.material));break;case"LineLoop":n=new aF(h(t.geometry),l(t.material));break;case"LineSegments":n=new aN(h(t.geometry),l(t.material));break;case"PointCloud":case"Points":n=new aD(h(t.geometry),l(t.material));break;case"Sprite":n=new nO(l(t.material));break;case"Group":n=new np;break;case"Bone":n=new nG;break;default:n=new re}if(n.uuid=t.uuid,void 0!==t.name&&(n.name=t.name),void 0!==t.matrix?(n.matrix.fromArray(t.matrix),void 0!==t.matrixAutoUpdate&&(n.matrixAutoUpdate=t.matrixAutoUpdate),n.matrixAutoUpdate&&n.matrix.decompose(n.position,n.quaternion,n.scale)):(void 0!==t.position&&n.position.fromArray(t.position),void 0!==t.rotation&&n.rotation.fromArray(t.rotation),void 0!==t.quaternion&&n.quaternion.fromArray(t.quaternion),void 0!==t.scale&&n.scale.fromArray(t.scale)),void 0!==t.up&&n.up.fromArray(t.up),void 0!==t.castShadow&&(n.castShadow=t.castShadow),void 0!==t.receiveShadow&&(n.receiveShadow=t.receiveShadow),t.shadow&&(void 0!==t.shadow.intensity&&(n.shadow.intensity=t.shadow.intensity),void 0!==t.shadow.bias&&(n.shadow.bias=t.shadow.bias),void 0!==t.shadow.normalBias&&(n.shadow.normalBias=t.shadow.normalBias),void 0!==t.shadow.radius&&(n.shadow.radius=t.shadow.radius),void 0!==t.shadow.mapSize&&n.shadow.mapSize.fromArray(t.shadow.mapSize),void 0!==t.shadow.camera&&(n.shadow.camera=this.parseObject(t.shadow.camera))),void 0!==t.visible&&(n.visible=t.visible),void 0!==t.frustumCulled&&(n.frustumCulled=t.frustumCulled),void 0!==t.renderOrder&&(n.renderOrder=t.renderOrder),void 0!==t.userData&&(n.userData=t.userData),void 0!==t.layers&&(n.layers.mask=t.layers),void 0!==t.children){let a=t.children;for(let t=0;t<a.length;t++)n.add(this.parseObject(a[t],e,i,s,r))}if(void 0!==t.animations){let e=t.animations;for(let t=0;t<e.length;t++){let i=e[t];n.animations.push(r[i])}}if("LOD"===t.type){void 0!==t.autoUpdate&&(n.autoUpdate=t.autoUpdate);let e=t.levels;for(let t=0;t<e.length;t++){let i=e[t],s=n.getObjectByProperty("uuid",i.object);void 0!==s&&n.addLevel(s,i.distance,i.hysteresis)}}return n}bindSkeletons(t,e){0!==Object.keys(e).length&&t.traverse(function(t){if(!0===t.isSkinnedMesh&&void 0!==t.skeleton){let i=e[t.skeleton];void 0===i?console.warn("THREE.ObjectLoader: No skeleton found with UUID:",t.skeleton):t.bind(i,t.bindMatrix)}})}bindLightTargets(t){t.traverse(function(e){if(e.isDirectionalLight||e.isSpotLight){let i=e.target,s=t.getObjectByProperty("uuid",i);void 0!==s?e.target=s:e.target=new re}})}}let lt={UVMapping:ty,CubeReflectionMapping:tf,CubeRefractionMapping:tg,EquirectangularReflectionMapping:tx,EquirectangularRefractionMapping:tb,CubeUVReflectionMapping:tv},le={RepeatWrapping:tw,ClampToEdgeWrapping:tM,MirroredRepeatWrapping:tS},li={NearestFilter:t_,NearestMipmapNearestFilter:tA,NearestMipmapLinearFilter:tz,LinearFilter:tC,LinearMipmapNearestFilter:tk,LinearMipmapLinearFilter:tR};class ls extends hC{constructor(t){super(t),this.isImageBitmapLoader=!0,"undefined"==typeof createImageBitmap&&console.warn("THREE.ImageBitmapLoader: createImageBitmap() not supported."),"undefined"==typeof fetch&&console.warn("THREE.ImageBitmapLoader: fetch() not supported."),this.options={premultiplyAlpha:"none"}}setOptions(t){return this.options=t,this}load(t,e,i,s){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);let r=this,n=hT.get(t);if(void 0!==n)return(r.manager.itemStart(t),n.then)?void n.then(i=>{e&&e(i),r.manager.itemEnd(t)}).catch(t=>{s&&s(t)}):(setTimeout(function(){e&&e(n),r.manager.itemEnd(t)},0),n);let a={};a.credentials="anonymous"===this.crossOrigin?"same-origin":"include",a.headers=this.requestHeader;let o=fetch(t,a).then(function(t){return t.blob()}).then(function(t){return createImageBitmap(t,Object.assign(r.options,{colorSpaceConversion:"none"}))}).then(function(i){return hT.add(t,i),e&&e(i),r.manager.itemEnd(t),i}).catch(function(e){s&&s(e),hT.remove(t),r.manager.itemError(t),r.manager.itemEnd(t)});hT.add(t,o),r.manager.itemStart(t)}}class lr{static getContext(){return void 0===n&&(n=new(window.AudioContext||window.webkitAudioContext)),n}static setContext(t){n=t}}class ln extends hC{constructor(t){super(t)}load(t,e,i,s){let r=this,n=new hR(this.manager);function a(e){s?s(e):console.error(e),r.manager.itemError(t)}n.setResponseType("arraybuffer"),n.setPath(this.path),n.setRequestHeader(this.requestHeader),n.setWithCredentials(this.withCredentials),n.load(t,function(t){try{let i=t.slice(0);lr.getContext().decodeAudioData(i,function(t){e(t)}).catch(a)}catch(t){a(t)}},i,s)}}let la=new sV,lo=new sV,lh=new sV;class ll{constructor(){this.type="StereoCamera",this.aspect=1,this.eyeSep=.064,this.cameraL=new nl,this.cameraL.layers.enable(1),this.cameraL.matrixAutoUpdate=!1,this.cameraR=new nl,this.cameraR.layers.enable(2),this.cameraR.matrixAutoUpdate=!1,this._cache={focus:null,fov:null,aspect:null,near:null,far:null,zoom:null,eyeSep:null}}update(t){let e=this._cache;if(e.focus!==t.focus||e.fov!==t.fov||e.aspect!==t.aspect*this.aspect||e.near!==t.near||e.far!==t.far||e.zoom!==t.zoom||e.eyeSep!==this.eyeSep){let i,s;e.focus=t.focus,e.fov=t.fov,e.aspect=t.aspect*this.aspect,e.near=t.near,e.far=t.far,e.zoom=t.zoom,e.eyeSep=this.eyeSep,lh.copy(t.projectionMatrix);let r=e.eyeSep/2,n=r*e.near/e.focus,a=e.near*Math.tan(iB*e.fov*.5)/e.zoom;lo.elements[12]=-r,la.elements[12]=r,i=-a*e.aspect+n,s=a*e.aspect+n,lh.elements[0]=2*e.near/(s-i),lh.elements[8]=(s+i)/(s-i),this.cameraL.projectionMatrix.copy(lh),i=-a*e.aspect-n,s=a*e.aspect-n,lh.elements[0]=2*e.near/(s-i),lh.elements[8]=(s+i)/(s-i),this.cameraR.projectionMatrix.copy(lh)}this.cameraL.matrixWorld.copy(t.matrixWorld).multiply(lo),this.cameraR.matrixWorld.copy(t.matrixWorld).multiply(la)}}class lu extends nl{constructor(t=[]){super(),this.isArrayCamera=!0,this.cameras=t,this.index=0}}class lc{constructor(t=!0){this.autoStart=t,this.startTime=0,this.oldTime=0,this.elapsedTime=0,this.running=!1}start(){this.startTime=ld(),this.oldTime=this.startTime,this.elapsedTime=0,this.running=!0}stop(){this.getElapsedTime(),this.running=!1,this.autoStart=!1}getElapsedTime(){return this.getDelta(),this.elapsedTime}getDelta(){let t=0;if(this.autoStart&&!this.running)return this.start(),0;if(this.running){let e=ld();t=(e-this.oldTime)/1e3,this.oldTime=e,this.elapsedTime+=t}return t}}function ld(){return performance.now()}let lp=new sh,lm=new so,ly=new sh,lf=new sh;class lg extends re{constructor(){super(),this.type="AudioListener",this.context=lr.getContext(),this.gain=this.context.createGain(),this.gain.connect(this.context.destination),this.filter=null,this.timeDelta=0,this._clock=new lc}getInput(){return this.gain}removeFilter(){return null!==this.filter&&(this.gain.disconnect(this.filter),this.filter.disconnect(this.context.destination),this.gain.connect(this.context.destination),this.filter=null),this}getFilter(){return this.filter}setFilter(t){return null!==this.filter?(this.gain.disconnect(this.filter),this.filter.disconnect(this.context.destination)):this.gain.disconnect(this.context.destination),this.filter=t,this.gain.connect(this.filter),this.filter.connect(this.context.destination),this}getMasterVolume(){return this.gain.gain.value}setMasterVolume(t){return this.gain.gain.setTargetAtTime(t,this.context.currentTime,.01),this}updateMatrixWorld(t){super.updateMatrixWorld(t);let e=this.context.listener,i=this.up;if(this.timeDelta=this._clock.getDelta(),this.matrixWorld.decompose(lp,lm,ly),lf.set(0,0,-1).applyQuaternion(lm),e.positionX){let t=this.context.currentTime+this.timeDelta;e.positionX.linearRampToValueAtTime(lp.x,t),e.positionY.linearRampToValueAtTime(lp.y,t),e.positionZ.linearRampToValueAtTime(lp.z,t),e.forwardX.linearRampToValueAtTime(lf.x,t),e.forwardY.linearRampToValueAtTime(lf.y,t),e.forwardZ.linearRampToValueAtTime(lf.z,t),e.upX.linearRampToValueAtTime(i.x,t),e.upY.linearRampToValueAtTime(i.y,t),e.upZ.linearRampToValueAtTime(i.z,t)}else e.setPosition(lp.x,lp.y,lp.z),e.setOrientation(lf.x,lf.y,lf.z,i.x,i.y,i.z)}}class lx extends re{constructor(t){super(),this.type="Audio",this.listener=t,this.context=t.context,this.gain=this.context.createGain(),this.gain.connect(t.getInput()),this.autoplay=!1,this.buffer=null,this.detune=0,this.loop=!1,this.loopStart=0,this.loopEnd=0,this.offset=0,this.duration=void 0,this.playbackRate=1,this.isPlaying=!1,this.hasPlaybackControl=!0,this.source=null,this.sourceType="empty",this._startedAt=0,this._progress=0,this._connected=!1,this.filters=[]}getOutput(){return this.gain}setNodeSource(t){return this.hasPlaybackControl=!1,this.sourceType="audioNode",this.source=t,this.connect(),this}setMediaElementSource(t){return this.hasPlaybackControl=!1,this.sourceType="mediaNode",this.source=this.context.createMediaElementSource(t),this.connect(),this}setMediaStreamSource(t){return this.hasPlaybackControl=!1,this.sourceType="mediaStreamNode",this.source=this.context.createMediaStreamSource(t),this.connect(),this}setBuffer(t){return this.buffer=t,this.sourceType="buffer",this.autoplay&&this.play(),this}play(t=0){if(!0===this.isPlaying)return void console.warn("THREE.Audio: Audio is already playing.");if(!1===this.hasPlaybackControl)return void console.warn("THREE.Audio: this Audio has no playback control.");this._startedAt=this.context.currentTime+t;let e=this.context.createBufferSource();return e.buffer=this.buffer,e.loop=this.loop,e.loopStart=this.loopStart,e.loopEnd=this.loopEnd,e.onended=this.onEnded.bind(this),e.start(this._startedAt,this._progress+this.offset,this.duration),this.isPlaying=!0,this.source=e,this.setDetune(this.detune),this.setPlaybackRate(this.playbackRate),this.connect()}pause(){return!1===this.hasPlaybackControl?void console.warn("THREE.Audio: this Audio has no playback control."):(!0===this.isPlaying&&(this._progress+=Math.max(this.context.currentTime-this._startedAt,0)*this.playbackRate,!0===this.loop&&(this._progress=this._progress%(this.duration||this.buffer.duration)),this.source.stop(),this.source.onended=null,this.isPlaying=!1),this)}stop(t=0){return!1===this.hasPlaybackControl?void console.warn("THREE.Audio: this Audio has no playback control."):(this._progress=0,null!==this.source&&(this.source.stop(this.context.currentTime+t),this.source.onended=null),this.isPlaying=!1,this)}connect(){if(this.filters.length>0){this.source.connect(this.filters[0]);for(let t=1,e=this.filters.length;t<e;t++)this.filters[t-1].connect(this.filters[t]);this.filters[this.filters.length-1].connect(this.getOutput())}else this.source.connect(this.getOutput());return this._connected=!0,this}disconnect(){if(!1!==this._connected){if(this.filters.length>0){this.source.disconnect(this.filters[0]);for(let t=1,e=this.filters.length;t<e;t++)this.filters[t-1].disconnect(this.filters[t]);this.filters[this.filters.length-1].disconnect(this.getOutput())}else this.source.disconnect(this.getOutput());return this._connected=!1,this}}getFilters(){return this.filters}setFilters(t){return t||(t=[]),!0===this._connected?(this.disconnect(),this.filters=t.slice(),this.connect()):this.filters=t.slice(),this}setDetune(t){return this.detune=t,!0===this.isPlaying&&void 0!==this.source.detune&&this.source.detune.setTargetAtTime(this.detune,this.context.currentTime,.01),this}getDetune(){return this.detune}getFilter(){return this.getFilters()[0]}setFilter(t){return this.setFilters(t?[t]:[])}setPlaybackRate(t){return!1===this.hasPlaybackControl?void console.warn("THREE.Audio: this Audio has no playback control."):(this.playbackRate=t,!0===this.isPlaying&&this.source.playbackRate.setTargetAtTime(this.playbackRate,this.context.currentTime,.01),this)}getPlaybackRate(){return this.playbackRate}onEnded(){this.isPlaying=!1,this._progress=0}getLoop(){return!1===this.hasPlaybackControl?(console.warn("THREE.Audio: this Audio has no playback control."),!1):this.loop}setLoop(t){return!1===this.hasPlaybackControl?void console.warn("THREE.Audio: this Audio has no playback control."):(this.loop=t,!0===this.isPlaying&&(this.source.loop=this.loop),this)}setLoopStart(t){return this.loopStart=t,this}setLoopEnd(t){return this.loopEnd=t,this}getVolume(){return this.gain.gain.value}setVolume(t){return this.gain.gain.setTargetAtTime(t,this.context.currentTime,.01),this}copy(t,e){return(super.copy(t,e),"buffer"!==t.sourceType)?console.warn("THREE.Audio: Audio source type cannot be copied."):(this.autoplay=t.autoplay,this.buffer=t.buffer,this.detune=t.detune,this.loop=t.loop,this.loopStart=t.loopStart,this.loopEnd=t.loopEnd,this.offset=t.offset,this.duration=t.duration,this.playbackRate=t.playbackRate,this.hasPlaybackControl=t.hasPlaybackControl,this.sourceType=t.sourceType,this.filters=t.filters.slice()),this}clone(t){return new this.constructor(this.listener).copy(this,t)}}let lb=new sh,lv=new so,lw=new sh,lM=new sh;class lS extends lx{constructor(t){super(t),this.panner=this.context.createPanner(),this.panner.panningModel="HRTF",this.panner.connect(this.gain)}connect(){return super.connect(),this.panner.connect(this.gain),this}disconnect(){return super.disconnect(),this.panner.disconnect(this.gain),this}getOutput(){return this.panner}getRefDistance(){return this.panner.refDistance}setRefDistance(t){return this.panner.refDistance=t,this}getRolloffFactor(){return this.panner.rolloffFactor}setRolloffFactor(t){return this.panner.rolloffFactor=t,this}getDistanceModel(){return this.panner.distanceModel}setDistanceModel(t){return this.panner.distanceModel=t,this}getMaxDistance(){return this.panner.maxDistance}setMaxDistance(t){return this.panner.maxDistance=t,this}setDirectionalCone(t,e,i){return this.panner.coneInnerAngle=t,this.panner.coneOuterAngle=e,this.panner.coneOuterGain=i,this}updateMatrixWorld(t){if(super.updateMatrixWorld(t),!0===this.hasPlaybackControl&&!1===this.isPlaying)return;this.matrixWorld.decompose(lb,lv,lw),lM.set(0,0,1).applyQuaternion(lv);let e=this.panner;if(e.positionX){let t=this.context.currentTime+this.listener.timeDelta;e.positionX.linearRampToValueAtTime(lb.x,t),e.positionY.linearRampToValueAtTime(lb.y,t),e.positionZ.linearRampToValueAtTime(lb.z,t),e.orientationX.linearRampToValueAtTime(lM.x,t),e.orientationY.linearRampToValueAtTime(lM.y,t),e.orientationZ.linearRampToValueAtTime(lM.z,t)}else e.setPosition(lb.x,lb.y,lb.z),e.setOrientation(lM.x,lM.y,lM.z)}}class l_{constructor(t,e=2048){this.analyser=t.context.createAnalyser(),this.analyser.fftSize=e,this.data=new Uint8Array(this.analyser.frequencyBinCount),t.getOutput().connect(this.analyser)}getFrequencyData(){return this.analyser.getByteFrequencyData(this.data),this.data}getAverageFrequency(){let t=0,e=this.getFrequencyData();for(let i=0;i<e.length;i++)t+=e[i];return t/e.length}}class lA{constructor(t,e,i){let s,r,n;switch(this.binding=t,this.valueSize=i,e){case"quaternion":s=this._slerp,r=this._slerpAdditive,n=this._setAdditiveIdentityQuaternion,this.buffer=new Float64Array(6*i),this._workIndex=5;break;case"string":case"bool":s=this._select,r=this._select,n=this._setAdditiveIdentityOther,this.buffer=Array(5*i);break;default:s=this._lerp,r=this._lerpAdditive,n=this._setAdditiveIdentityNumeric,this.buffer=new Float64Array(5*i)}this._mixBufferRegion=s,this._mixBufferRegionAdditive=r,this._setIdentity=n,this._origIndex=3,this._addIndex=4,this.cumulativeWeight=0,this.cumulativeWeightAdditive=0,this.useCount=0,this.referenceCount=0}accumulate(t,e){let i=this.buffer,s=this.valueSize,r=t*s+s,n=this.cumulativeWeight;if(0===n){for(let t=0;t!==s;++t)i[r+t]=i[t];n=e}else{n+=e;let t=e/n;this._mixBufferRegion(i,r,0,t,s)}this.cumulativeWeight=n}accumulateAdditive(t){let e=this.buffer,i=this.valueSize,s=i*this._addIndex;0===this.cumulativeWeightAdditive&&this._setIdentity(),this._mixBufferRegionAdditive(e,s,0,t,i),this.cumulativeWeightAdditive+=t}apply(t){let e=this.valueSize,i=this.buffer,s=t*e+e,r=this.cumulativeWeight,n=this.cumulativeWeightAdditive,a=this.binding;if(this.cumulativeWeight=0,this.cumulativeWeightAdditive=0,r<1){let t=e*this._origIndex;this._mixBufferRegion(i,s,t,1-r,e)}n>0&&this._mixBufferRegionAdditive(i,s,this._addIndex*e,1,e);for(let t=e,r=e+e;t!==r;++t)if(i[t]!==i[t+e]){a.setValue(i,s);break}}saveOriginalState(){let t=this.binding,e=this.buffer,i=this.valueSize,s=i*this._origIndex;t.getValue(e,s);for(let t=i;t!==s;++t)e[t]=e[s+t%i];this._setIdentity(),this.cumulativeWeight=0,this.cumulativeWeightAdditive=0}restoreOriginalState(){let t=3*this.valueSize;this.binding.setValue(this.buffer,t)}_setAdditiveIdentityNumeric(){let t=this._addIndex*this.valueSize,e=t+this.valueSize;for(let i=t;i<e;i++)this.buffer[i]=0}_setAdditiveIdentityQuaternion(){this._setAdditiveIdentityNumeric(),this.buffer[this._addIndex*this.valueSize+3]=1}_setAdditiveIdentityOther(){let t=this._origIndex*this.valueSize,e=this._addIndex*this.valueSize;for(let i=0;i<this.valueSize;i++)this.buffer[e+i]=this.buffer[t+i]}_select(t,e,i,s,r){if(s>=.5)for(let s=0;s!==r;++s)t[e+s]=t[i+s]}_slerp(t,e,i,s){so.slerpFlat(t,e,t,e,t,i,s)}_slerpAdditive(t,e,i,s,r){let n=this._workIndex*r;so.multiplyQuaternionsFlat(t,n,t,e,t,i),so.slerpFlat(t,e,t,e,t,n,s)}_lerp(t,e,i,s,r){let n=1-s;for(let a=0;a!==r;++a){let r=e+a;t[r]=t[r]*n+t[i+a]*s}}_lerpAdditive(t,e,i,s,r){for(let n=0;n!==r;++n){let r=e+n;t[r]=t[r]+t[i+n]*s}}}let lT="\\[\\]\\.:\\/",lz=RegExp("["+lT+"]","g"),lI="[^"+lT+"]",lC="[^"+lT.replace("\\.","")+"]",lk=/((?:WC+[\/:])*)/.source.replace("WC",lI),lB=/(WCOD+)?/.source.replace("WCOD",lC),lR=RegExp("^"+lk+lB+/(?:\.(WC+)(?:\[(.+)\])?)?/.source.replace("WC",lI)+/\.(WC+)(?:\[(.+)\])?/.source.replace("WC",lI)+"$"),lE=["material","materials","bones","map"];class lP{constructor(t,e,i){let s=i||lO.parseTrackName(e);this._targetGroup=t,this._bindings=t.subscribe_(e,s)}getValue(t,e){this.bind();let i=this._targetGroup.nCachedObjects_,s=this._bindings[i];void 0!==s&&s.getValue(t,e)}setValue(t,e){let i=this._bindings;for(let s=this._targetGroup.nCachedObjects_,r=i.length;s!==r;++s)i[s].setValue(t,e)}bind(){let t=this._bindings;for(let e=this._targetGroup.nCachedObjects_,i=t.length;e!==i;++e)t[e].bind()}unbind(){let t=this._bindings;for(let e=this._targetGroup.nCachedObjects_,i=t.length;e!==i;++e)t[e].unbind()}}class lO{constructor(t,e,i){this.path=e,this.parsedPath=i||lO.parseTrackName(e),this.node=lO.findNode(t,this.parsedPath.nodeName),this.rootNode=t,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}static create(t,e,i){return t&&t.isAnimationObjectGroup?new lO.Composite(t,e,i):new lO(t,e,i)}static sanitizeNodeName(t){return t.replace(/\s/g,"_").replace(lz,"")}static parseTrackName(t){let e=lR.exec(t);if(null===e)throw Error("PropertyBinding: Cannot parse trackName: "+t);let i={nodeName:e[2],objectName:e[3],objectIndex:e[4],propertyName:e[5],propertyIndex:e[6]},s=i.nodeName&&i.nodeName.lastIndexOf(".");if(void 0!==s&&-1!==s){let t=i.nodeName.substring(s+1);-1!==lE.indexOf(t)&&(i.nodeName=i.nodeName.substring(0,s),i.objectName=t)}if(null===i.propertyName||0===i.propertyName.length)throw Error("PropertyBinding: can not parse propertyName from trackName: "+t);return i}static findNode(t,e){if(void 0===e||""===e||"."===e||-1===e||e===t.name||e===t.uuid)return t;if(t.skeleton){let i=t.skeleton.getBoneByName(e);if(void 0!==i)return i}if(t.children){let i=function(t){for(let s=0;s<t.length;s++){let r=t[s];if(r.name===e||r.uuid===e)return r;let n=i(r.children);if(n)return n}return null},s=i(t.children);if(s)return s}return null}_getValue_unavailable(){}_setValue_unavailable(){}_getValue_direct(t,e){t[e]=this.targetObject[this.propertyName]}_getValue_array(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)t[e++]=i[s]}_getValue_arrayElement(t,e){t[e]=this.resolvedProperty[this.propertyIndex]}_getValue_toArray(t,e){this.resolvedProperty.toArray(t,e)}_setValue_direct(t,e){this.targetObject[this.propertyName]=t[e]}_setValue_direct_setNeedsUpdate(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.needsUpdate=!0}_setValue_direct_setMatrixWorldNeedsUpdate(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_array(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++]}_setValue_array_setNeedsUpdate(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++];this.targetObject.needsUpdate=!0}_setValue_array_setMatrixWorldNeedsUpdate(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++];this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_arrayElement(t,e){this.resolvedProperty[this.propertyIndex]=t[e]}_setValue_arrayElement_setNeedsUpdate(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.needsUpdate=!0}_setValue_arrayElement_setMatrixWorldNeedsUpdate(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_fromArray(t,e){this.resolvedProperty.fromArray(t,e)}_setValue_fromArray_setNeedsUpdate(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.needsUpdate=!0}_setValue_fromArray_setMatrixWorldNeedsUpdate(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.matrixWorldNeedsUpdate=!0}_getValue_unbound(t,e){this.bind(),this.getValue(t,e)}_setValue_unbound(t,e){this.bind(),this.setValue(t,e)}bind(){let t=this.node,e=this.parsedPath,i=e.objectName,s=e.propertyName,r=e.propertyIndex;if(t||(t=lO.findNode(this.rootNode,e.nodeName),this.node=t),this.getValue=this._getValue_unavailable,this.setValue=this._setValue_unavailable,!t)return void console.warn("THREE.PropertyBinding: No target node found for track: "+this.path+".");if(i){let s=e.objectIndex;switch(i){case"materials":if(!t.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!t.material.materials)return void console.error("THREE.PropertyBinding: Can not bind to material.materials as node.material does not have a materials array.",this);t=t.material.materials;break;case"bones":if(!t.skeleton)return void console.error("THREE.PropertyBinding: Can not bind to bones as node does not have a skeleton.",this);t=t.skeleton.bones;for(let e=0;e<t.length;e++)if(t[e].name===s){s=e;break}break;case"map":if("map"in t){t=t.map;break}if(!t.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!t.material.map)return void console.error("THREE.PropertyBinding: Can not bind to material.map as node.material does not have a map.",this);t=t.material.map;break;default:if(void 0===t[i])return void console.error("THREE.PropertyBinding: Can not bind to objectName of node undefined.",this);t=t[i]}if(void 0!==s){if(void 0===t[s])return void console.error("THREE.PropertyBinding: Trying to bind to objectIndex of objectName, but is undefined.",this,t);t=t[s]}}let n=t[s];if(void 0===n)return void console.error("THREE.PropertyBinding: Trying to update property for track: "+e.nodeName+"."+s+" but it wasn't found.",t);let a=this.Versioning.None;this.targetObject=t,!0===t.isMaterial?a=this.Versioning.NeedsUpdate:!0===t.isObject3D&&(a=this.Versioning.MatrixWorldNeedsUpdate);let o=this.BindingType.Direct;if(void 0!==r){if("morphTargetInfluences"===s){if(!t.geometry)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.",this);if(!t.geometry.morphAttributes)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.morphAttributes.",this);void 0!==t.morphTargetDictionary[r]&&(r=t.morphTargetDictionary[r])}o=this.BindingType.ArrayElement,this.resolvedProperty=n,this.propertyIndex=r}else void 0!==n.fromArray&&void 0!==n.toArray?(o=this.BindingType.HasFromToArray,this.resolvedProperty=n):Array.isArray(n)?(o=this.BindingType.EntireArray,this.resolvedProperty=n):this.propertyName=s;this.getValue=this.GetterByBindingType[o],this.setValue=this.SetterByBindingTypeAndVersioning[o][a]}unbind(){this.node=null,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}}lO.Composite=lP,lO.prototype.BindingType={Direct:0,EntireArray:1,ArrayElement:2,HasFromToArray:3},lO.prototype.Versioning={None:0,NeedsUpdate:1,MatrixWorldNeedsUpdate:2},lO.prototype.GetterByBindingType=[lO.prototype._getValue_direct,lO.prototype._getValue_array,lO.prototype._getValue_arrayElement,lO.prototype._getValue_toArray],lO.prototype.SetterByBindingTypeAndVersioning=[[lO.prototype._setValue_direct,lO.prototype._setValue_direct_setNeedsUpdate,lO.prototype._setValue_direct_setMatrixWorldNeedsUpdate],[lO.prototype._setValue_array,lO.prototype._setValue_array_setNeedsUpdate,lO.prototype._setValue_array_setMatrixWorldNeedsUpdate],[lO.prototype._setValue_arrayElement,lO.prototype._setValue_arrayElement_setNeedsUpdate,lO.prototype._setValue_arrayElement_setMatrixWorldNeedsUpdate],[lO.prototype._setValue_fromArray,lO.prototype._setValue_fromArray_setNeedsUpdate,lO.prototype._setValue_fromArray_setMatrixWorldNeedsUpdate]];class lN{constructor(){this.isAnimationObjectGroup=!0,this.uuid=iE(),this._objects=Array.prototype.slice.call(arguments),this.nCachedObjects_=0;let t={};this._indicesByUUID=t;for(let e=0,i=arguments.length;e!==i;++e)t[arguments[e].uuid]=e;this._paths=[],this._parsedPaths=[],this._bindings=[],this._bindingsIndicesByPath={};let e=this;this.stats={objects:{get total(){return e._objects.length},get inUse(){return this.total-e.nCachedObjects_}},get bindingsPerObject(){return e._bindings.length}}}add(){let t=this._objects,e=this._indicesByUUID,i=this._paths,s=this._parsedPaths,r=this._bindings,n=r.length,a,o=t.length,h=this.nCachedObjects_;for(let l=0,u=arguments.length;l!==u;++l){let u=arguments[l],c=u.uuid,d=e[c];if(void 0===d){d=o++,e[c]=d,t.push(u);for(let t=0;t!==n;++t)r[t].push(new lO(u,i[t],s[t]))}else if(d<h){a=t[d];let o=--h,l=t[o];e[l.uuid]=d,t[d]=l,e[c]=o,t[o]=u;for(let t=0;t!==n;++t){let e=r[t],n=e[o],a=e[d];e[d]=n,void 0===a&&(a=new lO(u,i[t],s[t])),e[o]=a}}else t[d]!==a&&console.error("THREE.AnimationObjectGroup: Different objects with the same UUID detected. Clean the caches or recreate your infrastructure when reloading scenes.")}this.nCachedObjects_=h}remove(){let t=this._objects,e=this._indicesByUUID,i=this._bindings,s=i.length,r=this.nCachedObjects_;for(let n=0,a=arguments.length;n!==a;++n){let a=arguments[n],o=a.uuid,h=e[o];if(void 0!==h&&h>=r){let n=r++,l=t[n];e[l.uuid]=h,t[h]=l,e[o]=n,t[n]=a;for(let t=0;t!==s;++t){let e=i[t],s=e[n],r=e[h];e[h]=s,e[n]=r}}}this.nCachedObjects_=r}uncache(){let t=this._objects,e=this._indicesByUUID,i=this._bindings,s=i.length,r=this.nCachedObjects_,n=t.length;for(let a=0,o=arguments.length;a!==o;++a){let o=arguments[a],h=o.uuid,l=e[h];if(void 0!==l)if(delete e[h],l<r){let a=--r,o=t[a],h=--n,u=t[h];e[o.uuid]=l,t[l]=o,e[u.uuid]=a,t[a]=u,t.pop();for(let t=0;t!==s;++t){let e=i[t],s=e[a],r=e[h];e[l]=s,e[a]=r,e.pop()}}else{let r=--n,a=t[r];r>0&&(e[a.uuid]=l),t[l]=a,t.pop();for(let t=0;t!==s;++t){let e=i[t];e[l]=e[r],e.pop()}}}this.nCachedObjects_=r}subscribe_(t,e){let i=this._bindingsIndicesByPath,s=i[t],r=this._bindings;if(void 0!==s)return r[s];let n=this._paths,a=this._parsedPaths,o=this._objects,h=o.length,l=this.nCachedObjects_,u=Array(h);s=r.length,i[t]=s,n.push(t),a.push(e),r.push(u);for(let i=l,s=o.length;i!==s;++i){let s=o[i];u[i]=new lO(s,t,e)}return u}unsubscribe_(t){let e=this._bindingsIndicesByPath,i=e[t];if(void 0!==i){let s=this._paths,r=this._parsedPaths,n=this._bindings,a=n.length-1,o=n[a];e[t[a]]=i,n[i]=o,n.pop(),r[i]=r[a],r.pop(),s[i]=s[a],s.pop()}}}class lF{constructor(t,e,i=null,s=e.blendMode){this._mixer=t,this._clip=e,this._localRoot=i,this.blendMode=s;let r=e.tracks,n=r.length,a=Array(n),o={endingStart:eE,endingEnd:eE};for(let t=0;t!==n;++t){let e=r[t].createInterpolant(null);a[t]=e,e.settings=o}this._interpolantSettings=o,this._interpolants=a,this._propertyBindings=Array(n),this._cacheIndex=null,this._byClipCacheIndex=null,this._timeScaleInterpolant=null,this._weightInterpolant=null,this.loop=eI,this._loopCount=-1,this._startTime=null,this.time=0,this.timeScale=1,this._effectiveTimeScale=1,this.weight=1,this._effectiveWeight=1,this.repetitions=1/0,this.paused=!1,this.enabled=!0,this.clampWhenFinished=!1,this.zeroSlopeAtStart=!0,this.zeroSlopeAtEnd=!0}play(){return this._mixer._activateAction(this),this}stop(){return this._mixer._deactivateAction(this),this.reset()}reset(){return this.paused=!1,this.enabled=!0,this.time=0,this._loopCount=-1,this._startTime=null,this.stopFading().stopWarping()}isRunning(){return this.enabled&&!this.paused&&0!==this.timeScale&&null===this._startTime&&this._mixer._isActiveAction(this)}isScheduled(){return this._mixer._isActiveAction(this)}startAt(t){return this._startTime=t,this}setLoop(t,e){return this.loop=t,this.repetitions=e,this}setEffectiveWeight(t){return this.weight=t,this._effectiveWeight=this.enabled?t:0,this.stopFading()}getEffectiveWeight(){return this._effectiveWeight}fadeIn(t){return this._scheduleFading(t,0,1)}fadeOut(t){return this._scheduleFading(t,1,0)}crossFadeFrom(t,e,i=!1){if(t.fadeOut(e),this.fadeIn(e),!0===i){let i=this._clip.duration,s=t._clip.duration;t.warp(1,s/i,e),this.warp(i/s,1,e)}return this}crossFadeTo(t,e,i=!1){return t.crossFadeFrom(this,e,i)}stopFading(){let t=this._weightInterpolant;return null!==t&&(this._weightInterpolant=null,this._mixer._takeBackControlInterpolant(t)),this}setEffectiveTimeScale(t){return this.timeScale=t,this._effectiveTimeScale=this.paused?0:t,this.stopWarping()}getEffectiveTimeScale(){return this._effectiveTimeScale}setDuration(t){return this.timeScale=this._clip.duration/t,this.stopWarping()}syncWith(t){return this.time=t.time,this.timeScale=t.timeScale,this.stopWarping()}halt(t){return this.warp(this._effectiveTimeScale,0,t)}warp(t,e,i){let s=this._mixer,r=s.time,n=this.timeScale,a=this._timeScaleInterpolant;null===a&&(a=s._lendControlInterpolant(),this._timeScaleInterpolant=a);let o=a.parameterPositions,h=a.sampleValues;return o[0]=r,o[1]=r+i,h[0]=t/n,h[1]=e/n,this}stopWarping(){let t=this._timeScaleInterpolant;return null!==t&&(this._timeScaleInterpolant=null,this._mixer._takeBackControlInterpolant(t)),this}getMixer(){return this._mixer}getClip(){return this._clip}getRoot(){return this._localRoot||this._mixer._root}_update(t,e,i,s){if(!this.enabled)return void this._updateWeight(t);let r=this._startTime;if(null!==r){let s=(t-r)*i;s<0||0===i?e=0:(this._startTime=null,e=i*s)}e*=this._updateTimeScale(t);let n=this._updateTime(e),a=this._updateWeight(t);if(a>0){let t=this._interpolants,e=this._propertyBindings;if(this.blendMode===eF)for(let i=0,s=t.length;i!==s;++i)t[i].evaluate(n),e[i].accumulateAdditive(a);else for(let i=0,r=t.length;i!==r;++i)t[i].evaluate(n),e[i].accumulate(s,a)}}_updateWeight(t){let e=0;if(this.enabled){e=this.weight;let i=this._weightInterpolant;if(null!==i){let s=i.evaluate(t)[0];e*=s,t>i.parameterPositions[1]&&(this.stopFading(),0===s&&(this.enabled=!1))}}return this._effectiveWeight=e,e}_updateTimeScale(t){let e=0;if(!this.paused){e=this.timeScale;let i=this._timeScaleInterpolant;null!==i&&(e*=i.evaluate(t)[0],t>i.parameterPositions[1]&&(this.stopWarping(),0===e?this.paused=!0:this.timeScale=e))}return this._effectiveTimeScale=e,e}_updateTime(t){let e=this._clip.duration,i=this.loop,s=this.time+t,r=this._loopCount,n=i===eC;if(0===t)return -1===r?s:n&&(1&r)==1?e-s:s;if(i===ez){-1===r&&(this._loopCount=0,this._setEndings(!0,!0,!1));r:{if(s>=e)s=e;else if(s<0)s=0;else{this.time=s;break r}this.clampWhenFinished?this.paused=!0:this.enabled=!1,this.time=s,this._mixer.dispatchEvent({type:"finished",action:this,direction:t<0?-1:1})}}else{if(-1===r&&(t>=0?(r=0,this._setEndings(!0,0===this.repetitions,n)):this._setEndings(0===this.repetitions,!0,n)),s>=e||s<0){let i=Math.floor(s/e);s-=e*i,r+=Math.abs(i);let a=this.repetitions-r;if(a<=0)this.clampWhenFinished?this.paused=!0:this.enabled=!1,s=t>0?e:0,this.time=s,this._mixer.dispatchEvent({type:"finished",action:this,direction:t>0?1:-1});else{if(1===a){let e=t<0;this._setEndings(e,!e,n)}else this._setEndings(!1,!1,n);this._loopCount=r,this.time=s,this._mixer.dispatchEvent({type:"loop",action:this,loopDelta:i})}}else this.time=s;if(n&&(1&r)==1)return e-s}return s}_setEndings(t,e,i){let s=this._interpolantSettings;i?(s.endingStart=eP,s.endingEnd=eP):(t?s.endingStart=this.zeroSlopeAtStart?eP:eE:s.endingStart=eO,e?s.endingEnd=this.zeroSlopeAtEnd?eP:eE:s.endingEnd=eO)}_scheduleFading(t,e,i){let s=this._mixer,r=s.time,n=this._weightInterpolant;null===n&&(n=s._lendControlInterpolant(),this._weightInterpolant=n);let a=n.parameterPositions,o=n.sampleValues;return a[0]=r,o[0]=e,a[1]=r+t,o[1]=i,this}}let lV=new Float32Array(1);class lL extends iI{constructor(t){super(),this._root=t,this._initMemoryManager(),this._accuIndex=0,this.time=0,this.timeScale=1}_bindAction(t,e){let i=t._localRoot||this._root,s=t._clip.tracks,r=s.length,n=t._propertyBindings,a=t._interpolants,o=i.uuid,h=this._bindingsByRootAndName,l=h[o];void 0===l&&(l={},h[o]=l);for(let t=0;t!==r;++t){let r=s[t],h=r.name,u=l[h];if(void 0!==u)++u.referenceCount,n[t]=u;else{if(void 0!==(u=n[t])){null===u._cacheIndex&&(++u.referenceCount,this._addInactiveBinding(u,o,h));continue}let s=e&&e._propertyBindings[t].binding.parsedPath;u=new lA(lO.create(i,h,s),r.ValueTypeName,r.getValueSize()),++u.referenceCount,this._addInactiveBinding(u,o,h),n[t]=u}a[t].resultBuffer=u.buffer}}_activateAction(t){if(!this._isActiveAction(t)){if(null===t._cacheIndex){let e=(t._localRoot||this._root).uuid,i=t._clip.uuid,s=this._actionsByClip[i];this._bindAction(t,s&&s.knownActions[0]),this._addInactiveAction(t,i,e)}let e=t._propertyBindings;for(let t=0,i=e.length;t!==i;++t){let i=e[t];0==i.useCount++&&(this._lendBinding(i),i.saveOriginalState())}this._lendAction(t)}}_deactivateAction(t){if(this._isActiveAction(t)){let e=t._propertyBindings;for(let t=0,i=e.length;t!==i;++t){let i=e[t];0==--i.useCount&&(i.restoreOriginalState(),this._takeBackBinding(i))}this._takeBackAction(t)}}_initMemoryManager(){this._actions=[],this._nActiveActions=0,this._actionsByClip={},this._bindings=[],this._nActiveBindings=0,this._bindingsByRootAndName={},this._controlInterpolants=[],this._nActiveControlInterpolants=0;let t=this;this.stats={actions:{get total(){return t._actions.length},get inUse(){return t._nActiveActions}},bindings:{get total(){return t._bindings.length},get inUse(){return t._nActiveBindings}},controlInterpolants:{get total(){return t._controlInterpolants.length},get inUse(){return t._nActiveControlInterpolants}}}}_isActiveAction(t){let e=t._cacheIndex;return null!==e&&e<this._nActiveActions}_addInactiveAction(t,e,i){let s=this._actions,r=this._actionsByClip,n=r[e];if(void 0===n)n={knownActions:[t],actionByRoot:{}},t._byClipCacheIndex=0,r[e]=n;else{let e=n.knownActions;t._byClipCacheIndex=e.length,e.push(t)}t._cacheIndex=s.length,s.push(t),n.actionByRoot[i]=t}_removeInactiveAction(t){let e=this._actions,i=e[e.length-1],s=t._cacheIndex;i._cacheIndex=s,e[s]=i,e.pop(),t._cacheIndex=null;let r=t._clip.uuid,n=this._actionsByClip,a=n[r],o=a.knownActions,h=o[o.length-1],l=t._byClipCacheIndex;h._byClipCacheIndex=l,o[l]=h,o.pop(),t._byClipCacheIndex=null;let u=a.actionByRoot,c=(t._localRoot||this._root).uuid;delete u[c],0===o.length&&delete n[r],this._removeInactiveBindingsForAction(t)}_removeInactiveBindingsForAction(t){let e=t._propertyBindings;for(let t=0,i=e.length;t!==i;++t){let i=e[t];0==--i.referenceCount&&this._removeInactiveBinding(i)}}_lendAction(t){let e=this._actions,i=t._cacheIndex,s=this._nActiveActions++,r=e[s];t._cacheIndex=s,e[s]=t,r._cacheIndex=i,e[i]=r}_takeBackAction(t){let e=this._actions,i=t._cacheIndex,s=--this._nActiveActions,r=e[s];t._cacheIndex=s,e[s]=t,r._cacheIndex=i,e[i]=r}_addInactiveBinding(t,e,i){let s=this._bindingsByRootAndName,r=this._bindings,n=s[e];void 0===n&&(n={},s[e]=n),n[i]=t,t._cacheIndex=r.length,r.push(t)}_removeInactiveBinding(t){let e=this._bindings,i=t.binding,s=i.rootNode.uuid,r=i.path,n=this._bindingsByRootAndName,a=n[s],o=e[e.length-1],h=t._cacheIndex;o._cacheIndex=h,e[h]=o,e.pop(),delete a[r],0===Object.keys(a).length&&delete n[s]}_lendBinding(t){let e=this._bindings,i=t._cacheIndex,s=this._nActiveBindings++,r=e[s];t._cacheIndex=s,e[s]=t,r._cacheIndex=i,e[i]=r}_takeBackBinding(t){let e=this._bindings,i=t._cacheIndex,s=--this._nActiveBindings,r=e[s];t._cacheIndex=s,e[s]=t,r._cacheIndex=i,e[i]=r}_lendControlInterpolant(){let t=this._controlInterpolants,e=this._nActiveControlInterpolants++,i=t[e];return void 0===i&&((i=new hy(new Float32Array(2),new Float32Array(2),1,lV)).__cacheIndex=e,t[e]=i),i}_takeBackControlInterpolant(t){let e=this._controlInterpolants,i=t.__cacheIndex,s=--this._nActiveControlInterpolants,r=e[s];t.__cacheIndex=s,e[s]=t,r.__cacheIndex=i,e[i]=r}clipAction(t,e,i){let s=e||this._root,r=s.uuid,n="string"==typeof t?hA.findByName(s,t):t,a=null!==n?n.uuid:t,o=this._actionsByClip[a],h=null;if(void 0===i&&(i=null!==n?n.blendMode:eN),void 0!==o){let t=o.actionByRoot[r];if(void 0!==t&&t.blendMode===i)return t;h=o.knownActions[0],null===n&&(n=h._clip)}if(null===n)return null;let l=new lF(this,n,e,i);return this._bindAction(l,h),this._addInactiveAction(l,a,r),l}existingAction(t,e){let i=e||this._root,s=i.uuid,r="string"==typeof t?hA.findByName(i,t):t,n=r?r.uuid:t,a=this._actionsByClip[n];return void 0!==a&&a.actionByRoot[s]||null}stopAllAction(){let t=this._actions,e=this._nActiveActions;for(let i=e-1;i>=0;--i)t[i].stop();return this}update(t){t*=this.timeScale;let e=this._actions,i=this._nActiveActions,s=this.time+=t,r=Math.sign(t),n=this._accuIndex^=1;for(let a=0;a!==i;++a)e[a]._update(s,t,r,n);let a=this._bindings,o=this._nActiveBindings;for(let t=0;t!==o;++t)a[t].apply(n);return this}setTime(t){this.time=0;for(let t=0;t<this._actions.length;t++)this._actions[t].time=0;return this.update(t)}getRoot(){return this._root}uncacheClip(t){let e=this._actions,i=t.uuid,s=this._actionsByClip,r=s[i];if(void 0!==r){let t=r.knownActions;for(let i=0,s=t.length;i!==s;++i){let s=t[i];this._deactivateAction(s);let r=s._cacheIndex,n=e[e.length-1];s._cacheIndex=null,s._byClipCacheIndex=null,n._cacheIndex=r,e[r]=n,e.pop(),this._removeInactiveBindingsForAction(s)}delete s[i]}}uncacheRoot(t){let e=t.uuid,i=this._actionsByClip;for(let t in i){let s=i[t].actionByRoot[e];void 0!==s&&(this._deactivateAction(s),this._removeInactiveAction(s))}let s=this._bindingsByRootAndName[e];if(void 0!==s)for(let t in s){let e=s[t];e.restoreOriginalState(),this._removeInactiveBinding(e)}}uncacheAction(t,e){let i=this.existingAction(t,e);null!==i&&(this._deactivateAction(i),this._removeInactiveAction(i))}}class lj extends se{constructor(t=1,e=1,i=1,s={}){super(t,e,s),this.isRenderTarget3D=!0,this.depth=i,this.texture=new sn(null,t,e,i),this.texture.isRenderTargetTexture=!0}}class lU extends se{constructor(t=1,e=1,i=1,s={}){super(t,e,s),this.isRenderTargetArray=!0,this.depth=i,this.texture=new ss(null,t,e,i),this.texture.isRenderTargetTexture=!0}}class lW{constructor(t){this.value=t}clone(){return new lW(void 0===this.value.clone?this.value:this.value.clone())}}let lD=0;class lH extends iI{constructor(){super(),this.isUniformsGroup=!0,Object.defineProperty(this,"id",{value:lD++}),this.name="",this.usage=ip,this.uniforms=[]}add(t){return this.uniforms.push(t),this}remove(t){let e=this.uniforms.indexOf(t);return -1!==e&&this.uniforms.splice(e,1),this}setName(t){return this.name=t,this}setUsage(t){return this.usage=t,this}dispose(){this.dispatchEvent({type:"dispose"})}copy(t){this.name=t.name,this.usage=t.usage;let e=t.uniforms;this.uniforms.length=0;for(let t=0,i=e.length;t<i;t++){let i=Array.isArray(e[t])?e[t]:[e[t]];for(let t=0;t<i.length;t++)this.uniforms.push(i[t].clone())}return this}clone(){return new this.constructor().copy(this)}}class lq extends nb{constructor(t,e,i=1){super(t,e),this.isInstancedInterleavedBuffer=!0,this.meshPerAttribute=i}copy(t){return super.copy(t),this.meshPerAttribute=t.meshPerAttribute,this}clone(t){let e=super.clone(t);return e.meshPerAttribute=this.meshPerAttribute,e}toJSON(t){let e=super.toJSON(t);return e.isInstancedInterleavedBuffer=!0,e.meshPerAttribute=this.meshPerAttribute,e}}class lJ{constructor(t,e,i,s,r){this.isGLBufferAttribute=!0,this.name="",this.buffer=t,this.type=e,this.itemSize=i,this.elementSize=s,this.count=r,this.version=0}set needsUpdate(t){!0===t&&this.version++}setBuffer(t){return this.buffer=t,this}setType(t,e){return this.type=t,this.elementSize=e,this}setItemSize(t){return this.itemSize=t,this}setCount(t){return this.count=t,this}}let lX=new sV;class lZ{constructor(t,e,i=0,s=1/0){this.ray=new sF(t,e),this.near=i,this.far=s,this.camera=null,this.layers=new sY,this.params={Mesh:{},Line:{threshold:1},LOD:{},Points:{threshold:1},Sprite:{}}}set(t,e){this.ray.set(t,e)}setFromCamera(t,e){e.isPerspectiveCamera?(this.ray.origin.setFromMatrixPosition(e.matrixWorld),this.ray.direction.set(t.x,t.y,.5).unproject(e).sub(this.ray.origin).normalize(),this.camera=e):e.isOrthographicCamera?(this.ray.origin.set(t.x,t.y,(e.near+e.far)/(e.near-e.far)).unproject(e),this.ray.direction.set(0,0,-1).transformDirection(e.matrixWorld),this.camera=e):console.error("THREE.Raycaster: Unsupported camera type: "+e.type)}setFromXRController(t){return lX.identity().extractRotation(t.matrixWorld),this.ray.origin.setFromMatrixPosition(t.matrixWorld),this.ray.direction.set(0,0,-1).applyMatrix4(lX),this}intersectObject(t,e=!0,i=[]){return lG(t,this,i,e),i.sort(lY),i}intersectObjects(t,e=!0,i=[]){for(let s=0,r=t.length;s<r;s++)lG(t[s],this,i,e);return i.sort(lY),i}}function lY(t,e){return t.distance-e.distance}function lG(t,e,i,s){let r=!0;if(t.layers.test(e.layers)&&!1===t.raycast(e,i)&&(r=!1),!0===r&&!0===s){let s=t.children;for(let t=0,r=s.length;t<r;t++)lG(s[t],e,i,!0)}}class l${constructor(t=1,e=0,i=0){this.radius=t,this.phi=e,this.theta=i}set(t,e,i){return this.radius=t,this.phi=e,this.theta=i,this}copy(t){return this.radius=t.radius,this.phi=t.phi,this.theta=t.theta,this}makeSafe(){return this.phi=iP(this.phi,1e-6,Math.PI-1e-6),this}setFromVector3(t){return this.setFromCartesianCoords(t.x,t.y,t.z)}setFromCartesianCoords(t,e,i){return this.radius=Math.sqrt(t*t+e*e+i*i),0===this.radius?(this.theta=0,this.phi=0):(this.theta=Math.atan2(t,i),this.phi=Math.acos(iP(e/this.radius,-1,1))),this}clone(){return new this.constructor().copy(this)}}class lQ{constructor(t=1,e=0,i=0){this.radius=t,this.theta=e,this.y=i}set(t,e,i){return this.radius=t,this.theta=e,this.y=i,this}copy(t){return this.radius=t.radius,this.theta=t.theta,this.y=t.y,this}setFromVector3(t){return this.setFromCartesianCoords(t.x,t.y,t.z)}setFromCartesianCoords(t,e,i){return this.radius=Math.sqrt(t*t+i*i),this.theta=Math.atan2(t,i),this.y=e,this}clone(){return new this.constructor().copy(this)}}class lK{constructor(t,e,i,s){lK.prototype.isMatrix2=!0,this.elements=[1,0,0,1],void 0!==t&&this.set(t,e,i,s)}identity(){return this.set(1,0,0,1),this}fromArray(t,e=0){for(let i=0;i<4;i++)this.elements[i]=t[i+e];return this}set(t,e,i,s){let r=this.elements;return r[0]=t,r[2]=e,r[1]=i,r[3]=s,this}}let l0=new ij;class l1{constructor(t=new ij(Infinity,Infinity),e=new ij(-1/0,-1/0)){this.isBox2=!0,this.min=t,this.max=e}set(t,e){return this.min.copy(t),this.max.copy(e),this}setFromPoints(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e++)this.expandByPoint(t[e]);return this}setFromCenterAndSize(t,e){let i=l0.copy(e).multiplyScalar(.5);return this.min.copy(t).sub(i),this.max.copy(t).add(i),this}clone(){return new this.constructor().copy(this)}copy(t){return this.min.copy(t.min),this.max.copy(t.max),this}makeEmpty(){return this.min.x=this.min.y=Infinity,this.max.x=this.max.y=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y}getCenter(t){return this.isEmpty()?t.set(0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(t){return this.isEmpty()?t.set(0,0):t.subVectors(this.max,this.min)}expandByPoint(t){return this.min.min(t),this.max.max(t),this}expandByVector(t){return this.min.sub(t),this.max.add(t),this}expandByScalar(t){return this.min.addScalar(-t),this.max.addScalar(t),this}containsPoint(t){return t.x>=this.min.x&&t.x<=this.max.x&&t.y>=this.min.y&&t.y<=this.max.y}containsBox(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y}getParameter(t,e){return e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y))}intersectsBox(t){return t.max.x>=this.min.x&&t.min.x<=this.max.x&&t.max.y>=this.min.y&&t.min.y<=this.max.y}clampPoint(t,e){return e.copy(t).clamp(this.min,this.max)}distanceToPoint(t){return this.clampPoint(t,l0).distanceTo(t)}intersect(t){return this.min.max(t.min),this.max.min(t.max),this.isEmpty()&&this.makeEmpty(),this}union(t){return this.min.min(t.min),this.max.max(t.max),this}translate(t){return this.min.add(t),this.max.add(t),this}equals(t){return t.min.equals(this.min)&&t.max.equals(this.max)}}let l2=new sh,l3=new sh;class l5{constructor(t=new sh,e=new sh){this.start=t,this.end=e}set(t,e){return this.start.copy(t),this.end.copy(e),this}copy(t){return this.start.copy(t.start),this.end.copy(t.end),this}getCenter(t){return t.addVectors(this.start,this.end).multiplyScalar(.5)}delta(t){return t.subVectors(this.end,this.start)}distanceSq(){return this.start.distanceToSquared(this.end)}distance(){return this.start.distanceTo(this.end)}at(t,e){return this.delta(e).multiplyScalar(t).add(this.start)}closestPointToPointParameter(t,e){l2.subVectors(t,this.start),l3.subVectors(this.end,this.start);let i=l3.dot(l3),s=l3.dot(l2)/i;return e&&(s=iP(s,0,1)),s}closestPointToPoint(t,e,i){let s=this.closestPointToPointParameter(t,e);return this.delta(i).multiplyScalar(s).add(this.start)}applyMatrix4(t){return this.start.applyMatrix4(t),this.end.applyMatrix4(t),this}equals(t){return t.start.equals(this.start)&&t.end.equals(this.end)}clone(){return new this.constructor().copy(this)}}let l4=new sh;class l6 extends re{constructor(t,e){super(),this.light=t,this.matrixAutoUpdate=!1,this.color=e,this.type="SpotLightHelper";let i=new rY,s=[0,0,0,0,0,1,0,0,0,1,0,1,0,0,0,-1,0,1,0,0,0,0,1,1,0,0,0,0,-1,1];for(let t=0,e=1;t<32;t++,e++){let i=t/32*Math.PI*2,r=e/32*Math.PI*2;s.push(Math.cos(i),Math.sin(i),1,Math.cos(r),Math.sin(r),1)}i.setAttribute("position",new rU(s,3));let r=new a_({fog:!1,toneMapped:!1});this.cone=new aN(i,r),this.add(this.cone),this.update()}dispose(){this.cone.geometry.dispose(),this.cone.material.dispose()}update(){this.light.updateWorldMatrix(!0,!1),this.light.target.updateWorldMatrix(!0,!1),this.parent?(this.parent.updateWorldMatrix(!0),this.matrix.copy(this.parent.matrixWorld).invert().multiply(this.light.matrixWorld)):this.matrix.copy(this.light.matrixWorld),this.matrixWorld.copy(this.light.matrixWorld);let t=this.light.distance?this.light.distance:1e3,e=t*Math.tan(this.light.angle);this.cone.scale.set(e,e,t),l4.setFromMatrixPosition(this.light.target.matrixWorld),this.cone.lookAt(l4),void 0!==this.color?this.cone.material.color.set(this.color):this.cone.material.color.copy(this.light.color)}}let l8=new sh,l9=new sV,l7=new sV;class ut extends aN{constructor(t){let e=function t(e){let i=[];!0===e.isBone&&i.push(e);for(let s=0;s<e.children.length;s++)i.push(...t(e.children[s]));return i}(t),i=new rY,s=[],r=[],n=new rv(0,0,1),a=new rv(0,1,0);for(let t=0;t<e.length;t++){let i=e[t];i.parent&&i.parent.isBone&&(s.push(0,0,0),s.push(0,0,0),r.push(n.r,n.g,n.b),r.push(a.r,a.g,a.b))}i.setAttribute("position",new rU(s,3)),i.setAttribute("color",new rU(r,3)),super(i,new a_({vertexColors:!0,depthTest:!1,depthWrite:!1,toneMapped:!1,transparent:!0})),this.isSkeletonHelper=!0,this.type="SkeletonHelper",this.root=t,this.bones=e,this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1}updateMatrixWorld(t){let e=this.bones,i=this.geometry,s=i.getAttribute("position");l7.copy(this.root.matrixWorld).invert();for(let t=0,i=0;t<e.length;t++){let r=e[t];r.parent&&r.parent.isBone&&(l9.multiplyMatrices(l7,r.matrixWorld),l8.setFromMatrixPosition(l9),s.setXYZ(i,l8.x,l8.y,l8.z),l9.multiplyMatrices(l7,r.parent.matrixWorld),l8.setFromMatrixPosition(l9),s.setXYZ(i+1,l8.x,l8.y,l8.z),i+=2)}i.getAttribute("position").needsUpdate=!0,super.updateMatrixWorld(t)}dispose(){this.geometry.dispose(),this.material.dispose()}}class ue extends r8{constructor(t,e,i){super(new o$(e,4,2),new r_({wireframe:!0,fog:!1,toneMapped:!1})),this.light=t,this.color=i,this.type="PointLightHelper",this.matrix=this.light.matrixWorld,this.matrixAutoUpdate=!1,this.update()}dispose(){this.geometry.dispose(),this.material.dispose()}update(){this.light.updateWorldMatrix(!0,!1),void 0!==this.color?this.material.color.set(this.color):this.material.color.copy(this.light.color)}}let ui=new sh,us=new rv,ur=new rv;class un extends re{constructor(t,e,i){super(),this.light=t,this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.color=i,this.type="HemisphereLightHelper";let s=new oX(e);s.rotateY(.5*Math.PI),this.material=new r_({wireframe:!0,fog:!1,toneMapped:!1}),void 0===this.color&&(this.material.vertexColors=!0);let r=new Float32Array(3*s.getAttribute("position").count);s.setAttribute("color",new rR(r,3)),this.add(new r8(s,this.material)),this.update()}dispose(){this.children[0].geometry.dispose(),this.children[0].material.dispose()}update(){let t=this.children[0];if(void 0!==this.color)this.material.color.set(this.color);else{let e=t.geometry.getAttribute("color");us.copy(this.light.color),ur.copy(this.light.groundColor);for(let t=0,i=e.count;t<i;t++){let s=t<i/2?us:ur;e.setXYZ(t,s.r,s.g,s.b)}e.needsUpdate=!0}this.light.updateWorldMatrix(!0,!1),t.lookAt(ui.setFromMatrixPosition(this.light.matrixWorld).negate())}}class ua extends aN{constructor(t=10,e=10,i=4473924,s=8947848){i=new rv(i),s=new rv(s);let r=e/2,n=t/e,a=t/2,o=[],h=[];for(let t=0,l=0,u=-a;t<=e;t++,u+=n){o.push(-a,0,u,a,0,u),o.push(u,0,-a,u,0,a);let e=t===r?i:s;e.toArray(h,l),l+=3,e.toArray(h,l),l+=3,e.toArray(h,l),l+=3,e.toArray(h,l),l+=3}let l=new rY;l.setAttribute("position",new rU(o,3)),l.setAttribute("color",new rU(h,3)),super(l,new a_({vertexColors:!0,toneMapped:!1})),this.type="GridHelper"}dispose(){this.geometry.dispose(),this.material.dispose()}}class uo extends aN{constructor(t=10,e=16,i=8,s=64,r=4473924,n=8947848){r=new rv(r),n=new rv(n);let a=[],o=[];if(e>1)for(let i=0;i<e;i++){let s=i/e*(2*Math.PI),h=Math.sin(s)*t,l=Math.cos(s)*t;a.push(0,0,0),a.push(h,0,l);let u=1&i?r:n;o.push(u.r,u.g,u.b),o.push(u.r,u.g,u.b)}for(let e=0;e<i;e++){let h=1&e?r:n,l=t-t/i*e;for(let t=0;t<s;t++){let e=t/s*(2*Math.PI),i=Math.sin(e)*l,r=Math.cos(e)*l;a.push(i,0,r),o.push(h.r,h.g,h.b),i=Math.sin(e=(t+1)/s*(2*Math.PI))*l,r=Math.cos(e)*l,a.push(i,0,r),o.push(h.r,h.g,h.b)}}let h=new rY;h.setAttribute("position",new rU(a,3)),h.setAttribute("color",new rU(o,3)),super(h,new a_({vertexColors:!0,toneMapped:!1})),this.type="PolarGridHelper"}dispose(){this.geometry.dispose(),this.material.dispose()}}let uh=new sh,ul=new sh,uu=new sh;class uc extends re{constructor(t,e,i){super(),this.light=t,this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.color=i,this.type="DirectionalLightHelper",void 0===e&&(e=1);let s=new rY;s.setAttribute("position",new rU([-e,e,0,e,e,0,e,-e,0,-e,-e,0,-e,e,0],3));let r=new a_({fog:!1,toneMapped:!1});this.lightPlane=new aR(s,r),this.add(this.lightPlane),(s=new rY).setAttribute("position",new rU([0,0,0,0,0,1],3)),this.targetLine=new aR(s,r),this.add(this.targetLine),this.update()}dispose(){this.lightPlane.geometry.dispose(),this.lightPlane.material.dispose(),this.targetLine.geometry.dispose(),this.targetLine.material.dispose()}update(){this.light.updateWorldMatrix(!0,!1),this.light.target.updateWorldMatrix(!0,!1),uh.setFromMatrixPosition(this.light.matrixWorld),ul.setFromMatrixPosition(this.light.target.matrixWorld),uu.subVectors(ul,uh),this.lightPlane.lookAt(ul),void 0!==this.color?(this.lightPlane.material.color.set(this.color),this.targetLine.material.color.set(this.color)):(this.lightPlane.material.color.copy(this.light.color),this.targetLine.material.color.copy(this.light.color)),this.targetLine.lookAt(ul),this.targetLine.scale.z=uu.length()}}let ud=new sh,up=new nn;class um extends aN{constructor(t){let e=new rY,i=new a_({color:0xffffff,vertexColors:!0,toneMapped:!1}),s=[],r=[],n={};function a(t,e){o(t),o(e)}function o(t){s.push(0,0,0),r.push(0,0,0),void 0===n[t]&&(n[t]=[]),n[t].push(s.length/3-1)}a("n1","n2"),a("n2","n4"),a("n4","n3"),a("n3","n1"),a("f1","f2"),a("f2","f4"),a("f4","f3"),a("f3","f1"),a("n1","f1"),a("n2","f2"),a("n3","f3"),a("n4","f4"),a("p","n1"),a("p","n2"),a("p","n3"),a("p","n4"),a("u1","u2"),a("u2","u3"),a("u3","u1"),a("c","t"),a("p","c"),a("cn1","cn2"),a("cn3","cn4"),a("cf1","cf2"),a("cf3","cf4"),e.setAttribute("position",new rU(s,3)),e.setAttribute("color",new rU(r,3)),super(e,i),this.type="CameraHelper",this.camera=t,this.camera.updateProjectionMatrix&&this.camera.updateProjectionMatrix(),this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.pointMap=n,this.update();let h=new rv(0xffaa00),l=new rv(0xff0000),u=new rv(43775),c=new rv(0xffffff),d=new rv(3355443);this.setColors(h,l,u,c,d)}setColors(t,e,i,s,r){let n=this.geometry.getAttribute("color");n.setXYZ(0,t.r,t.g,t.b),n.setXYZ(1,t.r,t.g,t.b),n.setXYZ(2,t.r,t.g,t.b),n.setXYZ(3,t.r,t.g,t.b),n.setXYZ(4,t.r,t.g,t.b),n.setXYZ(5,t.r,t.g,t.b),n.setXYZ(6,t.r,t.g,t.b),n.setXYZ(7,t.r,t.g,t.b),n.setXYZ(8,t.r,t.g,t.b),n.setXYZ(9,t.r,t.g,t.b),n.setXYZ(10,t.r,t.g,t.b),n.setXYZ(11,t.r,t.g,t.b),n.setXYZ(12,t.r,t.g,t.b),n.setXYZ(13,t.r,t.g,t.b),n.setXYZ(14,t.r,t.g,t.b),n.setXYZ(15,t.r,t.g,t.b),n.setXYZ(16,t.r,t.g,t.b),n.setXYZ(17,t.r,t.g,t.b),n.setXYZ(18,t.r,t.g,t.b),n.setXYZ(19,t.r,t.g,t.b),n.setXYZ(20,t.r,t.g,t.b),n.setXYZ(21,t.r,t.g,t.b),n.setXYZ(22,t.r,t.g,t.b),n.setXYZ(23,t.r,t.g,t.b),n.setXYZ(24,e.r,e.g,e.b),n.setXYZ(25,e.r,e.g,e.b),n.setXYZ(26,e.r,e.g,e.b),n.setXYZ(27,e.r,e.g,e.b),n.setXYZ(28,e.r,e.g,e.b),n.setXYZ(29,e.r,e.g,e.b),n.setXYZ(30,e.r,e.g,e.b),n.setXYZ(31,e.r,e.g,e.b),n.setXYZ(32,i.r,i.g,i.b),n.setXYZ(33,i.r,i.g,i.b),n.setXYZ(34,i.r,i.g,i.b),n.setXYZ(35,i.r,i.g,i.b),n.setXYZ(36,i.r,i.g,i.b),n.setXYZ(37,i.r,i.g,i.b),n.setXYZ(38,s.r,s.g,s.b),n.setXYZ(39,s.r,s.g,s.b),n.setXYZ(40,r.r,r.g,r.b),n.setXYZ(41,r.r,r.g,r.b),n.setXYZ(42,r.r,r.g,r.b),n.setXYZ(43,r.r,r.g,r.b),n.setXYZ(44,r.r,r.g,r.b),n.setXYZ(45,r.r,r.g,r.b),n.setXYZ(46,r.r,r.g,r.b),n.setXYZ(47,r.r,r.g,r.b),n.setXYZ(48,r.r,r.g,r.b),n.setXYZ(49,r.r,r.g,r.b),n.needsUpdate=!0}update(){let t=this.geometry,e=this.pointMap;up.projectionMatrixInverse.copy(this.camera.projectionMatrixInverse);let i=this.camera.coordinateSystem===iA?-1:0;uy("c",e,t,up,0,0,i),uy("t",e,t,up,0,0,1),uy("n1",e,t,up,-1,-1,i),uy("n2",e,t,up,1,-1,i),uy("n3",e,t,up,-1,1,i),uy("n4",e,t,up,1,1,i),uy("f1",e,t,up,-1,-1,1),uy("f2",e,t,up,1,-1,1),uy("f3",e,t,up,-1,1,1),uy("f4",e,t,up,1,1,1),uy("u1",e,t,up,.7,1.1,i),uy("u2",e,t,up,-.7,1.1,i),uy("u3",e,t,up,0,2,i),uy("cf1",e,t,up,-1,0,1),uy("cf2",e,t,up,1,0,1),uy("cf3",e,t,up,0,-1,1),uy("cf4",e,t,up,0,1,1),uy("cn1",e,t,up,-1,0,i),uy("cn2",e,t,up,1,0,i),uy("cn3",e,t,up,0,-1,i),uy("cn4",e,t,up,0,1,i),t.getAttribute("position").needsUpdate=!0}dispose(){this.geometry.dispose(),this.material.dispose()}}function uy(t,e,i,s,r,n,a){ud.set(r,n,a).unproject(s);let o=e[t];if(void 0!==o){let t=i.getAttribute("position");for(let e=0,i=o.length;e<i;e++)t.setXYZ(o[e],ud.x,ud.y,ud.z)}}let uf=new sc;class ug extends aN{constructor(t,e=0xffff00){let i=new Uint16Array([0,1,1,2,2,3,3,0,4,5,5,6,6,7,7,4,0,4,1,5,2,6,3,7]),s=new Float32Array(24),r=new rY;r.setIndex(new rR(i,1)),r.setAttribute("position",new rR(s,3)),super(r,new a_({color:e,toneMapped:!1})),this.object=t,this.type="BoxHelper",this.matrixAutoUpdate=!1,this.update()}update(){if(void 0!==this.object&&uf.setFromObject(this.object),uf.isEmpty())return;let t=uf.min,e=uf.max,i=this.geometry.attributes.position,s=i.array;s[0]=e.x,s[1]=e.y,s[2]=e.z,s[3]=t.x,s[4]=e.y,s[5]=e.z,s[6]=t.x,s[7]=t.y,s[8]=e.z,s[9]=e.x,s[10]=t.y,s[11]=e.z,s[12]=e.x,s[13]=e.y,s[14]=t.z,s[15]=t.x,s[16]=e.y,s[17]=t.z,s[18]=t.x,s[19]=t.y,s[20]=t.z,s[21]=e.x,s[22]=t.y,s[23]=t.z,i.needsUpdate=!0,this.geometry.computeBoundingSphere()}setFromObject(t){return this.object=t,this.update(),this}copy(t,e){return super.copy(t,e),this.object=t.object,this}dispose(){this.geometry.dispose(),this.material.dispose()}}class ux extends aN{constructor(t,e=0xffff00){let i=new Uint16Array([0,1,1,2,2,3,3,0,4,5,5,6,6,7,7,4,0,4,1,5,2,6,3,7]),s=new rY;s.setIndex(new rR(i,1)),s.setAttribute("position",new rU([1,1,1,-1,1,1,-1,-1,1,1,-1,1,1,1,-1,-1,1,-1,-1,-1,-1,1,-1,-1],3)),super(s,new a_({color:e,toneMapped:!1})),this.box=t,this.type="Box3Helper",this.geometry.computeBoundingSphere()}updateMatrixWorld(t){let e=this.box;e.isEmpty()||(e.getCenter(this.position),e.getSize(this.scale),this.scale.multiplyScalar(.5),super.updateMatrixWorld(t))}dispose(){this.geometry.dispose(),this.material.dispose()}}class ub extends aR{constructor(t,e=1,i=0xffff00){let s=new rY;s.setAttribute("position",new rU([1,-1,0,-1,1,0,-1,-1,0,1,1,0,-1,1,0,-1,-1,0,1,-1,0,1,1,0],3)),s.computeBoundingSphere(),super(s,new a_({color:i,toneMapped:!1})),this.type="PlaneHelper",this.plane=t,this.size=e;let r=new rY;r.setAttribute("position",new rU([1,1,0,-1,1,0,-1,-1,0,1,1,0,-1,-1,0,1,-1,0],3)),r.computeBoundingSphere(),this.add(new r8(r,new r_({color:i,opacity:.2,transparent:!0,depthWrite:!1,toneMapped:!1})))}updateMatrixWorld(t){this.position.set(0,0,0),this.scale.set(.5*this.size,.5*this.size,1),this.lookAt(this.plane.normal),this.translateZ(-this.plane.constant),super.updateMatrixWorld(t)}dispose(){this.geometry.dispose(),this.material.dispose(),this.children[0].geometry.dispose(),this.children[0].material.dispose()}}let uv=new sh;class uw extends re{constructor(t=new sh(0,0,1),e=new sh(0,0,0),i=1,s=0xffff00,r=.2*i,n=.2*r){super(),this.type="ArrowHelper",void 0===a&&((a=new rY).setAttribute("position",new rU([0,0,0,0,1,0],3)),(o=new om(0,.5,1,5,1)).translate(0,-.5,0)),this.position.copy(e),this.line=new aR(a,new a_({color:s,toneMapped:!1})),this.line.matrixAutoUpdate=!1,this.add(this.line),this.cone=new r8(o,new r_({color:s,toneMapped:!1})),this.cone.matrixAutoUpdate=!1,this.add(this.cone),this.setDirection(t),this.setLength(i,r,n)}setDirection(t){if(t.y>.99999)this.quaternion.set(0,0,0,1);else if(t.y<-.99999)this.quaternion.set(1,0,0,0);else{uv.set(t.z,0,-t.x).normalize();let e=Math.acos(t.y);this.quaternion.setFromAxisAngle(uv,e)}}setLength(t,e=.2*t,i=.2*e){this.line.scale.set(1,Math.max(1e-4,t-e),1),this.line.updateMatrix(),this.cone.scale.set(i,e,i),this.cone.position.y=t,this.cone.updateMatrix()}setColor(t){this.line.material.color.set(t),this.cone.material.color.set(t)}copy(t){return super.copy(t,!1),this.line.copy(t.line),this.cone.copy(t.cone),this}dispose(){this.line.geometry.dispose(),this.line.material.dispose(),this.cone.geometry.dispose(),this.cone.material.dispose()}}class uM extends aN{constructor(t=1){let e=new rY;e.setAttribute("position",new rU([0,0,0,t,0,0,0,0,0,0,t,0,0,0,0,0,0,t],3)),e.setAttribute("color",new rU([1,0,0,1,.6,0,0,1,0,.6,1,0,0,0,1,0,.6,1],3)),super(e,new a_({vertexColors:!0,toneMapped:!1})),this.type="AxesHelper"}setColors(t,e,i){let s=new rv,r=this.geometry.attributes.color.array;return s.set(t),s.toArray(r,0),s.toArray(r,3),s.set(e),s.toArray(r,6),s.toArray(r,9),s.set(i),s.toArray(r,12),s.toArray(r,15),this.geometry.attributes.color.needsUpdate=!0,this}dispose(){this.geometry.dispose(),this.material.dispose()}}class uS{constructor(){this.type="ShapePath",this.color=new rv,this.subPaths=[],this.currentPath=null}moveTo(t,e){return this.currentPath=new ou,this.subPaths.push(this.currentPath),this.currentPath.moveTo(t,e),this}lineTo(t,e){return this.currentPath.lineTo(t,e),this}quadraticCurveTo(t,e,i,s){return this.currentPath.quadraticCurveTo(t,e,i,s),this}bezierCurveTo(t,e,i,s,r,n){return this.currentPath.bezierCurveTo(t,e,i,s,r,n),this}splineThru(t){return this.currentPath.splineThru(t),this}toShapes(t){let e,i,s,r,n,a=oU.isClockWise,o=this.subPaths;if(0===o.length)return[];let h=[];if(1===o.length)return i=o[0],(s=new oS).curves=i.curves,h.push(s),h;let l=!a(o[0].getPoints());l=t?!l:l;let u=[],c=[],d=[],p=0;c[0]=void 0,d[p]=[];for(let s=0,n=o.length;s<n;s++)e=a(r=(i=o[s]).getPoints()),(e=t?!e:e)?(!l&&c[p]&&p++,c[p]={s:new oS,p:r},c[p].s.curves=i.curves,l&&p++,d[p]=[]):d[p].push({h:i,p:r[0]});if(!c[0])return function(t){let e=[];for(let i=0,s=t.length;i<s;i++){let s=t[i],r=new oS;r.curves=s.curves,e.push(r)}return e}(o);if(c.length>1){let t=!1,e=0;for(let t=0,e=c.length;t<e;t++)u[t]=[];for(let i=0,s=c.length;i<s;i++){let s=d[i];for(let r=0;r<s.length;r++){let n=s[r],a=!0;for(let s=0;s<c.length;s++)(function(t,e){let i=e.length,s=!1;for(let r=i-1,n=0;n<i;r=n++){let i=e[r],a=e[n],o=a.x-i.x,h=a.y-i.y;if(Math.abs(h)>Number.EPSILON){if(h<0&&(i=e[n],o=-o,a=e[r],h=-h),t.y<i.y||t.y>a.y)continue;if(t.y===i.y){if(t.x===i.x)return!0}else{let e=h*(t.x-i.x)-o*(t.y-i.y);if(0===e)return!0;if(e<0)continue;s=!s}}else{if(t.y!==i.y)continue;if(a.x<=t.x&&t.x<=i.x||i.x<=t.x&&t.x<=a.x)return!0}}return s})(n.p,c[s].p)&&(i!==s&&e++,a?(a=!1,u[s].push(n)):t=!0);a&&u[i].push(n)}}e>0&&!1===t&&(d=u)}for(let t=0,e=c.length;t<e;t++){s=c[t].s,h.push(s),n=d[t];for(let t=0,e=n.length;t<e;t++)s.holes.push(n[t].h)}return h}}class u_ extends iI{constructor(t,e=null){super(),this.object=t,this.domElement=e,this.enabled=!0,this.state=-1,this.keys={},this.mouseButtons={LEFT:null,MIDDLE:null,RIGHT:null},this.touches={ONE:null,TWO:null}}connect(t){if(void 0===t)return void console.warn("THREE.Controls: connect() now requires an element.");null!==this.domElement&&this.disconnect(),this.domElement=t}disconnect(){}dispose(){}update(){}}function uA(t,e,i,s){let r=function(t){switch(t){case tP:case tO:return{byteLength:1,components:1};case tF:case tN:case tU:return{byteLength:2,components:1};case tW:case tD:return{byteLength:2,components:4};case tL:case tV:case tj:return{byteLength:4,components:1};case tq:return{byteLength:4,components:3}}throw Error(`Unknown texture type ${t}.`)}(s);switch(i){case tJ:case tY:return t*e;case tG:return t*e*2;case tK:case t0:return t*e/r.components*r.byteLength;case t1:case t2:return t*e*2/r.components*r.byteLength;case tX:return t*e*3/r.components*r.byteLength;case tZ:case t5:return t*e*4/r.components*r.byteLength;case t4:case t6:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*8;case t8:case t9:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*16;case et:case ei:return Math.max(t,16)*Math.max(e,8)/4;case t7:case ee:return Math.max(t,8)*Math.max(e,8)/2;case es:case er:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*8;case en:case ea:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*16;case eo:return Math.floor((t+4)/5)*Math.floor((e+3)/4)*16;case eh:return Math.floor((t+4)/5)*Math.floor((e+4)/5)*16;case el:return Math.floor((t+5)/6)*Math.floor((e+4)/5)*16;case eu:return Math.floor((t+5)/6)*Math.floor((e+5)/6)*16;case ec:return Math.floor((t+7)/8)*Math.floor((e+4)/5)*16;case ed:return Math.floor((t+7)/8)*Math.floor((e+5)/6)*16;case ep:return Math.floor((t+7)/8)*Math.floor((e+7)/8)*16;case em:return Math.floor((t+9)/10)*Math.floor((e+4)/5)*16;case ey:return Math.floor((t+9)/10)*Math.floor((e+5)/6)*16;case ef:return Math.floor((t+9)/10)*Math.floor((e+7)/8)*16;case eg:return Math.floor((t+9)/10)*Math.floor((e+9)/10)*16;case ex:return Math.floor((t+11)/12)*Math.floor((e+9)/10)*16;case eb:return Math.floor((t+11)/12)*Math.floor((e+11)/12)*16;case ev:case ew:case eM:return Math.ceil(t/4)*Math.ceil(e/4)*16;case eS:case e_:return Math.ceil(t/4)*Math.ceil(e/4)*8;case eA:case eT:return Math.ceil(t/4)*Math.ceil(e/4)*16}throw Error(`Unable to determine texture byte length for ${i} format.`)}class uT{static contain(t,e){let i=t.image&&t.image.width?t.image.width/t.image.height:1;return i>e?(t.repeat.x=1,t.repeat.y=i/e,t.offset.x=0,t.offset.y=(1-t.repeat.y)/2):(t.repeat.x=e/i,t.repeat.y=1,t.offset.x=(1-t.repeat.x)/2,t.offset.y=0),t}static cover(t,e){let i=t.image&&t.image.width?t.image.width/t.image.height:1;return i>e?(t.repeat.x=e/i,t.repeat.y=1,t.offset.x=(1-t.repeat.x)/2,t.offset.y=0):(t.repeat.x=1,t.repeat.y=i/e,t.offset.x=0,t.offset.y=(1-t.repeat.y)/2),t}static fill(t){return t.repeat.x=1,t.repeat.y=1,t.offset.x=0,t.offset.y=0,t}static getByteLength(t,e,i,s){return uA(t,e,i,s)}}"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("register",{detail:{revision:h}})),"undefined"!=typeof window&&(window.__THREE__?console.warn("WARNING: Multiple instances of Three.js being imported."):window.__THREE__=h)}}]);