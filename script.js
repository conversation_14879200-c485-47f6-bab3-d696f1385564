// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    
    if (mobileToggle) {
        mobileToggle.addEventListener('click', function() {
            navLinks.classList.toggle('active');
            mobileToggle.classList.toggle('active');
        });
    }
    
    // Smooth scrolling for navigation links
    const navLinksElements = document.querySelectorAll('.nav-link');
    navLinksElements.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
    
    // Add floating animation to shapes
    animateFloatingShapes();
    
    // Add parallax effect to creatures
    addParallaxEffect();
    
    // Add button hover effects
    addButtonEffects();
    
    // Add scroll animations
    addScrollAnimations();
    
    // Add creature interactions
    addCreatureInteractions();
    
    // Add dynamic background animations
    addBackgroundAnimations();
});

function animateFloatingShapes() {
    const shapes = document.querySelectorAll('.floating-shapes .shape');
    
    shapes.forEach((shape, index) => {
        // Add random floating animation
        setInterval(() => {
            const randomX = Math.random() * 20 - 10;
            const randomY = Math.random() * 20 - 10;
            const currentTransform = shape.style.transform || '';
            
            shape.style.transform = `${currentTransform} translate(${randomX}px, ${randomY}px)`;
            
            setTimeout(() => {
                shape.style.transform = currentTransform;
            }, 2000);
        }, 3000 + index * 1000);
    });
}

function addParallaxEffect() {
    const creatures = document.querySelectorAll('.hero-creature, .about-creature, .cta-creature');
    
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallax = scrolled * 0.1;
        
        creatures.forEach((creature, index) => {
            const direction = index % 2 === 0 ? 1 : -1;
            creature.style.transform = `translateY(${parallax * direction}px)`;
        });
    });
}

function addButtonEffects() {
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
        
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.service-card, .step, .pricing-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

function addCreatureInteractions() {
    // Hero creature eye following
    const heroCat = document.querySelector('.hero-creature');
    const eyes = document.querySelectorAll('.creature-pupil');
    
    if (heroCat && eyes.length > 0) {
        document.addEventListener('mousemove', (e) => {
            const rect = heroCat.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            const deltaX = e.clientX - centerX;
            const deltaY = e.clientY - centerY;
            const angle = Math.atan2(deltaY, deltaX);
            
            eyes.forEach((eye, index) => {
                const moveX = Math.cos(angle) * 3;
                const moveY = Math.sin(angle) * 3;
                eye.style.transform = `translate(${moveX}px, ${moveY}px)`;
            });
        });
    }
    
    // Creature breathing effect
    const creatureBodies = document.querySelectorAll('.creature-body, .cta-creature-body');
    creatureBodies.forEach(body => {
        let scale = 1;
        let growing = true;
        
        setInterval(() => {
            if (growing) {
                scale += 0.001;
                if (scale >= 1.02) growing = false;
            } else {
                scale -= 0.001;
                if (scale <= 0.98) growing = true;
            }
            body.style.transform = `scale(${scale})`;
        }, 50);
    });
    
    // Spring animation for about section
    const springPath = document.querySelector('.spring-path');
    if (springPath) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    springPath.style.animation = 'drawSpring 2s ease-out forwards, springBounce 3s ease-in-out infinite 2s';
                }
            });
        }, { threshold: 0.5 });
        
        observer.observe(springPath);
    }
}

function addBackgroundAnimations() {
    // Floating stars animation
    const stars = document.querySelectorAll('.floating-stars path');
    stars.forEach((star, index) => {
        setInterval(() => {
            const randomRotation = Math.random() * 360;
            star.style.transform = `rotate(${randomRotation}deg)`;
        }, 2000 + index * 500);
    });
    
    // Service icon hover effects
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        const icon = card.querySelector('.service-icon svg');
        
        card.addEventListener('mouseenter', () => {
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
                icon.style.transition = 'transform 0.3s ease';
            }
        });
        
        card.addEventListener('mouseleave', () => {
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });
    
    // Pricing card pulse effect
    const pricingCards = document.querySelectorAll('.pricing-card');
    pricingCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.animation = 'pulse 1s ease-in-out infinite';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.animation = '';
        });
    });
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .nav-links.active {
        display: flex;
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background: white;
        flex-direction: column;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        z-index: 99;
    }
    
    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    .animate-in {
        animation: slideInUp 0.6s ease forwards;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.02);
        }
    }
    
    .service-card, .step, .pricing-card {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }
    
    .service-card.animate-in, .step.animate-in, .pricing-card.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    /* Stagger animation for grid items */
    .service-card:nth-child(1) { transition-delay: 0.1s; }
    .service-card:nth-child(2) { transition-delay: 0.2s; }
    .service-card:nth-child(3) { transition-delay: 0.3s; }
    .service-card:nth-child(4) { transition-delay: 0.4s; }
    .service-card:nth-child(5) { transition-delay: 0.5s; }
    .service-card:nth-child(6) { transition-delay: 0.6s; }
    
    .step:nth-child(1) { transition-delay: 0.1s; }
    .step:nth-child(2) { transition-delay: 0.3s; }
    .step:nth-child(3) { transition-delay: 0.5s; }
`;

document.head.appendChild(style); 