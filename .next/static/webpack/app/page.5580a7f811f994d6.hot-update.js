"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ProcessScroll.tsx":
/*!**************************************!*\
  !*** ./components/ProcessScroll.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useScrollAnimation */ \"(app-pages-browser)/./hooks/useScrollAnimation.ts\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst processSteps = [\n    {\n        number: \"01\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9couverte & Strat\\xe9gie\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        number: \"02\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Design & Prototypage\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        number: \"03\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"D\\xe9veloppement Agile\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        number: \"04\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Lancement & Optimisation\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nconst ProcessScroll = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    const titleAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"scroll-section relative flex items-center justify-center bg-bg-primary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: titleAnimation.ref,\n                        className: \"scroll-fade-in \".concat(titleAnimation.isVisible ? \"visible\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary\",\n                                children: [\n                                    \"Notre\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Processus\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed\",\n                                children: \"Une m\\xe9thodologie \\xe9prouv\\xe9e qui transforme vos id\\xe9es en solutions digitales performantes, \\xe9tape par \\xe9tape.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            processSteps.map(_s1((step, index)=>{\n                _s1();\n                const Icon = step.icon;\n                const visualAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.2);\n                const contentAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.3);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"scroll-section relative flex items-center justify-center bg-bg-secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: visualAnimation.ref,\n                                    className: \"flex justify-center lg:justify-start order-2 lg:order-1 scroll-slide-left \".concat(visualAnimation.isVisible ? \"visible\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl transition-all duration-500 group-hover:scale-110 group-hover:shadow-2xl group-hover:shadow-primary-green/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-display text-4xl font-bold text-white transition-all duration-300 group-hover:scale-110\",\n                                                    children: step.number\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light transition-all duration-300 group-hover:scale-110 group-hover:rotate-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-8 h-8 text-primary-green\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-4 -left-4 w-6 h-6 bg-accent-purple rounded-full opacity-20 animate-float-slow\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-8 left-8 w-4 h-4 bg-primary-green rounded-full opacity-30 animate-float-medium\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: contentAnimation.ref,\n                                    className: \"text-center lg:text-left order-1 lg:order-2 scroll-slide-right \".concat(contentAnimation.isVisible ? \"visible\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary\",\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined)\n                }, index, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, undefined);\n            }, \"026KWJnxPa7UGC7fL9w8+taCPqk=\", false, function() {\n                return [\n                    _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation,\n                    _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation\n                ];\n            }))\n        ]\n    }, void 0, true);\n};\n_s(ProcessScroll, \"vfFimljMtrWcNywNf5H9nqed+mk=\", false, function() {\n    return [\n        _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation\n    ];\n});\n_c = ProcessScroll;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProcessScroll);\nvar _c;\n$RefreshReg$(_c, \"ProcessScroll\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ProcessScroll.tsx\n"));

/***/ })

});