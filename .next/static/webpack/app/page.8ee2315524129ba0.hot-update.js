"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./components/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst services = [\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9veloppement Web\",\n        description: \"Applications web modernes et performantes. React, Vue.js, Node.js - nous ma\\xeetrisons les technologies de pointe pour cr\\xe9er des exp\\xe9riences utilisateur exceptionnelles.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Applications Mobile\",\n        description: \"Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie \\xe0 vos id\\xe9es sur tous les \\xe9crans.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Design & UX/UI\",\n        description: \"Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous cr\\xe9ons des interfaces intuitives.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Branding Digital\",\n        description: \"Identit\\xe9s visuelles m\\xe9morables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Maintenance & Support\",\n        description: \"Accompagnement technique continu pour faire \\xe9voluer vos projets. Monitoring, mises \\xe0 jour, optimisations - nous veillons sur vos applications.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Consulting Technique\",\n        description: \"Expertise strat\\xe9gique pour orienter vos d\\xe9cisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions.\"\n    }\n];\nconst processes = [\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"D\\xe9couverte\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Conception\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"D\\xe9veloppement\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: \"Lancement\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\n// SVG Circle Component (exact copy from morningside.ai)\nconst CircleElement = (param)=>{\n    let { icon: Icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full flex justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 494 494\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"w-[35vw] h-[50vh] lg:w-[35vw] lg:h-[28vw]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    filter: \"url(#filter0_i_182_70)\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M494 247C494 110.586 383.414 0 247 0C110.586 0 0 110.586 0 247C0 383.414 110.586 494 247 494C383.414 494 494 383.414 494 247Z\",\n                        fill: \"url(#paint0_linear_182_70)\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M492.75 247C492.75 111.276 382.724 1.25 247 1.25C111.276 1.25 1.25 111.276 1.25 247C1.25 382.724 111.276 492.75 247 492.75C382.724 492.75 492.75 382.724 492.75 247Z\",\n                    stroke: \"url(#paint1_linear_182_70)\",\n                    strokeWidth: \"2.5\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    filter: \"url(#filter1_i_182_70)\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M382 247C382 171.889 321.111 111 246 111C170.889 111 110 171.889 110 247C110 322.111 170.889 383 246 383C321.111 383 382 322.111 382 247Z\",\n                        fill: \"url(#paint2_linear_182_70)\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M380.75 247C380.75 172.58 320.42 112.25 246 112.25C171.58 112.25 111.25 172.58 111.25 247C111.25 321.42 171.58 381.75 246 381.75C320.42 381.75 380.75 321.42 380.75 247Z\",\n                    stroke: \"url(#paint3_linear_182_70)\",\n                    strokeWidth: \"2.5\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M420.229 422.127L247.102 249\",\n                    stroke: \"#DCEAE3\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    opacity: \"0.5\",\n                    d: \"M350.28 468.845L247.258 249.092\",\n                    stroke: \"#DCEAE3\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    opacity: \"0.1\",\n                    d: \"M220.24 491.074L246.836 249.258\",\n                    stroke: \"#DCEAE3\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"foreignObject\", {\n                    x: \"222\",\n                    y: \"222\",\n                    width: \"50\",\n                    height: \"50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"w-8 h-8 text-primary-green\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"filter\", {\n                            id: \"filter0_i_182_70\",\n                            x: \"0\",\n                            y: \"-24.0881\",\n                            width: \"494\",\n                            height: \"518.088\",\n                            filterUnits: \"userSpaceOnUse\",\n                            colorInterpolationFilters: \"sRGB\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feFlood\", {\n                                    floodOpacity: \"0\",\n                                    result: \"BackgroundImageFix\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feBlend\", {\n                                    mode: \"normal\",\n                                    in: \"SourceGraphic\",\n                                    in2: \"BackgroundImageFix\",\n                                    result: \"shape\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feColorMatrix\", {\n                                    in: \"SourceAlpha\",\n                                    type: \"matrix\",\n                                    values: \"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\",\n                                    result: \"hardAlpha\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feOffset\", {\n                                    dy: \"-24.0881\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feGaussianBlur\", {\n                                    stdDeviation: \"18.0661\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feComposite\", {\n                                    in2: \"hardAlpha\",\n                                    operator: \"arithmetic\",\n                                    k2: \"-1\",\n                                    k3: \"1\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feColorMatrix\", {\n                                    type: \"matrix\",\n                                    values: \"0 0 0 0 0.536175 0 0 0 0 0.741662 0 0 0 0 0.638918 0 0 0 0.7 0\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feBlend\", {\n                                    mode: \"normal\",\n                                    in2: \"shape\",\n                                    result: \"effect1_innerShadow_182_70\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"filter\", {\n                            id: \"filter1_i_182_70\",\n                            x: \"110\",\n                            y: \"86.9119\",\n                            width: \"272\",\n                            height: \"296.088\",\n                            filterUnits: \"userSpaceOnUse\",\n                            colorInterpolationFilters: \"sRGB\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feFlood\", {\n                                    floodOpacity: \"0\",\n                                    result: \"BackgroundImageFix\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feBlend\", {\n                                    mode: \"normal\",\n                                    in: \"SourceGraphic\",\n                                    in2: \"BackgroundImageFix\",\n                                    result: \"shape\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feColorMatrix\", {\n                                    in: \"SourceAlpha\",\n                                    type: \"matrix\",\n                                    values: \"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\",\n                                    result: \"hardAlpha\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feOffset\", {\n                                    dy: \"-24.0881\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feGaussianBlur\", {\n                                    stdDeviation: \"18.0661\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feComposite\", {\n                                    in2: \"hardAlpha\",\n                                    operator: \"arithmetic\",\n                                    k2: \"-1\",\n                                    k3: \"1\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feColorMatrix\", {\n                                    type: \"matrix\",\n                                    values: \"0 0 0 0 0.536175 0 0 0 0 0.741662 0 0 0 0 0.638918 0 0 0 0.7 0\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"feBlend\", {\n                                    mode: \"normal\",\n                                    in2: \"shape\",\n                                    result: \"effect1_innerShadow_182_70\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                            id: \"paint0_linear_182_70\",\n                            x1: \"247\",\n                            y1: \"0\",\n                            x2: \"247\",\n                            y2: \"494\",\n                            gradientUnits: \"userSpaceOnUse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    stopColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"0.6\",\n                                    stopColor: \"#4CAA7D\",\n                                    stopOpacity: \"0.1\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"1\",\n                                    stopColor: \"#E1FFF1\",\n                                    stopOpacity: \"0.5\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                            id: \"paint1_linear_182_70\",\n                            x1: \"247\",\n                            y1: \"2.15294e-8\",\n                            x2: \"247\",\n                            y2: \"494\",\n                            gradientUnits: \"userSpaceOnUse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    stopColor: \"#FDFFFE\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"0.4\",\n                                    stopColor: \"#549876\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"1\",\n                                    stopColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                            id: \"paint2_linear_182_70\",\n                            x1: \"246\",\n                            y1: \"111\",\n                            x2: \"246\",\n                            y2: \"383\",\n                            gradientUnits: \"userSpaceOnUse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    stopColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"0.6\",\n                                    stopColor: \"#4CAA7D\",\n                                    stopOpacity: \"0.1\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"1\",\n                                    stopColor: \"#E1FFF1\",\n                                    stopOpacity: \"0.5\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                            id: \"paint3_linear_182_70\",\n                            x1: \"246\",\n                            y1: \"111\",\n                            x2: \"246\",\n                            y2: \"383\",\n                            gradientUnits: \"userSpaceOnUse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    stopColor: \"#FDFFFE\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"0.4\",\n                                    stopColor: \"#549876\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"1\",\n                                    stopColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n            lineNumber: 65,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n        lineNumber: 64,\n        columnNumber: 3\n    }, undefined);\n};\n_c = CircleElement;\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"snap-y snap-mandatory overflow-y-scroll h-[100dvh] min-h-[100dvh] w-full overflow-x-hidden no-scrollbar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border w-full snap-always snap-center h-[100dvh] min-h-[100dvh] flex flex-col items-center justify-center bg-bg-primary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full flex flex-col items-center justify-center gap-12 px-4 md:px-8 lg:px-12 mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl text-center text-text-primary\",\n                                                children: [\n                                                    \"Nous cr\\xe9ons des\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"gradient-text\",\n                                                        children: \"applications exceptionnelles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl w-full text-center text-text-primary mt-4\",\n                                                children: \"qui transforment vos id\\xe9es\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl md:text-3xl lg:text-3xl tracking-normal text-text-secondary\",\n                                        children: \"Design \\xe9l\\xe9gant, code robuste, r\\xe9sultats mesurables.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-secondary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl md:text-5xl w-full text-center leading-tight whitespace-pre-wrap px-4 md:px-8 lg:px-12\",\n                                children: [\n                                    \"Nous fa\\xe7onnons l'excellence en\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"d\\xe9veloppement web\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this),\n                                    \",\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"applications mobiles\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" et\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"design digital\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            services.map((service, index)=>{\n                const Icon = service.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"snap-always snap-center min-h-screen\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-[100dvh] min-h-[100dvh] flex items-center justify-center text-white\",\n                                style: {\n                                    backgroundColor: \"#1C1B1C\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircleElement, {\n                                        icon: Icon\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fixed w-11/12 lg:w-full bottom-5 md:bottom-10 lg:bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-4 text-center pointer-events-none z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-6xl lg:text-7xl font-light opacity-0\",\n                                                children: service.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base lg:text-lg lg:w-6/12 text-[#A0A4A1] opacity-0 px-1 lg:px-2\",\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-1/2 lg:left-2 lg:top-1/2 bottom-[1vh] -translate-x-1/2 lg:-translate-x-0 lg:-translate-y-1/2 -translate-y-1/2 flex flex-row lg:flex-col lg:items-start items-center justify-center lg:gap-2 gap-6 px-4 md:px-8 lg:px-12 mx-auto pointer-events-auto z-[999]\",\n                                        children: services.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-left transition-colors duration-300 \".concat(i === index ? \"text-white\" : \"text-gray-500\"),\n                                                style: {\n                                                    fontFamily: \"monospace\"\n                                                },\n                                                children: service.title.toUpperCase().replace(/[^A-Z]/g, \"\").slice(0, 8)\n                                            }, i, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 13\n                    }, this)\n                }, index, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-primary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl md:text-5xl w-full text-center leading-tight whitespace-pre-wrap px-4 md:px-8 lg:px-12\",\n                                children: [\n                                    \"Notre m\\xe9thodologie \\xe9prouv\\xe9e en\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"4 \\xe9tapes\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    \"pour transformer vos id\\xe9es en\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"succ\\xe8s digitaux\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            processes.map((process, index)=>{\n                const Icon = process.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"snap-always snap-center min-h-screen\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-[100dvh] min-h-[100dvh] flex items-center justify-center text-white\",\n                                style: {\n                                    backgroundColor: \"#1C1B1C\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircleElement, {\n                                        icon: Icon\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fixed w-11/12 lg:w-full bottom-5 md:bottom-10 lg:bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-4 text-center pointer-events-none z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-6xl lg:text-7xl font-light opacity-0\",\n                                                children: process.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base lg:text-lg lg:w-6/12 text-[#A0A4A1] opacity-0 px-1 lg:px-2\",\n                                                children: process.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-1/2 lg:left-2 lg:top-1/2 bottom-[1vh] -translate-x-1/2 lg:-translate-x-0 lg:-translate-y-1/2 -translate-y-1/2 flex flex-row lg:flex-col lg:items-start items-center justify-center lg:gap-2 gap-6 px-4 md:px-8 lg:px-12 mx-auto pointer-events-auto z-[999]\",\n                                        children: processes.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-left transition-colors duration-300 \".concat(i === index ? \"text-white\" : \"text-gray-500\"),\n                                                style: {\n                                                    fontFamily: \"monospace\"\n                                                },\n                                                children: process.title.toUpperCase()\n                                            }, i, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 13\n                    }, this)\n                }, index, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-secondary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6\",\n                                        children: [\n                                            \"Pr\\xeat \\xe0 transformer\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"gradient-text\",\n                                                children: \"votre vision\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            \"en r\\xe9alit\\xe9 ?\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto mb-12\",\n                                        children: \"Discutons de votre projet et d\\xe9couvrons ensemble comment nous pouvons vous aider \\xe0 atteindre vos objectifs.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"shimmer-effect bg-gradient-to-r from-primary-green to-primary-green-dark text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary-green/25\",\n                                        children: \"D\\xe9marrer votre projet\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"CircleElement\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});