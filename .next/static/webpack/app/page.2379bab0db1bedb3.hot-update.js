"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ProcessScroll.tsx":
/*!**************************************!*\
  !*** ./components/ProcessScroll.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst processSteps = [\n    {\n        number: \"01\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9couverte & Strat\\xe9gie\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        number: \"02\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Design & Prototypage\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        number: \"03\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"D\\xe9veloppement Agile\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        number: \"04\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Lancement & Optimisation\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nconst ProcessScroll = ()=>{\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [scrollProgress, setScrollProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            if (!containerRef.current) return;\n            const container = containerRef.current;\n            const containerTop = container.offsetTop;\n            const containerHeight = container.offsetHeight;\n            const windowHeight = window.innerHeight;\n            const scrollTop = window.scrollY;\n            // Calculate scroll progress through this section\n            const startScroll = containerTop - windowHeight;\n            const endScroll = containerTop + containerHeight - windowHeight;\n            const progress = Math.max(0, Math.min(1, (scrollTop - startScroll) / (endScroll - startScroll)));\n            setScrollProgress(progress);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        handleScroll() // Initial calculation\n        ;\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Calculate horizontal transform based on scroll progress\n    const totalWidth = processSteps.length * 100 // Each step takes 100vw\n    ;\n    const translateX = -(scrollProgress * (totalWidth - 100) // Move from 0 to -(totalWidth - 100)\n    );\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"relative\",\n        style: {\n            height: \"\".concat(processSteps.length * 100, \"vh\")\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"sticky top-0 h-screen overflow-hidden bg-bg-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center z-10 bg-bg-primary/90 backdrop-blur-sm transition-opacity duration-500\",\n                    style: {\n                        opacity: scrollProgress < 0.1 ? 1 : 0\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary\",\n                                children: [\n                                    \"Notre\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Processus\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed\",\n                                children: \"D\\xe9couvrez notre m\\xe9thodologie \\xe9tape par \\xe9tape. Chaque phase est pens\\xe9e pour maximiser votre succ\\xe8s.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-full transition-transform duration-100 ease-out\",\n                    style: {\n                        transform: \"translateX(\".concat(translateX, \"vw)\"),\n                        width: \"\".concat(totalWidth, \"vw\")\n                    },\n                    children: processSteps.map((step, index)=>{\n                        const Icon = step.icon;\n                        const isActive = scrollProgress >= index / processSteps.length && scrollProgress < (index + 1) / processSteps.length;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-screen h-full flex items-center justify-center relative\",\n                            style: {\n                                backgroundColor: index % 2 === 0 ? \"var(--bg-secondary)\" : \"var(--bg-primary)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"container mx-auto px-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center lg:justify-start order-2 lg:order-1 transition-all duration-700 \".concat(isActive ? \"opacity-100 translate-x-0\" : \"opacity-50 translate-x-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl transition-all duration-500 group-hover:scale-110 group-hover:shadow-2xl group-hover:shadow-primary-green/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-display text-4xl font-bold text-white transition-all duration-300 group-hover:scale-110\",\n                                                                children: step.number\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                                lineNumber: 107,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light transition-all duration-300 group-hover:scale-110 group-hover:rotate-12\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"w-8 h-8 text-primary-green\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -bottom-4 -left-4 w-6 h-6 bg-accent-purple rounded-full opacity-20 animate-float-slow\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-8 left-8 w-4 h-4 bg-primary-green rounded-full opacity-30 animate-float-medium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center lg:text-left order-1 lg:order-2 transition-all duration-700 delay-200 \".concat(isActive ? \"opacity-100 translate-x-0\" : \"opacity-30 translate-x-12\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary\",\n                                                        children: step.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                                        children: step.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2\",\n                                    children: processSteps.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-2 rounded-full transition-all duration-300 \".concat(i === index ? \"bg-primary-green w-8\" : \"bg-text-muted w-2\")\n                                        }, i, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProcessScroll, \"2Nda3y7kUUkFkE+mAwZd39p+4rk=\");\n_c = ProcessScroll;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProcessScroll);\nvar _c;\n$RefreshReg$(_c, \"ProcessScroll\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ProcessScroll.tsx\n"));

/***/ })

});