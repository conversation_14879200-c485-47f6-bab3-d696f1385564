"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ProcessScroll.tsx":
/*!**************************************!*\
  !*** ./components/ProcessScroll.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst processSteps = [\n    {\n        number: \"01\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        title: \"D\\xe9couverte & Strat\\xe9gie\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        number: \"02\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Design & Prototypage\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        number: \"03\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"D\\xe9veloppement Agile\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        number: \"04\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Lancement & Optimisation\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nconst ProcessScroll = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    const titleAnimation = useScrollAnimation(0.3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"scroll-section relative flex items-center justify-center bg-bg-primary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: titleAnimation.ref,\n                        className: \"scroll-fade-in \".concat(titleAnimation.isVisible ? \"visible\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary\",\n                                children: [\n                                    \"Notre\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Processus\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed\",\n                                children: \"Une m\\xe9thodologie \\xe9prouv\\xe9e qui transforme vos id\\xe9es en solutions digitales performantes, \\xe9tape par \\xe9tape.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            processSteps.map(_s1((step, index)=>{\n                _s1();\n                const Icon = step.icon;\n                const visualAnimation = useScrollAnimation(0.2);\n                const contentAnimation = useScrollAnimation(0.3);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"scroll-section relative flex items-center justify-center bg-bg-secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: visualAnimation.ref,\n                                    className: \"flex justify-center lg:justify-start order-2 lg:order-1 scroll-slide-left \".concat(visualAnimation.isVisible ? \"visible\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl transition-all duration-500 group-hover:scale-110 group-hover:shadow-2xl group-hover:shadow-primary-green/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-display text-4xl font-bold text-white transition-all duration-300 group-hover:scale-110\",\n                                                    children: step.number\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light transition-all duration-300 group-hover:scale-110 group-hover:rotate-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-8 h-8 text-primary-green\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-4 -left-4 w-6 h-6 bg-accent-purple rounded-full opacity-20 animate-float-slow\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-8 left-8 w-4 h-4 bg-primary-green rounded-full opacity-30 animate-float-medium\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: contentAnimation.ref,\n                                    className: \"text-center lg:text-left order-1 lg:order-2 scroll-slide-right \".concat(contentAnimation.isVisible ? \"visible\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary\",\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined)\n                }, index, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, undefined);\n            }, \"026KWJnxPa7UGC7fL9w8+taCPqk=\", true))\n        ]\n    }, void 0, true);\n};\n_s(ProcessScroll, \"vfFimljMtrWcNywNf5H9nqed+mk=\", true);\n_c = ProcessScroll;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProcessScroll);\nvar _c;\n$RefreshReg$(_c, \"ProcessScroll\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ProcessScroll.tsx\n"));

/***/ })

});