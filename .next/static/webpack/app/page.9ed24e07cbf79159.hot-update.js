"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./components/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst services = [\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9veloppement Web\",\n        description: \"Applications web modernes et performantes. React, Vue.js, Node.js - nous ma\\xeetrisons les technologies de pointe pour cr\\xe9er des exp\\xe9riences utilisateur exceptionnelles.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Applications Mobile\",\n        description: \"Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie \\xe0 vos id\\xe9es sur tous les \\xe9crans.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Design & UX/UI\",\n        description: \"Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous cr\\xe9ons des interfaces intuitives.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Branding Digital\",\n        description: \"Identit\\xe9s visuelles m\\xe9morables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Maintenance & Support\",\n        description: \"Accompagnement technique continu pour faire \\xe9voluer vos projets. Monitoring, mises \\xe0 jour, optimisations - nous veillons sur vos applications.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Consulting Technique\",\n        description: \"Expertise strat\\xe9gique pour orienter vos d\\xe9cisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions.\"\n    }\n];\nconst processes = [\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"D\\xe9couverte\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Conception\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"D\\xe9veloppement\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: \"Lancement\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"snap-y snap-mandatory overflow-y-scroll h-[100dvh] min-h-[100dvh] w-full overflow-x-hidden no-scrollbar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border w-full snap-always snap-center h-[100dvh] min-h-[100dvh] flex flex-col items-center justify-center bg-bg-primary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full flex flex-col items-center justify-center gap-12 px-4 md:px-8 lg:px-12 mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl text-center text-text-primary\",\n                                                children: [\n                                                    \"Nous cr\\xe9ons des\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"gradient-text\",\n                                                        children: \"applications exceptionnelles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl w-full text-center text-text-primary mt-4\",\n                                                children: \"qui transforment vos id\\xe9es\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl md:text-3xl lg:text-3xl tracking-normal text-text-secondary\",\n                                        children: \"Design \\xe9l\\xe9gant, code robuste, r\\xe9sultats mesurables.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-secondary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl md:text-5xl w-full text-center leading-tight whitespace-pre-wrap px-4 md:px-8 lg:px-12\",\n                                children: [\n                                    \"Nous fa\\xe7onnons l'excellence en\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"d\\xe9veloppement web\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this),\n                                    \",\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"applications mobiles\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" et\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"design digital\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            services.map((service, index)=>{\n                const Icon = service.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"snap-always snap-center min-h-screen\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-[100dvh] min-h-[100dvh] flex items-center justify-center text-white\",\n                                style: {\n                                    backgroundColor: \"#1C1B1C\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircleElement, {\n                                        icon: Icon\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fixed w-11/12 lg:w-full bottom-5 md:bottom-10 lg:bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-4 text-center pointer-events-none z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-6xl lg:text-7xl font-light text-white\",\n                                                children: service.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base lg:text-lg lg:w-6/12 text-[#A0A4A1] px-1 lg:px-2\",\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-1/2 lg:left-2 lg:top-1/2 bottom-[1vh] -translate-x-1/2 lg:-translate-x-0 lg:-translate-y-1/2 -translate-y-1/2 flex flex-row lg:flex-col lg:items-start items-center justify-center lg:gap-2 gap-6 px-4 md:px-8 lg:px-12 mx-auto pointer-events-auto z-[999]\",\n                                        children: services.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-left transition-colors duration-300 \".concat(i === index ? \"text-white\" : \"text-gray-500\"),\n                                                style: {\n                                                    fontFamily: \"monospace\"\n                                                },\n                                                children: service.title.toUpperCase().replace(/[^A-Z]/g, \"\").slice(0, 8)\n                                            }, i, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 13\n                    }, this)\n                }, index, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-primary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl md:text-5xl w-full text-center leading-tight whitespace-pre-wrap px-4 md:px-8 lg:px-12\",\n                                children: [\n                                    \"Notre m\\xe9thodologie \\xe9prouv\\xe9e en\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"4 \\xe9tapes\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    \"pour transformer vos id\\xe9es en\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"succ\\xe8s digitaux\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            processes.map((process, index)=>{\n                const Icon = process.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"snap-always snap-center min-h-screen\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-[100dvh] min-h-[100dvh] flex items-center justify-center text-white\",\n                                style: {\n                                    backgroundColor: \"#1C1B1C\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircleElement, {\n                                        icon: Icon\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fixed w-11/12 lg:w-full bottom-5 md:bottom-10 lg:bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-4 text-center pointer-events-none z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-6xl lg:text-7xl font-light text-white\",\n                                                children: process.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base lg:text-lg lg:w-6/12 text-[#A0A4A1] px-1 lg:px-2\",\n                                                children: process.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-1/2 lg:left-2 lg:top-1/2 bottom-[1vh] -translate-x-1/2 lg:-translate-x-0 lg:-translate-y-1/2 -translate-y-1/2 flex flex-row lg:flex-col lg:items-start items-center justify-center lg:gap-2 gap-6 px-4 md:px-8 lg:px-12 mx-auto pointer-events-auto z-[999]\",\n                                        children: processes.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-left transition-colors duration-300 \".concat(i === index ? \"text-white\" : \"text-gray-500\"),\n                                                style: {\n                                                    fontFamily: \"monospace\"\n                                                },\n                                                children: process.title.toUpperCase()\n                                            }, i, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)\n                }, index, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-secondary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6\",\n                                        children: [\n                                            \"Pr\\xeat \\xe0 transformer\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"gradient-text\",\n                                                children: \"votre vision\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            \"en r\\xe9alit\\xe9 ?\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto mb-12\",\n                                        children: \"Discutons de votre projet et d\\xe9couvrons ensemble comment nous pouvons vous aider \\xe0 atteindre vos objectifs.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"shimmer-effect bg-gradient-to-r from-primary-green to-primary-green-dark text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary-green/25\",\n                                        children: \"D\\xe9marrer votre projet\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});