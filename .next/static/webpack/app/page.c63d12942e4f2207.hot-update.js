"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"(app-pages-browser)/./components/Hero.tsx\");\n/* harmony import */ var _components_ServicesScroll__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ServicesScroll */ \"(app-pages-browser)/./components/ServicesScroll.tsx\");\n/* harmony import */ var _components_ProcessScroll__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProcessScroll */ \"(app-pages-browser)/./components/ProcessScroll.tsx\");\n/* harmony import */ var _components_CTAFooter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CTAFooter */ \"(app-pages-browser)/./components/CTAFooter.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"snap-y snap-mandatory overflow-y-scroll h-screen w-full overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServicesScroll__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProcessScroll__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CTAFooter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUd3QztBQUNKO0FBQ29CO0FBQ0Y7QUFDUjtBQUUvQixTQUFTSztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNQLDBEQUFNQTs7Ozs7MEJBQ1AsOERBQUNDLHdEQUFJQTs7Ozs7MEJBQ0wsOERBQUNDLGtFQUFjQTs7Ozs7MEJBQ2YsOERBQUNDLGlFQUFhQTs7Ozs7MEJBQ2QsOERBQUNDLDZEQUFTQTs7Ozs7Ozs7Ozs7QUFHaEI7S0FWd0JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9wYWdlLnRzeD83NjAzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvSGVhZGVyJ1xuaW1wb3J0IEhlcm8gZnJvbSAnQC9jb21wb25lbnRzL0hlcm8nXG5pbXBvcnQgU2VydmljZXNTY3JvbGwgZnJvbSAnQC9jb21wb25lbnRzL1NlcnZpY2VzU2Nyb2xsJ1xuaW1wb3J0IFByb2Nlc3NTY3JvbGwgZnJvbSAnQC9jb21wb25lbnRzL1Byb2Nlc3NTY3JvbGwnXG5pbXBvcnQgQ1RBRm9vdGVyIGZyb20gJ0AvY29tcG9uZW50cy9DVEFGb290ZXInXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzbmFwLXkgc25hcC1tYW5kYXRvcnkgb3ZlcmZsb3cteS1zY3JvbGwgaC1zY3JlZW4gdy1mdWxsIG92ZXJmbG93LXgtaGlkZGVuXCI+XG4gICAgICA8SGVhZGVyIC8+XG4gICAgICA8SGVybyAvPlxuICAgICAgPFNlcnZpY2VzU2Nyb2xsIC8+XG4gICAgICA8UHJvY2Vzc1Njcm9sbCAvPlxuICAgICAgPENUQUZvb3RlciAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiSGVhZGVyIiwiSGVybyIsIlNlcnZpY2VzU2Nyb2xsIiwiUHJvY2Vzc1Njcm9sbCIsIkNUQUZvb3RlciIsIkhvbWUiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Hero = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"snap-always snap-center min-h-screen relative flex items-center justify-center overflow-hidden bg-gradient-to-br from-bg-primary to-bg-secondary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-[0.03]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%237DD3AE' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-1/4 right-0 w-96 h-96 opacity-10\",\n                        animate: {\n                            y: [\n                                -20,\n                                20,\n                                -20\n                            ],\n                            rotate: [\n                                0,\n                                5,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 8,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            viewBox: \"0 0 400 400\",\n                            fill: \"none\",\n                            className: \"w-full h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                        id: \"heroGradient1\",\n                                        x1: \"0%\",\n                                        y1: \"0%\",\n                                        x2: \"100%\",\n                                        y2: \"100%\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                offset: \"0%\",\n                                                stopColor: \"#7DD3AE\",\n                                                stopOpacity: \"0.8\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                offset: \"100%\",\n                                                stopColor: \"#5BC192\",\n                                                stopOpacity: \"0.4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"200\",\n                                    cy: \"150\",\n                                    r: \"80\",\n                                    fill: \"url(#heroGradient1)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                    x: \"150\",\n                                    y: \"250\",\n                                    width: \"100\",\n                                    height: \"100\",\n                                    rx: \"20\",\n                                    fill: \"url(#heroGradient1)\",\n                                    transform: \"rotate(25 200 300)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-1/3 left-0 w-64 h-64 opacity-8\",\n                        animate: {\n                            y: [\n                                20,\n                                -20,\n                                20\n                            ],\n                            rotate: [\n                                0,\n                                -3,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 6,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            viewBox: \"0 0 200 200\",\n                            fill: \"none\",\n                            className: \"w-full h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                        id: \"heroGradient2\",\n                                        x1: \"0%\",\n                                        y1: \"0%\",\n                                        x2: \"100%\",\n                                        y2: \"100%\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                offset: \"0%\",\n                                                stopColor: \"#8B5CF6\",\n                                                stopOpacity: \"0.6\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                offset: \"100%\",\n                                                stopColor: \"#F97316\",\n                                                stopOpacity: \"0.3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"100\",\n                                    cy: \"100\",\n                                    r: \"60\",\n                                    fill: \"url(#heroGradient2)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"text-center max-w-4xl mx-auto\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h1, {\n                            className: \"font-display text-5xl md:text-7xl lg:text-8xl font-extrabold leading-tight mb-6\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            children: [\n                                \"Nous cr\\xe9ons des\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"highlight-effect gradient-text\",\n                                    children: \"applications exceptionnelles\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" \",\n                                \"qui transforment vos id\\xe9es en succ\\xe8s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                            className: \"text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto mb-12 leading-relaxed\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.6\n                            },\n                            children: \"De la conception au d\\xe9veloppement, nous accompagnons les entreprises ambitieuses dans la cr\\xe9ation d'exp\\xe9riences digitales m\\xe9morables. Design \\xe9l\\xe9gant, code robuste, r\\xe9sultats mesurables.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"#contact\",\n                                className: \"shimmer-effect inline-flex items-center gap-3 bg-gradient-to-r from-primary-green to-primary-green-dark text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary-green/25 group\",\n                                children: [\n                                    \"D\\xe9marrer votre projet\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5 transition-transform group-hover:translate-x-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Hero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Hero);\nvar _c;\n$RefreshReg$(_c, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Hero.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ProcessScroll.tsx":
/*!**************************************!*\
  !*** ./components/ProcessScroll.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst processSteps = [\n    {\n        number: \"01\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9couverte & Strat\\xe9gie\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        number: \"02\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Design & Prototypage\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        number: \"03\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"D\\xe9veloppement Agile\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        number: \"04\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Lancement & Optimisation\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nconst ProcessScroll = ()=>{\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const scrollPosition = window.scrollY;\n            const windowHeight = window.innerHeight;\n            const stepIndex = Math.floor(scrollPosition / windowHeight) - 7 // Adjust based on previous sections\n            ;\n            if (stepIndex >= 0 && stepIndex < processSteps.length) {\n                setCurrentStep(stepIndex);\n            }\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"snap-always snap-center min-h-screen relative flex items-center justify-center bg-bg-primary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary\",\n                                children: [\n                                    \"Notre\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Processus\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed\",\n                                children: \"Une m\\xe9thodologie \\xe9prouv\\xe9e qui transforme vos id\\xe9es en solutions digitales performantes, \\xe9tape par \\xe9tape.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            processSteps.map((step, index)=>{\n                const Icon = step.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"snap-always snap-center min-h-screen relative flex items-center justify-center bg-bg-secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"flex justify-center lg:justify-start order-2 lg:order-1\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.2\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-display text-4xl font-bold text-white\",\n                                                        children: step.number\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-8 h-8 text-primary-green\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"text-center lg:text-left order-1 lg:order-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary\",\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                                children: step.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2\",\n                            children: processSteps.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(i === index ? \"bg-primary-green w-8\" : \"bg-text-muted\")\n                                }, i, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, undefined);\n            })\n        ]\n    }, void 0, true);\n};\n_s(ProcessScroll, \"dqJMRMh825IZM3uH5OU+Vfn9tX8=\");\n_c = ProcessScroll;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProcessScroll);\nvar _c;\n$RefreshReg$(_c, \"ProcessScroll\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ProcessScroll.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ServicesScroll.tsx":
/*!***************************************!*\
  !*** ./components/ServicesScroll.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst services = [\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9veloppement Web\",\n        description: \"Applications web modernes et performantes. React, Vue.js, Node.js - nous ma\\xeetrisons les technologies de pointe pour cr\\xe9er des exp\\xe9riences utilisateur exceptionnelles et des architectures scalables.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Applications Mobile\",\n        description: \"Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie \\xe0 vos id\\xe9es sur tous les \\xe9crans.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Design & UX/UI\",\n        description: \"Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous cr\\xe9ons des interfaces intuitives qui racontent l'histoire de votre marque.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Branding Digital\",\n        description: \"Identit\\xe9s visuelles m\\xe9morables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque pour tous les supports digitaux.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Maintenance & Support\",\n        description: \"Accompagnement technique continu pour faire \\xe9voluer vos projets. Monitoring, mises \\xe0 jour, optimisations - nous veillons sur vos applications comme sur nos propres cr\\xe9ations.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Consulting Technique\",\n        description: \"Expertise strat\\xe9gique pour orienter vos d\\xe9cisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions pour votre business.\"\n    }\n];\nconst ServicesScroll = ()=>{\n    _s();\n    const [currentService, setCurrentService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const scrollPosition = window.scrollY;\n            const windowHeight = window.innerHeight;\n            const serviceIndex = Math.floor(scrollPosition / windowHeight) - 1;\n            if (serviceIndex >= 0 && serviceIndex < services.length) {\n                setCurrentService(serviceIndex);\n            }\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"snap-always snap-center min-h-screen relative flex items-center justify-center bg-bg-primary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary\",\n                                children: [\n                                    \"Nous fa\\xe7onnons l'excellence en\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"d\\xe9veloppement web\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \",\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"applications mobiles\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" et\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"design digital\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed\",\n                                children: \"D\\xe9couvrez nos services en faisant d\\xe9filer vers le bas. Chaque projet est une œuvre d'art technique.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            services.map((service, index)=>{\n                const Icon = service.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"snap-always snap-center min-h-screen relative flex items-center justify-center bg-bg-secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        className: \"text-center lg:text-left\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.2\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-2xl flex items-center justify-center mb-8 mx-auto lg:mx-0 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-10 h-10 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary\",\n                                                children: service.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        className: \"flex justify-center lg:justify-end\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-80 h-80 bg-gradient-to-br from-primary-green/10 to-primary-green-dark/10 rounded-3xl flex items-center justify-center border border-primary-green/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-32 h-32 text-primary-green opacity-30\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2\",\n                            children: services.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(i === index ? \"bg-primary-green w-8\" : \"bg-text-muted\")\n                                }, i, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, undefined);\n            })\n        ]\n    }, void 0, true);\n};\n_s(ServicesScroll, \"XoZ9Mv84YK/93fWhkzdRwWbrEt4=\");\n_c = ServicesScroll;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ServicesScroll);\nvar _c;\n$RefreshReg$(_c, \"ServicesScroll\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ServicesScroll.tsx\n"));

/***/ })

});