"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ProcessScroll.tsx":
/*!**************************************!*\
  !*** ./components/ProcessScroll.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst processSteps = [\n    {\n        number: \"01\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        title: \"D\\xe9couverte & Strat\\xe9gie\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        number: \"02\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Design & Prototypage\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        number: \"03\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"D\\xe9veloppement Agile\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        number: \"04\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Lancement & Optimisation\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nconst ProcessScroll = ()=>{\n    _s();\n    const [currentStep, setCurrentStep] = useState(0);\n    useEffect(()=>{\n        const handleScroll = ()=>{\n            const scrollPosition = window.scrollY;\n            const windowHeight = window.innerHeight;\n            const stepIndex = Math.floor(scrollPosition / windowHeight) - 7 // Adjust based on previous sections\n            ;\n            if (stepIndex >= 0 && stepIndex < processSteps.length) {\n                setCurrentStep(stepIndex);\n            }\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"snap-section relative flex items-center justify-center bg-bg-primary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary\",\n                                children: [\n                                    \"Notre\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Processus\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed\",\n                                children: \"Une m\\xe9thodologie \\xe9prouv\\xe9e qui transforme vos id\\xe9es en solutions digitales performantes, \\xe9tape par \\xe9tape.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined),\n            processSteps.map((step, index)=>{\n                const Icon = step.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"snap-section relative flex items-center justify-center bg-bg-secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        className: \"flex justify-center lg:justify-start order-2 lg:order-1\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.2\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-display text-4xl font-bold text-white\",\n                                                        children: step.number\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-8 h-8 text-primary-green\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        className: \"text-center lg:text-left order-1 lg:order-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary\",\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                                children: step.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2\",\n                            children: processSteps.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(i === index ? \"bg-primary-green w-8\" : \"bg-text-muted\")\n                                }, i, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined);\n            })\n        ]\n    }, void 0, true);\n};\n_s(ProcessScroll, \"dqJMRMh825IZM3uH5OU+Vfn9tX8=\");\n_c = ProcessScroll;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProcessScroll);\nvar _c;\n$RefreshReg$(_c, \"ProcessScroll\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ProcessScroll.tsx\n"));

/***/ })

});