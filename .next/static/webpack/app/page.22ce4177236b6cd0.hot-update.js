"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ProcessScroll.tsx":
/*!**************************************!*\
  !*** ./components/ProcessScroll.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useScrollAnimation */ \"(app-pages-browser)/./hooks/useScrollAnimation.ts\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst processSteps = [\n    {\n        number: \"01\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9couverte & Strat\\xe9gie\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        number: \"02\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Design & Prototypage\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        number: \"03\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"D\\xe9veloppement Agile\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        number: \"04\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Lancement & Optimisation\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nconst ProcessScroll = ()=>{\n    _s();\n    const titleAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"scroll-section relative flex items-center justify-center bg-bg-primary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: titleAnimation.ref,\n                        className: \"scroll-fade-in \".concat(titleAnimation.isVisible ? \"visible\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary\",\n                                children: [\n                                    \"Notre\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Processus\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed\",\n                                children: \"Une m\\xe9thodologie \\xe9prouv\\xe9e qui transforme vos id\\xe9es en solutions digitales performantes, \\xe9tape par \\xe9tape.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            processSteps.map((step, index)=>{\n                const Icon = step.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"scroll-section relative flex items-center justify-center bg-bg-secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        className: \"flex justify-center lg:justify-start order-2 lg:order-1\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.2\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-display text-4xl font-bold text-white\",\n                                                        children: step.number\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-8 h-8 text-primary-green\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        className: \"text-center lg:text-left order-1 lg:order-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary\",\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                                children: step.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2\",\n                            children: processSteps.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(i === index ? \"bg-primary-green w-8\" : \"bg-text-muted\")\n                                }, i, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined);\n            })\n        ]\n    }, void 0, true);\n};\n_s(ProcessScroll, \"vfFimljMtrWcNywNf5H9nqed+mk=\", false, function() {\n    return [\n        _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation\n    ];\n});\n_c = ProcessScroll;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProcessScroll);\nvar _c;\n$RefreshReg$(_c, \"ProcessScroll\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ProcessScroll.tsx\n"));

/***/ })

});