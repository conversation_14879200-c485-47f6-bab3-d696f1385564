"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/CTAFooter.tsx":
/*!**********************************!*\
  !*** ./components/CTAFooter.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Github,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Github,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Github,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Github,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CTAFooter = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        className: \"snap-always snap-center min-h-screen relative bg-gradient-to-br from-primary-green to-primary-green-dark overflow-hidden flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-1/4 right-0 w-96 h-96 opacity-10\",\n                        animate: {\n                            y: [\n                                -30,\n                                30,\n                                -30\n                            ],\n                            rotate: [\n                                0,\n                                10,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 12,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            viewBox: \"0 0 400 400\",\n                            fill: \"none\",\n                            className: \"w-full h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"200\",\n                                    cy: \"150\",\n                                    r: \"80\",\n                                    fill: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                    x: \"150\",\n                                    y: \"250\",\n                                    width: \"100\",\n                                    height: \"100\",\n                                    rx: \"20\",\n                                    fill: \"white\",\n                                    transform: \"rotate(25 200 300)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute bottom-1/4 left-0 w-64 h-64 opacity-8\",\n                        animate: {\n                            y: [\n                                20,\n                                -20,\n                                20\n                            ],\n                            rotate: [\n                                0,\n                                -5,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 10,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            viewBox: \"0 0 200 200\",\n                            fill: \"none\",\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                points: \"100,20 180,180 20,180\",\n                                fill: \"white\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex-1 flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"text-center max-w-4xl mx-auto\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl font-bold mb-6 text-white\",\n                                children: \"Pr\\xeat \\xe0 transformer votre vision en r\\xe9alit\\xe9 ?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-white/90 max-w-3xl mx-auto mb-12 leading-relaxed\",\n                                children: \"Discutons de votre projet et d\\xe9couvrons ensemble comment nous pouvons cr\\xe9er quelque chose d'exceptionnel. Consultation gratuite, devis personnalis\\xe9 sous 48h.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"shimmer-effect inline-flex items-center gap-3 bg-white text-primary-green px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 hover:shadow-xl hover:shadow-black/20 group\",\n                                    children: [\n                                        \"D\\xe9marrer votre projet\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5 transition-transform group-hover:translate-x-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 border-t border-white/10 pt-16 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-12 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"max-w-md\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-white rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-primary-green font-bold text-sm\",\n                                                        children: \"G\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-display text-2xl font-bold text-white\",\n                                                    children: \"GibbonLab\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 leading-relaxed\",\n                                            children: \"Cr\\xe9ateurs d'exp\\xe9riences digitales exceptionnelles. Nous transformons vos id\\xe9es en solutions techniques performantes et esth\\xe9tiques.\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-8\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-display text-lg font-semibold text-white mb-4\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        \"D\\xe9veloppement Web\",\n                                                        \"Applications Mobile\",\n                                                        \"Design & UX/UI\",\n                                                        \"Branding Digital\"\n                                                    ].map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                href: \"#services\",\n                                                                className: \"text-white/70 hover:text-white transition-colors\",\n                                                                children: service\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, service, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-display text-lg font-semibold text-white mb-4\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                className: \"text-white/70 hover:text-white transition-colors\",\n                                                                children: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                href: \"tel:+33123456789\",\n                                                                className: \"text-white/70 hover:text-white transition-colors\",\n                                                                children: \"+33 1 23 45 67 89\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-white/70\",\n                                                            children: \"Paris, France\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"flex flex-col sm:flex-row justify-between items-center pt-8 border-t border-white/10\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-sm mb-4 sm:mb-0\",\n                                    children: \"\\xa9 2024 GibbonLab. Cr\\xe9\\xe9 avec passion \\xe0 Paris. Tous droits r\\xe9serv\\xe9s.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        {\n                                            icon: _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                            href: \"#\"\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                            href: \"#\"\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                            href: \"#\"\n                                        }\n                                    ].map((param, index)=>{\n                                        let { icon: Icon, href } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                            href: href,\n                                            className: \"w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-white/70 hover:text-white hover:bg-white/20 transition-all duration-300\",\n                                            whileHover: {\n                                                scale: 1.1,\n                                                y: -2\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CTAFooter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CTAFooter);\nvar _c;\n$RefreshReg$(_c, \"CTAFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvQ1RBRm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXNDO0FBQzhCO0FBQ3hDO0FBRTVCLE1BQU1NLFlBQVk7SUFDaEIscUJBQ0UsOERBQUNDO1FBQVFDLElBQUc7UUFBVUMsV0FBVTs7MEJBRTlCLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUlELFdBQVU7b0JBQW1CRSxPQUFPO3dCQUN2Q0MsaUJBQWtCO29CQUNwQjs7Ozs7Ozs7Ozs7MEJBSUYsOERBQUNGO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ1QsaURBQU1BLENBQUNVLEdBQUc7d0JBQ1RELFdBQVU7d0JBQ1ZJLFNBQVM7NEJBQ1BDLEdBQUc7Z0NBQUMsQ0FBQztnQ0FBSTtnQ0FBSSxDQUFDOzZCQUFHOzRCQUNqQkMsUUFBUTtnQ0FBQztnQ0FBRztnQ0FBSTs2QkFBRTt3QkFDcEI7d0JBQ0FDLFlBQVk7NEJBQ1ZDLFVBQVU7NEJBQ1ZDLFFBQVFDOzRCQUNSQyxNQUFNO3dCQUNSO2tDQUVBLDRFQUFDQzs0QkFBSUMsU0FBUTs0QkFBY0MsTUFBSzs0QkFBT2QsV0FBVTs7OENBQy9DLDhEQUFDZTtvQ0FBT0MsSUFBRztvQ0FBTUMsSUFBRztvQ0FBTUMsR0FBRTtvQ0FBS0osTUFBSzs7Ozs7OzhDQUN0Qyw4REFBQ0s7b0NBQUtDLEdBQUU7b0NBQU1mLEdBQUU7b0NBQU1nQixPQUFNO29DQUFNQyxRQUFPO29DQUFNQyxJQUFHO29DQUFLVCxNQUFLO29DQUFRVSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJbEYsOERBQUNqQyxpREFBTUEsQ0FBQ1UsR0FBRzt3QkFDVEQsV0FBVTt3QkFDVkksU0FBUzs0QkFDUEMsR0FBRztnQ0FBQztnQ0FBSSxDQUFDO2dDQUFJOzZCQUFHOzRCQUNoQkMsUUFBUTtnQ0FBQztnQ0FBRyxDQUFDO2dDQUFHOzZCQUFFO3dCQUNwQjt3QkFDQUMsWUFBWTs0QkFDVkMsVUFBVTs0QkFDVkMsUUFBUUM7NEJBQ1JDLE1BQU07d0JBQ1I7a0NBRUEsNEVBQUNDOzRCQUFJQyxTQUFROzRCQUFjQyxNQUFLOzRCQUFPZCxXQUFVO3NDQUMvQyw0RUFBQ3lCO2dDQUFRQyxRQUFPO2dDQUF3QlosTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNbkQsOERBQUNiO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ1QsaURBQU1BLENBQUNVLEdBQUc7d0JBQ1RELFdBQVU7d0JBQ1YyQixTQUFTOzRCQUFFQyxTQUFTOzRCQUFHdkIsR0FBRzt3QkFBRzt3QkFDN0J3QixhQUFhOzRCQUFFRCxTQUFTOzRCQUFHdkIsR0FBRzt3QkFBRTt3QkFDaENFLFlBQVk7NEJBQUVDLFVBQVU7d0JBQUk7d0JBQzVCc0IsVUFBVTs0QkFBRUMsTUFBTTt3QkFBSzs7MENBRXZCLDhEQUFDQztnQ0FBR2hDLFdBQVU7MENBQThEOzs7Ozs7MENBRzVFLDhEQUFDaUM7Z0NBQUVqQyxXQUFVOzBDQUFnRTs7Ozs7OzBDQUk3RSw4REFBQ1QsaURBQU1BLENBQUNVLEdBQUc7Z0NBQ1RpQyxZQUFZO29DQUFFQyxPQUFPO2dDQUFLO2dDQUMxQkMsVUFBVTtvQ0FBRUQsT0FBTztnQ0FBSzswQ0FFeEIsNEVBQUN2QyxrREFBSUE7b0NBQ0h5QyxNQUFLO29DQUNMckMsV0FBVTs7d0NBQ1g7c0RBRUMsOERBQUNSLDhHQUFVQTs0Q0FBQ1EsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFoQyw4REFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUViLDhEQUFDVCxpREFBTUEsQ0FBQ1UsR0FBRztvQ0FDVEQsV0FBVTtvQ0FDVjJCLFNBQVM7d0NBQUVDLFNBQVM7d0NBQUdSLEdBQUcsQ0FBQztvQ0FBRztvQ0FDOUJTLGFBQWE7d0NBQUVELFNBQVM7d0NBQUdSLEdBQUc7b0NBQUU7b0NBQ2hDYixZQUFZO3dDQUFFQyxVQUFVO29DQUFJO29DQUM1QnNCLFVBQVU7d0NBQUVDLE1BQU07b0NBQUs7O3NEQUV2Qiw4REFBQzlCOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVU7OERBQ2IsNEVBQUNzQzt3REFBS3RDLFdBQVU7a0VBQXVDOzs7Ozs7Ozs7Ozs4REFFekQsOERBQUNzQztvREFBS3RDLFdBQVU7OERBQTZDOzs7Ozs7Ozs7Ozs7c0RBSS9ELDhEQUFDaUM7NENBQUVqQyxXQUFVO3NEQUFnQzs7Ozs7Ozs7Ozs7OzhDQU0vQyw4REFBQ1QsaURBQU1BLENBQUNVLEdBQUc7b0NBQ1RELFdBQVU7b0NBQ1YyQixTQUFTO3dDQUFFQyxTQUFTO3dDQUFHUixHQUFHO29DQUFHO29DQUM3QlMsYUFBYTt3Q0FBRUQsU0FBUzt3Q0FBR1IsR0FBRztvQ0FBRTtvQ0FDaENiLFlBQVk7d0NBQUVDLFVBQVU7b0NBQUk7b0NBQzVCc0IsVUFBVTt3Q0FBRUMsTUFBTTtvQ0FBSzs7c0RBRXZCLDhEQUFDOUI7OzhEQUNDLDhEQUFDc0M7b0RBQUd2QyxXQUFVOzhEQUFxRDs7Ozs7OzhEQUduRSw4REFBQ3dDO29EQUFHeEMsV0FBVTs4REFDWDt3REFBQzt3REFBcUI7d0RBQXVCO3dEQUFrQjtxREFBbUIsQ0FBQ3lDLEdBQUcsQ0FBQyxDQUFDQyx3QkFDdkYsOERBQUNDO3NFQUNDLDRFQUFDL0Msa0RBQUlBO2dFQUFDeUMsTUFBSztnRUFBWXJDLFdBQVU7MEVBQzlCMEM7Ozs7OzsyREFGSUE7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBU2YsOERBQUN6Qzs7OERBQ0MsOERBQUNzQztvREFBR3ZDLFdBQVU7OERBQXFEOzs7Ozs7OERBR25FLDhEQUFDd0M7b0RBQUd4QyxXQUFVOztzRUFDWiw4REFBQzJDO3NFQUNDLDRFQUFDL0Msa0RBQUlBO2dFQUFDeUMsTUFBSztnRUFBK0JyQyxXQUFVOzBFQUFtRDs7Ozs7Ozs7Ozs7c0VBSXpHLDhEQUFDMkM7c0VBQ0MsNEVBQUMvQyxrREFBSUE7Z0VBQUN5QyxNQUFLO2dFQUFtQnJDLFdBQVU7MEVBQW1EOzs7Ozs7Ozs7OztzRUFJN0YsOERBQUMyQzs0REFBRzNDLFdBQVU7c0VBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBU3RDLDhEQUFDVCxpREFBTUEsQ0FBQ1UsR0FBRzs0QkFDVEQsV0FBVTs0QkFDVjJCLFNBQVM7Z0NBQUVDLFNBQVM7Z0NBQUd2QixHQUFHOzRCQUFHOzRCQUM3QndCLGFBQWE7Z0NBQUVELFNBQVM7Z0NBQUd2QixHQUFHOzRCQUFFOzRCQUNoQ0UsWUFBWTtnQ0FBRUMsVUFBVTs0QkFBSTs0QkFDNUJzQixVQUFVO2dDQUFFQyxNQUFNOzRCQUFLOzs4Q0FFdkIsOERBQUNFO29DQUFFakMsV0FBVTs4Q0FBcUM7Ozs7Ozs4Q0FJbEQsOERBQUNDO29DQUFJRCxXQUFVOzhDQUNaO3dDQUNDOzRDQUFFNEMsTUFBTW5ELDhHQUFRQTs0Q0FBRTRDLE1BQU07d0NBQUk7d0NBQzVCOzRDQUFFTyxNQUFNbEQsOEdBQU1BOzRDQUFFMkMsTUFBTTt3Q0FBSTt3Q0FDMUI7NENBQUVPLE1BQU1qRCw4R0FBT0E7NENBQUUwQyxNQUFNO3dDQUFJO3FDQUM1QixDQUFDSSxHQUFHLENBQUMsUUFBdUJJOzRDQUF0QixFQUFFRCxNQUFNRSxJQUFJLEVBQUVULElBQUksRUFBRTs2REFDekIsOERBQUM5QyxpREFBTUEsQ0FBQ3dELENBQUM7NENBRVBWLE1BQU1BOzRDQUNOckMsV0FBVTs0Q0FDVmtDLFlBQVk7Z0RBQUVDLE9BQU87Z0RBQUs5QixHQUFHLENBQUM7NENBQUU7NENBQ2hDK0IsVUFBVTtnREFBRUQsT0FBTzs0Q0FBSztzREFFeEIsNEVBQUNXO2dEQUFLOUMsV0FBVTs7Ozs7OzJDQU5YNkM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFldkI7S0ExTE1oRDtBQTRMTiwrREFBZUEsU0FBU0EsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL0NUQUZvb3Rlci50c3g/N2JjMSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7IEFycm93UmlnaHQsIExpbmtlZGluLCBHaXRodWIsIFR3aXR0ZXIgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5cbmNvbnN0IENUQUZvb3RlciA9ICgpID0+IHtcbiAgcmV0dXJuIChcbiAgICA8c2VjdGlvbiBpZD1cImNvbnRhY3RcIiBjbGFzc05hbWU9XCJzbmFwLWFsd2F5cyBzbmFwLWNlbnRlciBtaW4taC1zY3JlZW4gcmVsYXRpdmUgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1wcmltYXJ5LWdyZWVuIHRvLXByaW1hcnktZ3JlZW4tZGFyayBvdmVyZmxvdy1oaWRkZW4gZmxleCBmbGV4LWNvbFwiPlxuICAgICAgey8qIEJhY2tncm91bmQgUGF0dGVybiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBvcGFjaXR5LTVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wXCIgc3R5bGU9e3tcbiAgICAgICAgICBiYWNrZ3JvdW5kSW1hZ2U6IGB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNDc3ZnIHdpZHRoPSc2MCcgaGVpZ2h0PSc2MCcgdmlld0JveD0nMCAwIDYwIDYwJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnJTNFJTNDZyBmaWxsPSdub25lJyBmaWxsLXJ1bGU9J2V2ZW5vZGQnJTNFJTNDZyBmaWxsPSclMjNmZmZmZmYnIGZpbGwtb3BhY2l0eT0nMSclM0UlM0NjaXJjbGUgY3g9JzMwJyBjeT0nMzAnIHI9JzInLyUzRSUzQy9nJTNFJTNDL2clM0UlM0Mvc3ZnJTNFXCIpYFxuICAgICAgICB9fSAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBGbG9hdGluZyBCYWNrZ3JvdW5kIEVsZW1lbnRzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMS80IHJpZ2h0LTAgdy05NiBoLTk2IG9wYWNpdHktMTBcIlxuICAgICAgICAgIGFuaW1hdGU9e3tcbiAgICAgICAgICAgIHk6IFstMzAsIDMwLCAtMzBdLFxuICAgICAgICAgICAgcm90YXRlOiBbMCwgMTAsIDBdLFxuICAgICAgICAgIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17e1xuICAgICAgICAgICAgZHVyYXRpb246IDEyLFxuICAgICAgICAgICAgcmVwZWF0OiBJbmZpbml0eSxcbiAgICAgICAgICAgIGVhc2U6IFwiZWFzZUluT3V0XCJcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgPHN2ZyB2aWV3Qm94PVwiMCAwIDQwMCA0MDBcIiBmaWxsPVwibm9uZVwiIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGxcIj5cbiAgICAgICAgICAgIDxjaXJjbGUgY3g9XCIyMDBcIiBjeT1cIjE1MFwiIHI9XCI4MFwiIGZpbGw9XCJ3aGl0ZVwiIC8+XG4gICAgICAgICAgICA8cmVjdCB4PVwiMTUwXCIgeT1cIjI1MFwiIHdpZHRoPVwiMTAwXCIgaGVpZ2h0PVwiMTAwXCIgcng9XCIyMFwiIGZpbGw9XCJ3aGl0ZVwiIHRyYW5zZm9ybT1cInJvdGF0ZSgyNSAyMDAgMzAwKVwiIC8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0xLzQgbGVmdC0wIHctNjQgaC02NCBvcGFjaXR5LThcIlxuICAgICAgICAgIGFuaW1hdGU9e3tcbiAgICAgICAgICAgIHk6IFsyMCwgLTIwLCAyMF0sXG4gICAgICAgICAgICByb3RhdGU6IFswLCAtNSwgMF0sXG4gICAgICAgICAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICBkdXJhdGlvbjogMTAsXG4gICAgICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgZWFzZTogXCJlYXNlSW5PdXRcIlxuICAgICAgICAgIH19XG4gICAgICAgID5cbiAgICAgICAgICA8c3ZnIHZpZXdCb3g9XCIwIDAgMjAwIDIwMFwiIGZpbGw9XCJub25lXCIgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbFwiPlxuICAgICAgICAgICAgPHBvbHlnb24gcG9pbnRzPVwiMTAwLDIwIDE4MCwxODAgMjAsMTgwXCIgZmlsbD1cIndoaXRlXCIgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDVEEgU2VjdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC02XCI+XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1heC13LTR4bCBteC1hdXRvXCJcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMzAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCB9fVxuICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb250LWRpc3BsYXkgdGV4dC00eGwgbWQ6dGV4dC02eGwgZm9udC1ib2xkIG1iLTYgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICBQcsOqdCDDoCB0cmFuc2Zvcm1lciB2b3RyZSB2aXNpb24gZW4gcsOpYWxpdMOpID9cbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtd2hpdGUvOTAgbWF4LXctM3hsIG14LWF1dG8gbWItMTIgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgIERpc2N1dG9ucyBkZSB2b3RyZSBwcm9qZXQgZXQgZMOpY291dnJvbnMgZW5zZW1ibGUgY29tbWVudCBub3VzIHBvdXZvbnMgY3LDqWVyIHF1ZWxxdWUgY2hvc2UgZCdleGNlcHRpb25uZWwuIENvbnN1bHRhdGlvbiBncmF0dWl0ZSwgZGV2aXMgcGVyc29ubmFsaXPDqSBzb3VzIDQ4aC5cbiAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wNSB9fVxuICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGhyZWY9XCJtYWlsdG86Y29udGFjdEBnaWJib25sYWIuY29tXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzaGltbWVyLWVmZmVjdCBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgYmctd2hpdGUgdGV4dC1wcmltYXJ5LWdyZWVuIHB4LTggcHktNCByb3VuZGVkLTJ4bCBmb250LXNlbWlib2xkIHRleHQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNoYWRvdy14bCBob3ZlcjpzaGFkb3ctYmxhY2svMjAgZ3JvdXBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgRMOpbWFycmVyIHZvdHJlIHByb2pldFxuICAgICAgICAgICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cInctNSBoLTUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZ3JvdXAtaG92ZXI6dHJhbnNsYXRlLXgtMVwiIC8+XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBGb290ZXIgU2VjdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBib3JkZXItdCBib3JkZXItd2hpdGUvMTAgcHQtMTYgcGItOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTEyIG1iLTEyXCI+XG4gICAgICAgICAgICB7LyogQnJhbmQgU2VjdGlvbiAqL31cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LW1kXCJcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtMzAgfX1cbiAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjggfX1cbiAgICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctd2hpdGUgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktZ3JlZW4gZm9udC1ib2xkIHRleHQtc21cIj5HPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtZGlzcGxheSB0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgR2liYm9uTGFiXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICBDcsOpYXRldXJzIGQnZXhww6lyaWVuY2VzIGRpZ2l0YWxlcyBleGNlcHRpb25uZWxsZXMuIE5vdXMgdHJhbnNmb3Jtb25zIHZvcyBpZMOpZXMgZW4gc29sdXRpb25zIHRlY2huaXF1ZXMgcGVyZm9ybWFudGVzIGV0IGVzdGjDqXRpcXVlcy5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgICB7LyogTGlua3MgU2VjdGlvbiAqL31cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgZ2FwLThcIlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDMwIH19XG4gICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1kaXNwbGF5IHRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIFNlcnZpY2VzXG4gICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICB7WydEw6l2ZWxvcHBlbWVudCBXZWInLCAnQXBwbGljYXRpb25zIE1vYmlsZScsICdEZXNpZ24gJiBVWC9VSScsICdCcmFuZGluZyBEaWdpdGFsJ10ubWFwKChzZXJ2aWNlKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e3NlcnZpY2V9PlxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjc2VydmljZXNcIiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzcwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzZXJ2aWNlfVxuICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtZGlzcGxheSB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgICAgICBDb250YWN0XG4gICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCJtYWlsdG86Y29udGFjdEBnaWJib25sYWIuY29tXCIgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS83MCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgY29udGFjdEBnaWJib25sYWIuY29tXG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCJ0ZWw6KzMzMTIzNDU2Nzg5XCIgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS83MCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgKzMzIDEgMjMgNDUgNjcgODlcbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzcwXCI+XG4gICAgICAgICAgICAgICAgICAgIFBhcmlzLCBGcmFuY2VcbiAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQm90dG9tIFNlY3Rpb24gKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBwdC04IGJvcmRlci10IGJvcmRlci13aGl0ZS8xMFwiXG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjggfX1cbiAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwIHRleHQtc20gbWItNCBzbTptYi0wXCI+XG4gICAgICAgICAgICAgIMKpIDIwMjQgR2liYm9uTGFiLiBDcsOpw6kgYXZlYyBwYXNzaW9uIMOgIFBhcmlzLiBUb3VzIGRyb2l0cyByw6lzZXJ2w6lzLlxuICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgICAgeyBpY29uOiBMaW5rZWRpbiwgaHJlZjogJyMnIH0sXG4gICAgICAgICAgICAgICAgeyBpY29uOiBHaXRodWIsIGhyZWY6ICcjJyB9LFxuICAgICAgICAgICAgICAgIHsgaWNvbjogVHdpdHRlciwgaHJlZjogJyMnIH1cbiAgICAgICAgICAgICAgXS5tYXAoKHsgaWNvbjogSWNvbiwgaHJlZiB9LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxtb3Rpb24uYVxuICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgIGhyZWY9e2hyZWZ9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctd2hpdGUvMTAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXdoaXRlLzcwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUvMjAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMSwgeTogLTIgfX1cbiAgICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk1IH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPEljb24gY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgPC9tb3Rpb24uYT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9zZWN0aW9uPlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IENUQUZvb3RlclxuIl0sIm5hbWVzIjpbIm1vdGlvbiIsIkFycm93UmlnaHQiLCJMaW5rZWRpbiIsIkdpdGh1YiIsIlR3aXR0ZXIiLCJMaW5rIiwiQ1RBRm9vdGVyIiwic2VjdGlvbiIsImlkIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3R5bGUiLCJiYWNrZ3JvdW5kSW1hZ2UiLCJhbmltYXRlIiwieSIsInJvdGF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsInJlcGVhdCIsIkluZmluaXR5IiwiZWFzZSIsInN2ZyIsInZpZXdCb3giLCJmaWxsIiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJyZWN0IiwieCIsIndpZHRoIiwiaGVpZ2h0IiwicngiLCJ0cmFuc2Zvcm0iLCJwb2x5Z29uIiwicG9pbnRzIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ3aGlsZUluVmlldyIsInZpZXdwb3J0Iiwib25jZSIsImgyIiwicCIsIndoaWxlSG92ZXIiLCJzY2FsZSIsIndoaWxlVGFwIiwiaHJlZiIsInNwYW4iLCJoNCIsInVsIiwibWFwIiwic2VydmljZSIsImxpIiwiaWNvbiIsImluZGV4IiwiSWNvbiIsImEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CTAFooter.tsx\n"));

/***/ })

});