"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Services.tsx":
/*!*********************************!*\
  !*** ./components/Services.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst services = [\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9veloppement Web\",\n        description: \"Applications web modernes et performantes. React, Vue.js, Node.js - nous ma\\xeetrisons les technologies de pointe pour cr\\xe9er des exp\\xe9riences utilisateur exceptionnelles et des architectures scalables.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Applications Mobile\",\n        description: \"Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie \\xe0 vos id\\xe9es sur tous les \\xe9crans.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Design & UX/UI\",\n        description: \"Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous cr\\xe9ons des interfaces intuitives qui racontent l'histoire de votre marque.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Branding Digital\",\n        description: \"Identit\\xe9s visuelles m\\xe9morables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque pour tous les supports digitaux.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Maintenance & Support\",\n        description: \"Accompagnement technique continu pour faire \\xe9voluer vos projets. Monitoring, mises \\xe0 jour, optimisations - nous veillons sur vos applications comme sur nos propres cr\\xe9ations.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Consulting Technique\",\n        description: \"Expertise strat\\xe9gique pour orienter vos d\\xe9cisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions pour votre business.\"\n    }\n];\nconst Services = ()=>{\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    // Transform scroll progress to horizontal movement\n    const x = (0,framer_motion__WEBPACK_IMPORTED_MODULE_9__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"-50%\"\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: containerRef,\n        id: \"services\",\n        className: \"relative overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-[300vh] relative\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sticky top-0 h-screen flex items-center overflow-hidden bg-bg-primary\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6 mb-16 absolute top-24 left-1/2 transform -translate-x-1/2 z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            className: \"text-center max-w-3xl mx-auto\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-display text-4xl md:text-6xl font-bold mb-6 text-text-primary\",\n                                    children: \"Nos Services\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-text-secondary leading-relaxed\",\n                                    children: \"Nous fa\\xe7onnons l'excellence en d\\xe9veloppement web, applications mobiles et design digital. Chaque projet est une œuvre d'art technique.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        style: {\n                            x\n                        },\n                        className: \"flex gap-8 px-6 md:px-12 items-center h-full\",\n                        children: services.map((service, index)=>{\n                            const Icon = service.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"flex-shrink-0 w-96 bg-white p-8 rounded-3xl border border-border-light transition-all duration-500 hover:scale-105 hover:shadow-xl hover:shadow-primary-green/10 group relative overflow-hidden\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-green to-accent-purple transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-primary-green/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-2xl flex items-center justify-center mb-6 transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-8 h-8 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-display text-2xl font-semibold mb-4 text-text-primary group-hover:text-primary-green transition-colors duration-300\",\n                                                children: service.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary leading-relaxed\",\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Services.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Services, \"EFGjTwncUbaTEiMOndSBI+vp3fM=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_8__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_9__.useTransform\n    ];\n});\n_c = Services;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Services);\nvar _c;\n$RefreshReg$(_c, \"Services\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Services.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeElement: function() { return /* binding */ resizeElement; }\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_element_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/resolve-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs\");\n\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nfunction getElementSize(target, borderBoxSize) {\n    if (borderBoxSize) {\n        const { inlineSize, blockSize } = borderBoxSize[0];\n        return { width: inlineSize, height: blockSize };\n    }\n    else if (target instanceof SVGElement && \"getBBox\" in target) {\n        return target.getBBox();\n    }\n    else {\n        return {\n            width: target.offsetWidth,\n            height: target.offsetHeight,\n        };\n    }\n}\nfunction notifyTarget({ target, contentRect, borderBoxSize, }) {\n    var _a;\n    (_a = resizeHandlers.get(target)) === null || _a === void 0 ? void 0 : _a.forEach((handler) => {\n        handler({\n            target,\n            contentSize: contentRect,\n            get size() {\n                return getElementSize(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = (0,_utils_resolve_element_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer === null || observer === void 0 ? void 0 : observer.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.delete(handler);\n            if (!(elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.size)) {\n                observer === null || observer === void 0 ? void 0 : observer.unobserve(element);\n            }\n        });\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaGFuZGxlLWVsZW1lbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStEOztBQUUvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQix3QkFBd0I7QUFDeEMsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IscUNBQXFDO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDJFQUFlO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTs7QUFFeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvZG9tL3Jlc2l6ZS9oYW5kbGUtZWxlbWVudC5tanM/NmVlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZXNvbHZlRWxlbWVudHMgfSBmcm9tICcuLi91dGlscy9yZXNvbHZlLWVsZW1lbnQubWpzJztcblxuY29uc3QgcmVzaXplSGFuZGxlcnMgPSBuZXcgV2Vha01hcCgpO1xubGV0IG9ic2VydmVyO1xuZnVuY3Rpb24gZ2V0RWxlbWVudFNpemUodGFyZ2V0LCBib3JkZXJCb3hTaXplKSB7XG4gICAgaWYgKGJvcmRlckJveFNpemUpIHtcbiAgICAgICAgY29uc3QgeyBpbmxpbmVTaXplLCBibG9ja1NpemUgfSA9IGJvcmRlckJveFNpemVbMF07XG4gICAgICAgIHJldHVybiB7IHdpZHRoOiBpbmxpbmVTaXplLCBoZWlnaHQ6IGJsb2NrU2l6ZSB9O1xuICAgIH1cbiAgICBlbHNlIGlmICh0YXJnZXQgaW5zdGFuY2VvZiBTVkdFbGVtZW50ICYmIFwiZ2V0QkJveFwiIGluIHRhcmdldCkge1xuICAgICAgICByZXR1cm4gdGFyZ2V0LmdldEJCb3goKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB3aWR0aDogdGFyZ2V0Lm9mZnNldFdpZHRoLFxuICAgICAgICAgICAgaGVpZ2h0OiB0YXJnZXQub2Zmc2V0SGVpZ2h0LFxuICAgICAgICB9O1xuICAgIH1cbn1cbmZ1bmN0aW9uIG5vdGlmeVRhcmdldCh7IHRhcmdldCwgY29udGVudFJlY3QsIGJvcmRlckJveFNpemUsIH0pIHtcbiAgICB2YXIgX2E7XG4gICAgKF9hID0gcmVzaXplSGFuZGxlcnMuZ2V0KHRhcmdldCkpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5mb3JFYWNoKChoYW5kbGVyKSA9PiB7XG4gICAgICAgIGhhbmRsZXIoe1xuICAgICAgICAgICAgdGFyZ2V0LFxuICAgICAgICAgICAgY29udGVudFNpemU6IGNvbnRlbnRSZWN0LFxuICAgICAgICAgICAgZ2V0IHNpemUoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGdldEVsZW1lbnRTaXplKHRhcmdldCwgYm9yZGVyQm94U2l6ZSk7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9KTtcbiAgICB9KTtcbn1cbmZ1bmN0aW9uIG5vdGlmeUFsbChlbnRyaWVzKSB7XG4gICAgZW50cmllcy5mb3JFYWNoKG5vdGlmeVRhcmdldCk7XG59XG5mdW5jdGlvbiBjcmVhdGVSZXNpemVPYnNlcnZlcigpIHtcbiAgICBpZiAodHlwZW9mIFJlc2l6ZU9ic2VydmVyID09PSBcInVuZGVmaW5lZFwiKVxuICAgICAgICByZXR1cm47XG4gICAgb2JzZXJ2ZXIgPSBuZXcgUmVzaXplT2JzZXJ2ZXIobm90aWZ5QWxsKTtcbn1cbmZ1bmN0aW9uIHJlc2l6ZUVsZW1lbnQodGFyZ2V0LCBoYW5kbGVyKSB7XG4gICAgaWYgKCFvYnNlcnZlcilcbiAgICAgICAgY3JlYXRlUmVzaXplT2JzZXJ2ZXIoKTtcbiAgICBjb25zdCBlbGVtZW50cyA9IHJlc29sdmVFbGVtZW50cyh0YXJnZXQpO1xuICAgIGVsZW1lbnRzLmZvckVhY2goKGVsZW1lbnQpID0+IHtcbiAgICAgICAgbGV0IGVsZW1lbnRIYW5kbGVycyA9IHJlc2l6ZUhhbmRsZXJzLmdldChlbGVtZW50KTtcbiAgICAgICAgaWYgKCFlbGVtZW50SGFuZGxlcnMpIHtcbiAgICAgICAgICAgIGVsZW1lbnRIYW5kbGVycyA9IG5ldyBTZXQoKTtcbiAgICAgICAgICAgIHJlc2l6ZUhhbmRsZXJzLnNldChlbGVtZW50LCBlbGVtZW50SGFuZGxlcnMpO1xuICAgICAgICB9XG4gICAgICAgIGVsZW1lbnRIYW5kbGVycy5hZGQoaGFuZGxlcik7XG4gICAgICAgIG9ic2VydmVyID09PSBudWxsIHx8IG9ic2VydmVyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvYnNlcnZlci5vYnNlcnZlKGVsZW1lbnQpO1xuICAgIH0pO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGVsZW1lbnRzLmZvckVhY2goKGVsZW1lbnQpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGVsZW1lbnRIYW5kbGVycyA9IHJlc2l6ZUhhbmRsZXJzLmdldChlbGVtZW50KTtcbiAgICAgICAgICAgIGVsZW1lbnRIYW5kbGVycyA9PT0gbnVsbCB8fCBlbGVtZW50SGFuZGxlcnMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGVsZW1lbnRIYW5kbGVycy5kZWxldGUoaGFuZGxlcik7XG4gICAgICAgICAgICBpZiAoIShlbGVtZW50SGFuZGxlcnMgPT09IG51bGwgfHwgZWxlbWVudEhhbmRsZXJzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBlbGVtZW50SGFuZGxlcnMuc2l6ZSkpIHtcbiAgICAgICAgICAgICAgICBvYnNlcnZlciA9PT0gbnVsbCB8fCBvYnNlcnZlciA9PT0gdm9pZCAwID8gdm9pZCAwIDogb2JzZXJ2ZXIudW5vYnNlcnZlKGVsZW1lbnQpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9O1xufVxuXG5leHBvcnQgeyByZXNpemVFbGVtZW50IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeWindow: function() { return /* binding */ resizeWindow; }\n/* harmony export */ });\nconst windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n    windowResizeHandler = () => {\n        const size = {\n            width: window.innerWidth,\n            height: window.innerHeight,\n        };\n        const info = {\n            target: window,\n            size,\n            contentSize: size,\n        };\n        windowCallbacks.forEach((callback) => callback(info));\n    };\n    window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n    windowCallbacks.add(callback);\n    if (!windowResizeHandler)\n        createWindowResizeHandler();\n    return () => {\n        windowCallbacks.delete(callback);\n        if (!windowCallbacks.size && windowResizeHandler) {\n            windowResizeHandler = undefined;\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaGFuZGxlLXdpbmRvdy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vcmVzaXplL2hhbmRsZS13aW5kb3cubWpzPzQ2NzAiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgd2luZG93Q2FsbGJhY2tzID0gbmV3IFNldCgpO1xubGV0IHdpbmRvd1Jlc2l6ZUhhbmRsZXI7XG5mdW5jdGlvbiBjcmVhdGVXaW5kb3dSZXNpemVIYW5kbGVyKCkge1xuICAgIHdpbmRvd1Jlc2l6ZUhhbmRsZXIgPSAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHNpemUgPSB7XG4gICAgICAgICAgICB3aWR0aDogd2luZG93LmlubmVyV2lkdGgsXG4gICAgICAgICAgICBoZWlnaHQ6IHdpbmRvdy5pbm5lckhlaWdodCxcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgaW5mbyA9IHtcbiAgICAgICAgICAgIHRhcmdldDogd2luZG93LFxuICAgICAgICAgICAgc2l6ZSxcbiAgICAgICAgICAgIGNvbnRlbnRTaXplOiBzaXplLFxuICAgICAgICB9O1xuICAgICAgICB3aW5kb3dDYWxsYmFja3MuZm9yRWFjaCgoY2FsbGJhY2spID0+IGNhbGxiYWNrKGluZm8pKTtcbiAgICB9O1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIHdpbmRvd1Jlc2l6ZUhhbmRsZXIpO1xufVxuZnVuY3Rpb24gcmVzaXplV2luZG93KGNhbGxiYWNrKSB7XG4gICAgd2luZG93Q2FsbGJhY2tzLmFkZChjYWxsYmFjayk7XG4gICAgaWYgKCF3aW5kb3dSZXNpemVIYW5kbGVyKVxuICAgICAgICBjcmVhdGVXaW5kb3dSZXNpemVIYW5kbGVyKCk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgd2luZG93Q2FsbGJhY2tzLmRlbGV0ZShjYWxsYmFjayk7XG4gICAgICAgIGlmICghd2luZG93Q2FsbGJhY2tzLnNpemUgJiYgd2luZG93UmVzaXplSGFuZGxlcikge1xuICAgICAgICAgICAgd2luZG93UmVzaXplSGFuZGxlciA9IHVuZGVmaW5lZDtcbiAgICAgICAgfVxuICAgIH07XG59XG5cbmV4cG9ydCB7IHJlc2l6ZVdpbmRvdyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resize: function() { return /* binding */ resize; }\n/* harmony export */ });\n/* harmony import */ var _handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./handle-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs\");\n/* harmony import */ var _handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handle-window.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs\");\n\n\n\nfunction resize(a, b) {\n    return typeof a === \"function\" ? (0,_handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__.resizeWindow)(a) : (0,_handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__.resizeElement)(a, b);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUNGOztBQUVuRDtBQUNBLHFDQUFxQyxnRUFBWSxNQUFNLGtFQUFhO0FBQ3BFOztBQUVrQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vcmVzaXplL2luZGV4Lm1qcz9hYWNlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlc2l6ZUVsZW1lbnQgfSBmcm9tICcuL2hhbmRsZS1lbGVtZW50Lm1qcyc7XG5pbXBvcnQgeyByZXNpemVXaW5kb3cgfSBmcm9tICcuL2hhbmRsZS13aW5kb3cubWpzJztcblxuZnVuY3Rpb24gcmVzaXplKGEsIGIpIHtcbiAgICByZXR1cm4gdHlwZW9mIGEgPT09IFwiZnVuY3Rpb25cIiA/IHJlc2l6ZVdpbmRvdyhhKSA6IHJlc2l6ZUVsZW1lbnQoYSwgYik7XG59XG5cbmV4cG9ydCB7IHJlc2l6ZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createScrollInfo: function() { return /* binding */ createScrollInfo; },\n/* harmony export */   updateScrollInfo: function() { return /* binding */ updateScrollInfo; }\n/* harmony export */ });\n/* harmony import */ var _utils_progress_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/progress.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/progress.mjs\");\n/* harmony import */ var _utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/velocity-per-second.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs\");\n\n\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[\"scroll\" + position];\n    axis.scrollLength = element[\"scroll\" + length] - element[\"client\" + length];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = (0,_utils_progress_mjs__WEBPACK_IMPORTED_MODULE_0__.progress)(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed\n            ? 0\n            : (0,_utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_1__.velocityPerSecond)(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvaW5mby5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF1RDtBQUNvQjs7QUFFM0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLFlBQVksbUJBQW1CO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDZEQUFRO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxpRkFBaUI7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vc2Nyb2xsL2luZm8ubWpzPzhiNTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcHJvZ3Jlc3MgfSBmcm9tICcuLi8uLi8uLi91dGlscy9wcm9ncmVzcy5tanMnO1xuaW1wb3J0IHsgdmVsb2NpdHlQZXJTZWNvbmQgfSBmcm9tICcuLi8uLi8uLi91dGlscy92ZWxvY2l0eS1wZXItc2Vjb25kLm1qcyc7XG5cbi8qKlxuICogQSB0aW1lIGluIG1pbGxpc2Vjb25kcywgYmV5b25kIHdoaWNoIHdlIGNvbnNpZGVyIHRoZSBzY3JvbGwgdmVsb2NpdHkgdG8gYmUgMC5cbiAqL1xuY29uc3QgbWF4RWxhcHNlZCA9IDUwO1xuY29uc3QgY3JlYXRlQXhpc0luZm8gPSAoKSA9PiAoe1xuICAgIGN1cnJlbnQ6IDAsXG4gICAgb2Zmc2V0OiBbXSxcbiAgICBwcm9ncmVzczogMCxcbiAgICBzY3JvbGxMZW5ndGg6IDAsXG4gICAgdGFyZ2V0T2Zmc2V0OiAwLFxuICAgIHRhcmdldExlbmd0aDogMCxcbiAgICBjb250YWluZXJMZW5ndGg6IDAsXG4gICAgdmVsb2NpdHk6IDAsXG59KTtcbmNvbnN0IGNyZWF0ZVNjcm9sbEluZm8gPSAoKSA9PiAoe1xuICAgIHRpbWU6IDAsXG4gICAgeDogY3JlYXRlQXhpc0luZm8oKSxcbiAgICB5OiBjcmVhdGVBeGlzSW5mbygpLFxufSk7XG5jb25zdCBrZXlzID0ge1xuICAgIHg6IHtcbiAgICAgICAgbGVuZ3RoOiBcIldpZHRoXCIsXG4gICAgICAgIHBvc2l0aW9uOiBcIkxlZnRcIixcbiAgICB9LFxuICAgIHk6IHtcbiAgICAgICAgbGVuZ3RoOiBcIkhlaWdodFwiLFxuICAgICAgICBwb3NpdGlvbjogXCJUb3BcIixcbiAgICB9LFxufTtcbmZ1bmN0aW9uIHVwZGF0ZUF4aXNJbmZvKGVsZW1lbnQsIGF4aXNOYW1lLCBpbmZvLCB0aW1lKSB7XG4gICAgY29uc3QgYXhpcyA9IGluZm9bYXhpc05hbWVdO1xuICAgIGNvbnN0IHsgbGVuZ3RoLCBwb3NpdGlvbiB9ID0ga2V5c1theGlzTmFtZV07XG4gICAgY29uc3QgcHJldiA9IGF4aXMuY3VycmVudDtcbiAgICBjb25zdCBwcmV2VGltZSA9IGluZm8udGltZTtcbiAgICBheGlzLmN1cnJlbnQgPSBlbGVtZW50W1wic2Nyb2xsXCIgKyBwb3NpdGlvbl07XG4gICAgYXhpcy5zY3JvbGxMZW5ndGggPSBlbGVtZW50W1wic2Nyb2xsXCIgKyBsZW5ndGhdIC0gZWxlbWVudFtcImNsaWVudFwiICsgbGVuZ3RoXTtcbiAgICBheGlzLm9mZnNldC5sZW5ndGggPSAwO1xuICAgIGF4aXMub2Zmc2V0WzBdID0gMDtcbiAgICBheGlzLm9mZnNldFsxXSA9IGF4aXMuc2Nyb2xsTGVuZ3RoO1xuICAgIGF4aXMucHJvZ3Jlc3MgPSBwcm9ncmVzcygwLCBheGlzLnNjcm9sbExlbmd0aCwgYXhpcy5jdXJyZW50KTtcbiAgICBjb25zdCBlbGFwc2VkID0gdGltZSAtIHByZXZUaW1lO1xuICAgIGF4aXMudmVsb2NpdHkgPVxuICAgICAgICBlbGFwc2VkID4gbWF4RWxhcHNlZFxuICAgICAgICAgICAgPyAwXG4gICAgICAgICAgICA6IHZlbG9jaXR5UGVyU2Vjb25kKGF4aXMuY3VycmVudCAtIHByZXYsIGVsYXBzZWQpO1xufVxuZnVuY3Rpb24gdXBkYXRlU2Nyb2xsSW5mbyhlbGVtZW50LCBpbmZvLCB0aW1lKSB7XG4gICAgdXBkYXRlQXhpc0luZm8oZWxlbWVudCwgXCJ4XCIsIGluZm8sIHRpbWUpO1xuICAgIHVwZGF0ZUF4aXNJbmZvKGVsZW1lbnQsIFwieVwiLCBpbmZvLCB0aW1lKTtcbiAgICBpbmZvLnRpbWUgPSB0aW1lO1xufVxuXG5leHBvcnQgeyBjcmVhdGVTY3JvbGxJbmZvLCB1cGRhdGVTY3JvbGxJbmZvIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   namedEdges: function() { return /* binding */ namedEdges; },\n/* harmony export */   resolveEdge: function() { return /* binding */ resolveEdge; }\n/* harmony export */ });\nconst namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (namedEdges[edge] !== undefined) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffsets: function() { return /* binding */ resolveOffsets; }\n/* harmony export */ });\n/* harmony import */ var _inset_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./inset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\");\n/* harmony import */ var _presets_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./presets.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\");\n/* harmony import */ var _offset_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\");\n/* harmony import */ var _utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../utils/interpolate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\");\n/* harmony import */ var _utils_offsets_default_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../utils/offsets/default.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/default.mjs\");\n\n\n\n\n\n\nconst point = { x: 0, y: 0 };\nfunction getTargetSize(target) {\n    return \"getBBox\" in target && target.tagName !== \"svg\"\n        ? target.getBBox()\n        : { width: target.clientWidth, height: target.clientHeight };\n}\nfunction resolveOffsets(container, info, options) {\n    let { offset: offsetDefinition = _presets_mjs__WEBPACK_IMPORTED_MODULE_0__.ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset = target !== container ? (0,_inset_mjs__WEBPACK_IMPORTED_MODULE_1__.calcInset)(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : getTargetSize(target);\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset = (0,_offset_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffset)(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n        if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = (0,_utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_3__.interpolate)(info[axis].offset, (0,_utils_offsets_default_mjs__WEBPACK_IMPORTED_MODULE_4__.defaultOffset)(offsetDefinition));\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = info[axis].interpolate(info[axis].current);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcInset: function() { return /* binding */ calcInset; }\n/* harmony export */ });\nfunction calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if (current instanceof HTMLElement) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffset: function() { return /* binding */ resolveOffset; }\n/* harmony export */ });\n/* harmony import */ var _edge_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edge.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\");\n\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (typeof offset === \"number\") {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (typeof offset === \"string\") {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [offset, _edge_mjs__WEBPACK_IMPORTED_MODULE_0__.namedEdges[offset] ? offset : `0`];\n        }\n    }\n    targetPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollOffset: function() { return /* binding */ ScrollOffset; }\n/* harmony export */ });\nconst ScrollOffset = {\n    Enter: [\n        [0, 1],\n        [1, 1],\n    ],\n    Exit: [\n        [0, 0],\n        [1, 0],\n    ],\n    Any: [\n        [1, 0],\n        [0, 1],\n    ],\n    All: [\n        [0, 0],\n        [1, 1],\n    ],\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2Zmc2V0cy9wcmVzZXRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vc2Nyb2xsL29mZnNldHMvcHJlc2V0cy5tanM/MDUyYiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBTY3JvbGxPZmZzZXQgPSB7XG4gICAgRW50ZXI6IFtcbiAgICAgICAgWzAsIDFdLFxuICAgICAgICBbMSwgMV0sXG4gICAgXSxcbiAgICBFeGl0OiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDBdLFxuICAgIF0sXG4gICAgQW55OiBbXG4gICAgICAgIFsxLCAwXSxcbiAgICAgICAgWzAsIDFdLFxuICAgIF0sXG4gICAgQWxsOiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDFdLFxuICAgIF0sXG59O1xuXG5leHBvcnQgeyBTY3JvbGxPZmZzZXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOnScrollHandler: function() { return /* binding */ createOnScrollHandler; }\n/* harmony export */ });\n/* harmony import */ var _utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/warn-once.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offsets/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\");\n\n\n\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node !== container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n    /**\n     * In development mode ensure scroll containers aren't position: static as this makes\n     * it difficult to measure their relative positions.\n     */\n    if (true) {\n        if (container && target && target !== container) {\n            (0,_utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_0__.warnOnce)(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n        }\n    }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n    return {\n        measure: () => measure(element, options.target, info),\n        update: (time) => {\n            (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.updateScrollInfo)(element, info, time);\n            if (options.offset || options.target) {\n                (0,_offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffsets)(element, info, options);\n            }\n        },\n        notify: () => onScroll(info),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrollInfo: function() { return /* binding */ scrollInfo; }\n/* harmony export */ });\n/* harmony import */ var _resize_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../resize/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./on-scroll-handler.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.documentElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.documentElement, ...options } = {}) {\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = (0,_info_mjs__WEBPACK_IMPORTED_MODULE_0__.createScrollInfo)();\n    const containerHandler = (0,_on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_1__.createOnScrollHandler)(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers)\n                handler.measure();\n        };\n        const updateAll = () => {\n            for (const handler of containerHandlers) {\n                handler.update(_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frameData.timestamp);\n            }\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers)\n                handler.notify();\n        };\n        const listener = () => {\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(measureAll, false, true);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(updateAll, false, true);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.update(notifyAll, false, true);\n        };\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, (0,_resize_index_mjs__WEBPACK_IMPORTED_MODULE_3__.resize)(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n    }\n    const listener = scrollListeners.get(container);\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(listener, false, true);\n    return () => {\n        var _a;\n        (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/transform.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/transform.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transform: function() { return /* binding */ transform; }\n/* harmony export */ });\n/* harmony import */ var _interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interpolate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\");\n\n\nconst isCustomValueType = (v) => {\n    return v && typeof v === \"object\" && v.mix;\n};\nconst getMixer = (v) => (isCustomValueType(v) ? v.mix : undefined);\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = (0,_interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__.interpolate)(inputRange, outputRange, {\n        mixer: getMixer(outputRange[0]),\n        ...options,\n    });\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5Qiw2REFBVztBQUNwQztBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRXFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcz9lYzJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludGVycG9sYXRlIH0gZnJvbSAnLi9pbnRlcnBvbGF0ZS5tanMnO1xuXG5jb25zdCBpc0N1c3RvbVZhbHVlVHlwZSA9ICh2KSA9PiB7XG4gICAgcmV0dXJuIHYgJiYgdHlwZW9mIHYgPT09IFwib2JqZWN0XCIgJiYgdi5taXg7XG59O1xuY29uc3QgZ2V0TWl4ZXIgPSAodikgPT4gKGlzQ3VzdG9tVmFsdWVUeXBlKHYpID8gdi5taXggOiB1bmRlZmluZWQpO1xuZnVuY3Rpb24gdHJhbnNmb3JtKC4uLmFyZ3MpIHtcbiAgICBjb25zdCB1c2VJbW1lZGlhdGUgPSAhQXJyYXkuaXNBcnJheShhcmdzWzBdKTtcbiAgICBjb25zdCBhcmdPZmZzZXQgPSB1c2VJbW1lZGlhdGUgPyAwIDogLTE7XG4gICAgY29uc3QgaW5wdXRWYWx1ZSA9IGFyZ3NbMCArIGFyZ09mZnNldF07XG4gICAgY29uc3QgaW5wdXRSYW5nZSA9IGFyZ3NbMSArIGFyZ09mZnNldF07XG4gICAgY29uc3Qgb3V0cHV0UmFuZ2UgPSBhcmdzWzIgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IG9wdGlvbnMgPSBhcmdzWzMgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IGludGVycG9sYXRvciA9IGludGVycG9sYXRlKGlucHV0UmFuZ2UsIG91dHB1dFJhbmdlLCB7XG4gICAgICAgIG1peGVyOiBnZXRNaXhlcihvdXRwdXRSYW5nZVswXSksXG4gICAgICAgIC4uLm9wdGlvbnMsXG4gICAgfSk7XG4gICAgcmV0dXJuIHVzZUltbWVkaWF0ZSA/IGludGVycG9sYXRvcihpbnB1dFZhbHVlKSA6IGludGVycG9sYXRvcjtcbn1cblxuZXhwb3J0IHsgdHJhbnNmb3JtIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-combine-values.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCombineMotionValues: function() { return /* binding */ useCombineMotionValues; }\n/* harmony export */ });\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.useMotionValue)(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        const scheduleUpdate = () => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.update(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(updateValue);\n        };\n    });\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-computed.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComputed: function() { return /* binding */ useComputed; }\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n\n\n\nfunction useComputed(compute) {\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * will be saved into this array.\n     */\n    _index_mjs__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = [];\n    compute();\n    const value = (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__.useCombineMotionValues)(_index_mjs__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current, compute);\n    /**\n     * Synchronously close session of collectMotionValues.\n     */\n    _index_mjs__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = undefined;\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLWNvbXB1dGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0Q7QUFDZ0I7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJEQUFtQjtBQUN2QjtBQUNBLGtCQUFrQiwrRUFBc0IsQ0FBQywyREFBbUI7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyREFBbUI7QUFDdkI7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS91c2UtY29tcHV0ZWQubWpzP2ZkNDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29sbGVjdE1vdGlvblZhbHVlcyB9IGZyb20gJy4vaW5kZXgubWpzJztcbmltcG9ydCB7IHVzZUNvbWJpbmVNb3Rpb25WYWx1ZXMgfSBmcm9tICcuL3VzZS1jb21iaW5lLXZhbHVlcy5tanMnO1xuXG5mdW5jdGlvbiB1c2VDb21wdXRlZChjb21wdXRlKSB7XG4gICAgLyoqXG4gICAgICogT3BlbiBzZXNzaW9uIG9mIGNvbGxlY3RNb3Rpb25WYWx1ZXMuIEFueSBNb3Rpb25WYWx1ZSB0aGF0IGNhbGxzIGdldCgpXG4gICAgICogd2lsbCBiZSBzYXZlZCBpbnRvIHRoaXMgYXJyYXkuXG4gICAgICovXG4gICAgY29sbGVjdE1vdGlvblZhbHVlcy5jdXJyZW50ID0gW107XG4gICAgY29tcHV0ZSgpO1xuICAgIGNvbnN0IHZhbHVlID0gdXNlQ29tYmluZU1vdGlvblZhbHVlcyhjb2xsZWN0TW90aW9uVmFsdWVzLmN1cnJlbnQsIGNvbXB1dGUpO1xuICAgIC8qKlxuICAgICAqIFN5bmNocm9ub3VzbHkgY2xvc2Ugc2Vzc2lvbiBvZiBjb2xsZWN0TW90aW9uVmFsdWVzLlxuICAgICAqL1xuICAgIGNvbGxlY3RNb3Rpb25WYWx1ZXMuY3VycmVudCA9IHVuZGVmaW5lZDtcbiAgICByZXR1cm4gdmFsdWU7XG59XG5cbmV4cG9ydCB7IHVzZUNvbXB1dGVkIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-motion-value.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionValue: function() { return /* binding */ useMotionValue; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n\n\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(() => (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-scroll.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useScroll: function() { return /* binding */ useScroll; }\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _render_dom_scroll_track_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../render/dom/scroll/track.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\n\n\n\n\nfunction refWarning(name, ref) {\n    (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_1__.warning)(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollY: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollXProgress: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollYProgress: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__.useConstant)(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__.useIsomorphicLayoutEffect\n        : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return (0,_render_dom_scroll_track_mjs__WEBPACK_IMPORTED_MODULE_5__.scrollInfo)(({ x, y }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n            target: (target === null || target === void 0 ? void 0 : target.current) || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-transform.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransform: function() { return /* binding */ useTransform; }\n/* harmony export */ });\n/* harmony import */ var _utils_transform_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/transform.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-computed.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\");\n\n\n\n\n\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n    if (typeof input === \"function\") {\n        return (0,_use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__.useComputed)(input);\n    }\n    const transformer = typeof inputRangeOrTransformer === \"function\"\n        ? inputRangeOrTransformer\n        : (0,_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_1__.transform)(inputRangeOrTransformer, outputRange, options);\n    return Array.isArray(input)\n        ? useListTransform(input, transformer)\n        : useListTransform([input], ([latest]) => transformer(latest));\n}\nfunction useListTransform(values, transformer) {\n    const latest = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(() => []);\n    return (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__.useCombineMotionValues)(values, () => {\n        latest.length = 0;\n        const numValues = values.length;\n        for (let i = 0; i < numValues; i++) {\n            latest[i] = values[i].get();\n        }\n        return transformer(latest);\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\n"));

/***/ })

});