"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Process.tsx":
/*!********************************!*\
  !*** ./components/Process.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst processSteps = [\n    {\n        number: \"01\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9couverte & Strat\\xe9gie\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        number: \"02\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Design & Prototypage\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        number: \"03\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"D\\xe9veloppement Agile\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        number: \"04\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Lancement & Optimisation\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nconst Process = ()=>{\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    // Transform scroll progress to horizontal movement (reverse direction)\n    const x = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"30%\"\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: containerRef,\n        id: \"process\",\n        className: \"relative overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-[300vh] relative\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sticky top-0 h-screen flex items-center overflow-hidden bg-bg-secondary\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6 mb-16 absolute top-24 left-1/2 transform -translate-x-1/2 z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            className: \"text-center max-w-3xl mx-auto\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-display text-4xl md:text-6xl font-bold mb-6 text-text-primary\",\n                                    children: \"Notre Processus\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-text-secondary leading-relaxed\",\n                                    children: \"Une m\\xe9thodologie \\xe9prouv\\xe9e qui transforme vos id\\xe9es en solutions digitales performantes, \\xe9tape par \\xe9tape.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        style: {\n                            x\n                        },\n                        className: \"flex gap-12 px-6 md:px-12 items-center h-full\",\n                        children: processSteps.map((step, index)=>{\n                            const Icon = step.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                className: \"flex-shrink-0 w-80 text-center group\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: \"w-24 h-24 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:shadow-xl group-hover:shadow-primary-green/25 transition-all duration-300\",\n                                                whileHover: {\n                                                    scale: 1.1,\n                                                    rotate: 5\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-display text-3xl font-bold text-white\",\n                                                    children: step.number\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: \"absolute -top-2 -right-2 w-14 h-14 bg-white rounded-full flex items-center justify-center shadow-md border border-border-light group-hover:scale-110 transition-transform duration-300\",\n                                                whileHover: {\n                                                    rotate: -5\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-7 h-7 text-primary-green\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-display text-2xl font-semibold mb-4 text-text-primary group-hover:text-primary-green transition-colors duration-300\",\n                                        children: step.title\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-text-secondary leading-relaxed\",\n                                        children: step.description\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Process.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Process, \"EFGjTwncUbaTEiMOndSBI+vp3fM=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_6__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_7__.useTransform\n    ];\n});\n_c = Process;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Process);\nvar _c;\n$RefreshReg$(_c, \"Process\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Process.tsx\n"));

/***/ })

});