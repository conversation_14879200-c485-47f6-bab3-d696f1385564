"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ProcessScroll.tsx":
/*!**************************************!*\
  !*** ./components/ProcessScroll.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useScrollAnimation */ \"(app-pages-browser)/./hooks/useScrollAnimation.ts\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst processSteps = [\n    {\n        number: \"01\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9couverte & Strat\\xe9gie\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        number: \"02\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Design & Prototypage\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        number: \"03\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"D\\xe9veloppement Agile\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        number: \"04\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Lancement & Optimisation\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nconst ProcessScroll = ()=>{\n    _s();\n    const titleAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"scroll-section relative flex items-center justify-center bg-bg-primary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary\",\n                                children: [\n                                    \"Notre\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Processus\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed\",\n                                children: \"Une m\\xe9thodologie \\xe9prouv\\xe9e qui transforme vos id\\xe9es en solutions digitales performantes, \\xe9tape par \\xe9tape.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            processSteps.map((step, index)=>{\n                const Icon = step.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"scroll-section relative flex items-center justify-center bg-bg-secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        className: \"flex justify-center lg:justify-start order-2 lg:order-1\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.2\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-display text-4xl font-bold text-white\",\n                                                        children: step.number\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-8 h-8 text-primary-green\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        className: \"text-center lg:text-left order-1 lg:order-2\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary\",\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                                children: step.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2\",\n                            children: processSteps.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(i === index ? \"bg-primary-green w-8\" : \"bg-text-muted\")\n                                }, i, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, undefined);\n            })\n        ]\n    }, void 0, true);\n};\n_s(ProcessScroll, \"vfFimljMtrWcNywNf5H9nqed+mk=\", false, function() {\n    return [\n        _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation\n    ];\n});\n_c = ProcessScroll;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProcessScroll);\nvar _c;\n$RefreshReg$(_c, \"ProcessScroll\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ProcessScroll.tsx\n"));

/***/ })

});