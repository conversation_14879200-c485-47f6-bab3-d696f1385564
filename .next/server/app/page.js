/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/home/<USER>/Documents/projects/gibbonlab.com/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp%2Fpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp%2Fpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnJpY2slMkZEb2N1bWVudHMlMkZwcm9qZWN0cyUyRmdpYmJvbmxhYi5jb20lMkZhcHAlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naWJib25sYWItd2Vic2l0ZS8/NjQxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3JpY2svRG9jdW1lbnRzL3Byb2plY3RzL2dpYmJvbmxhYi5jb20vYXBwL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Space_Grotesk%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-space-grotesk%22%7D%5D%2C%22variableName%22%3A%22spaceGrotesk%22%7D&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp%2Fglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Space_Grotesk%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-space-grotesk%22%7D%5D%2C%22variableName%22%3A%22spaceGrotesk%22%7D&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp%2Fglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"(ssr)/./components/Hero.tsx\");\n/* harmony import */ var _components_ServicesScroll__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ServicesScroll */ \"(ssr)/./components/ServicesScroll.tsx\");\n/* harmony import */ var _components_ProcessScroll__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProcessScroll */ \"(ssr)/./components/ProcessScroll.tsx\");\n/* harmony import */ var _components_CTAFooter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CTAFooter */ \"(ssr)/./components/CTAFooter.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"smooth-scroll-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServicesScroll__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProcessScroll__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CTAFooter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR3dDO0FBQ0o7QUFDb0I7QUFDRjtBQUNSO0FBRS9CLFNBQVNLO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ1AsMERBQU1BOzs7OzswQkFDUCw4REFBQ0Msd0RBQUlBOzs7OzswQkFDTCw4REFBQ0Msa0VBQWNBOzs7OzswQkFDZiw4REFBQ0MsaUVBQWFBOzs7OzswQkFDZCw4REFBQ0MsNkRBQVNBOzs7Ozs7Ozs7OztBQUdoQiIsInNvdXJjZXMiOlsid2VicGFjazovL2dpYmJvbmxhYi13ZWJzaXRlLy4vYXBwL3BhZ2UudHN4Pzc2MDMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXG5pbXBvcnQgSGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9IZWFkZXInXG5pbXBvcnQgSGVybyBmcm9tICdAL2NvbXBvbmVudHMvSGVybydcbmltcG9ydCBTZXJ2aWNlc1Njcm9sbCBmcm9tICdAL2NvbXBvbmVudHMvU2VydmljZXNTY3JvbGwnXG5pbXBvcnQgUHJvY2Vzc1Njcm9sbCBmcm9tICdAL2NvbXBvbmVudHMvUHJvY2Vzc1Njcm9sbCdcbmltcG9ydCBDVEFGb290ZXIgZnJvbSAnQC9jb21wb25lbnRzL0NUQUZvb3RlcidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNtb290aC1zY3JvbGwtY29udGFpbmVyXCI+XG4gICAgICA8SGVhZGVyIC8+XG4gICAgICA8SGVybyAvPlxuICAgICAgPFNlcnZpY2VzU2Nyb2xsIC8+XG4gICAgICA8UHJvY2Vzc1Njcm9sbCAvPlxuICAgICAgPENUQUZvb3RlciAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiSGVhZGVyIiwiSGVybyIsIlNlcnZpY2VzU2Nyb2xsIiwiUHJvY2Vzc1Njcm9sbCIsIkNUQUZvb3RlciIsIkhvbWUiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/CTAFooter.tsx":
/*!**********************************!*\
  !*** ./components/CTAFooter.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Github,Linkedin,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Github,Linkedin,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Github,Linkedin,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Github,Linkedin,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CTAFooter = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        className: \"scroll-section relative bg-gradient-to-br from-primary-green to-primary-green-dark overflow-hidden flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n                    }\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-1/4 right-0 w-96 h-96 opacity-10\",\n                        animate: {\n                            y: [\n                                -30,\n                                30,\n                                -30\n                            ],\n                            rotate: [\n                                0,\n                                10,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 12,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            viewBox: \"0 0 400 400\",\n                            fill: \"none\",\n                            className: \"w-full h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"200\",\n                                    cy: \"150\",\n                                    r: \"80\",\n                                    fill: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                    x: \"150\",\n                                    y: \"250\",\n                                    width: \"100\",\n                                    height: \"100\",\n                                    rx: \"20\",\n                                    fill: \"white\",\n                                    transform: \"rotate(25 200 300)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute bottom-1/4 left-0 w-64 h-64 opacity-8\",\n                        animate: {\n                            y: [\n                                20,\n                                -20,\n                                20\n                            ],\n                            rotate: [\n                                0,\n                                -5,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 10,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            viewBox: \"0 0 200 200\",\n                            fill: \"none\",\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                points: \"100,20 180,180 20,180\",\n                                fill: \"white\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex-1 flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"text-center max-w-4xl mx-auto\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl font-bold mb-6 text-white\",\n                                children: \"Pr\\xeat \\xe0 transformer votre vision en r\\xe9alit\\xe9 ?\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-white/90 max-w-3xl mx-auto mb-12 leading-relaxed\",\n                                children: \"Discutons de votre projet et d\\xe9couvrons ensemble comment nous pouvons cr\\xe9er quelque chose d'exceptionnel. Consultation gratuite, devis personnalis\\xe9 sous 48h.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"shimmer-effect inline-flex items-center gap-3 bg-white text-primary-green px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 hover:shadow-xl hover:shadow-black/20 group\",\n                                    children: [\n                                        \"D\\xe9marrer votre projet\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5 transition-transform group-hover:translate-x-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 border-t border-white/10 pt-16 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-12 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"max-w-md\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-white rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-primary-green font-bold text-sm\",\n                                                        children: \"G\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-display text-2xl font-bold text-white\",\n                                                    children: \"GibbonLab\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 leading-relaxed\",\n                                            children: \"Cr\\xe9ateurs d'exp\\xe9riences digitales exceptionnelles. Nous transformons vos id\\xe9es en solutions techniques performantes et esth\\xe9tiques.\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-8\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-display text-lg font-semibold text-white mb-4\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        \"D\\xe9veloppement Web\",\n                                                        \"Applications Mobile\",\n                                                        \"Design & UX/UI\",\n                                                        \"Branding Digital\"\n                                                    ].map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                href: \"#services\",\n                                                                className: \"text-white/70 hover:text-white transition-colors\",\n                                                                children: service\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, service, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-display text-lg font-semibold text-white mb-4\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                className: \"text-white/70 hover:text-white transition-colors\",\n                                                                children: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                href: \"tel:+33123456789\",\n                                                                className: \"text-white/70 hover:text-white transition-colors\",\n                                                                children: \"+33 1 23 45 67 89\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-white/70\",\n                                                            children: \"Paris, France\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"flex flex-col sm:flex-row justify-between items-center pt-8 border-t border-white/10\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-sm mb-4 sm:mb-0\",\n                                    children: \"\\xa9 2024 GibbonLab. Cr\\xe9\\xe9 avec passion \\xe0 Paris. Tous droits r\\xe9serv\\xe9s.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        {\n                                            icon: _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                            href: \"#\"\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                            href: \"#\"\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_ArrowRight_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                            href: \"#\"\n                                        }\n                                    ].map(({ icon: Icon, href }, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                            href: href,\n                                            className: \"w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-white/70 hover:text-white hover:bg-white/20 transition-all duration-300\",\n                                            whileHover: {\n                                                scale: 1.1,\n                                                y: -2\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/CTAFooter.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CTAFooter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/CTAFooter.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Header = ()=>{\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 100);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.header, {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? \"py-3 bg-white/98 backdrop-blur-xl shadow-lg\" : \"py-5 bg-white/95 backdrop-blur-lg\"} border-b border-border-light`,\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        transition: {\n            duration: 0.6,\n            ease: \"easeOut\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex items-center gap-2 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center transition-transform group-hover:scale-105 group-hover:rotate-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-sm\",\n                                    children: \"G\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-display text-2xl font-bold text-primary-green transition-transform group-hover:scale-105\",\n                                children: \"GibbonLab\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center gap-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mr-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"#\",\n                                        className: \"px-3 py-1.5 rounded-md text-sm font-semibold bg-primary-green text-white\",\n                                        children: \"FR\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"#\",\n                                        className: \"px-3 py-1.5 rounded-md text-sm font-semibold text-text-muted hover:bg-bg-accent hover:text-text-secondary transition-colors\",\n                                        children: \"EN\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined),\n                            [\n                                \"Nos Services\",\n                                \"Notre Expertise\",\n                                \"Notre Processus\",\n                                \"Contact\"\n                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: `#${item.toLowerCase().replace(/\\s+/g, \"-\").replace(\"notre-\", \"\").replace(\"nos-\", \"\")}`,\n                                    className: \"relative text-text-secondary hover:text-primary-green font-medium transition-colors group\",\n                                    children: [\n                                        item,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute bottom-0 left-0 w-0 h-0.5 bg-primary-green transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, item, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#contact\",\n                                className: \"shimmer-effect bg-gradient-to-r from-primary-green to-primary-green-dark text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-primary-green/25\",\n                                children: \"D\\xe9marrer un projet\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Hero = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"scroll-section relative flex items-center justify-center overflow-hidden bg-gradient-to-br from-bg-primary to-bg-secondary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-[0.03]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%237DD3AE' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n                    }\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-1/4 right-0 w-96 h-96 opacity-10\",\n                        animate: {\n                            y: [\n                                -20,\n                                20,\n                                -20\n                            ],\n                            rotate: [\n                                0,\n                                5,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 8,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            viewBox: \"0 0 400 400\",\n                            fill: \"none\",\n                            className: \"w-full h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                        id: \"heroGradient1\",\n                                        x1: \"0%\",\n                                        y1: \"0%\",\n                                        x2: \"100%\",\n                                        y2: \"100%\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                offset: \"0%\",\n                                                stopColor: \"#7DD3AE\",\n                                                stopOpacity: \"0.8\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                offset: \"100%\",\n                                                stopColor: \"#5BC192\",\n                                                stopOpacity: \"0.4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"200\",\n                                    cy: \"150\",\n                                    r: \"80\",\n                                    fill: \"url(#heroGradient1)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                    x: \"150\",\n                                    y: \"250\",\n                                    width: \"100\",\n                                    height: \"100\",\n                                    rx: \"20\",\n                                    fill: \"url(#heroGradient1)\",\n                                    transform: \"rotate(25 200 300)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-1/3 left-0 w-64 h-64 opacity-8\",\n                        animate: {\n                            y: [\n                                20,\n                                -20,\n                                20\n                            ],\n                            rotate: [\n                                0,\n                                -3,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 6,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            viewBox: \"0 0 200 200\",\n                            fill: \"none\",\n                            className: \"w-full h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                        id: \"heroGradient2\",\n                                        x1: \"0%\",\n                                        y1: \"0%\",\n                                        x2: \"100%\",\n                                        y2: \"100%\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                offset: \"0%\",\n                                                stopColor: \"#8B5CF6\",\n                                                stopOpacity: \"0.6\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                offset: \"100%\",\n                                                stopColor: \"#F97316\",\n                                                stopOpacity: \"0.3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"100\",\n                                    cy: \"100\",\n                                    r: \"60\",\n                                    fill: \"url(#heroGradient2)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"text-center max-w-4xl mx-auto\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h1, {\n                            className: \"font-display text-5xl md:text-7xl lg:text-8xl font-extrabold leading-tight mb-6\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            children: [\n                                \"Nous cr\\xe9ons des\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"highlight-effect gradient-text\",\n                                    children: \"applications exceptionnelles\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" \",\n                                \"qui transforment vos id\\xe9es en succ\\xe8s\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                            className: \"text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto mb-12 leading-relaxed\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.6\n                            },\n                            children: \"De la conception au d\\xe9veloppement, nous accompagnons les entreprises ambitieuses dans la cr\\xe9ation d'exp\\xe9riences digitales m\\xe9morables. Design \\xe9l\\xe9gant, code robuste, r\\xe9sultats mesurables.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"#contact\",\n                                className: \"shimmer-effect inline-flex items-center gap-3 bg-gradient-to-r from-primary-green to-primary-green-dark text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary-green/25 group\",\n                                children: [\n                                    \"D\\xe9marrer votre projet\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5 transition-transform group-hover:translate-x-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Hero.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ProcessScroll.tsx":
/*!**************************************!*\
  !*** ./components/ProcessScroll.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useScrollAnimation */ \"(ssr)/./hooks/useScrollAnimation.ts\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Palette,Rocket,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst processSteps = [\n    {\n        number: \"01\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9couverte & Strat\\xe9gie\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        number: \"02\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Design & Prototypage\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        number: \"03\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"D\\xe9veloppement Agile\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        number: \"04\",\n        icon: _barrel_optimize_names_Code_Palette_Rocket_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Lancement & Optimisation\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nconst ProcessScroll = ()=>{\n    const titleAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"scroll-section relative flex items-center justify-center bg-bg-primary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: titleAnimation.ref,\n                        className: `scroll-fade-in ${titleAnimation.isVisible ? \"visible\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary\",\n                                children: [\n                                    \"Notre\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Processus\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed\",\n                                children: \"Une m\\xe9thodologie \\xe9prouv\\xe9e qui transforme vos id\\xe9es en solutions digitales performantes, \\xe9tape par \\xe9tape.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            processSteps.map((step, index)=>{\n                const Icon = step.icon;\n                const visualAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.2);\n                const contentAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.3);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"scroll-section relative flex items-center justify-center bg-bg-secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: visualAnimation.ref,\n                                    className: `flex justify-center lg:justify-start order-2 lg:order-1 scroll-slide-left ${visualAnimation.isVisible ? \"visible\" : \"\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-40 h-40 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center shadow-xl transition-all duration-500 group-hover:scale-110 group-hover:shadow-2xl group-hover:shadow-primary-green/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-display text-4xl font-bold text-white transition-all duration-300 group-hover:scale-110\",\n                                                    children: step.number\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border border-border-light transition-all duration-300 group-hover:scale-110 group-hover:rotate-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-8 h-8 text-primary-green\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-4 -left-4 w-6 h-6 bg-accent-purple rounded-full opacity-20 animate-float-slow\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-8 left-8 w-4 h-4 bg-primary-green rounded-full opacity-30 animate-float-medium\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: contentAnimation.ref,\n                                    className: `text-center lg:text-left order-1 lg:order-2 scroll-slide-right ${contentAnimation.isVisible ? \"visible\" : \"\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary\",\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined)\n                }, index, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ProcessScroll.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, undefined);\n            })\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProcessScroll);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1Byb2Nlc3NTY3JvbGwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUUrRDtBQUNIO0FBRTVELE1BQU1LLGVBQWU7SUFDbkI7UUFDRUMsUUFBUTtRQUNSQyxNQUFNTixzR0FBTUE7UUFDWk8sT0FBTztRQUNQQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFSCxRQUFRO1FBQ1JDLE1BQU1MLHNHQUFPQTtRQUNiTSxPQUFPO1FBQ1BDLGFBQWE7SUFDZjtJQUNBO1FBQ0VILFFBQVE7UUFDUkMsTUFBTUosc0dBQUlBO1FBQ1ZLLE9BQU87UUFDUEMsYUFBYTtJQUNmO0lBQ0E7UUFDRUgsUUFBUTtRQUNSQyxNQUFNSCxzR0FBTUE7UUFDWkksT0FBTztRQUNQQyxhQUFhO0lBQ2Y7Q0FDRDtBQUVELE1BQU1DLGdCQUFnQjtJQUNwQixNQUFNQyxpQkFBaUJYLDZFQUFrQkEsQ0FBQztJQUUxQyxxQkFDRTs7MEJBRUUsOERBQUNZO2dCQUFRQyxXQUFVOzBCQUNqQiw0RUFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUNDQyxLQUFLSixlQUFlSSxHQUFHO3dCQUN2QkYsV0FBVyxDQUFDLGVBQWUsRUFBRUYsZUFBZUssU0FBUyxHQUFHLFlBQVksR0FBRyxDQUFDOzswQ0FFeEUsOERBQUNDO2dDQUFHSixXQUFVOztvQ0FBaUY7b0NBQ3ZGO2tEQUNOLDhEQUFDSzt3Q0FBS0wsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FFbEMsOERBQUNNO2dDQUFFTixXQUFVOzBDQUE0RTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVE5RlIsYUFBYWUsR0FBRyxDQUFDLENBQUNDLE1BQU1DO2dCQUN2QixNQUFNQyxPQUFPRixLQUFLZCxJQUFJO2dCQUN0QixNQUFNaUIsa0JBQWtCeEIsNkVBQWtCQSxDQUFDO2dCQUMzQyxNQUFNeUIsbUJBQW1CekIsNkVBQWtCQSxDQUFDO2dCQUU1QyxxQkFDRSw4REFBQ1k7b0JBRUNDLFdBQVU7OEJBRVYsNEVBQUNDO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUNDQyxLQUFLUyxnQkFBZ0JULEdBQUc7b0NBQ3hCRixXQUFXLENBQUMsMEVBQTBFLEVBQUVXLGdCQUFnQlIsU0FBUyxHQUFHLFlBQVksR0FBRyxDQUFDOzhDQUVwSSw0RUFBQ0Y7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDQztnREFBSUQsV0FBVTswREFDYiw0RUFBQ0s7b0RBQUtMLFdBQVU7OERBQ2JRLEtBQUtmLE1BQU07Ozs7Ozs7Ozs7OzBEQUloQiw4REFBQ1E7Z0RBQUlELFdBQVU7MERBQ2IsNEVBQUNVO29EQUFLVixXQUFVOzs7Ozs7Ozs7OzswREFJbEIsOERBQUNDO2dEQUFJRCxXQUFVOzs7Ozs7MERBQ2YsOERBQUNDO2dEQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJbkIsOERBQUNDO29DQUNDQyxLQUFLVSxpQkFBaUJWLEdBQUc7b0NBQ3pCRixXQUFXLENBQUMsK0RBQStELEVBQUVZLGlCQUFpQlQsU0FBUyxHQUFHLFlBQVksR0FBRyxDQUFDOztzREFFMUgsOERBQUNVOzRDQUFHYixXQUFVO3NEQUNYUSxLQUFLYixLQUFLOzs7Ozs7c0RBR2IsOERBQUNXOzRDQUFFTixXQUFVO3NEQUNWUSxLQUFLWixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttQkFuQ3BCYTs7Ozs7WUEwQ1g7OztBQUdOO0FBRUEsaUVBQWVaLGFBQWFBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naWJib25sYWItd2Vic2l0ZS8uL2NvbXBvbmVudHMvUHJvY2Vzc1Njcm9sbC50c3g/MjRkNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU2Nyb2xsQW5pbWF0aW9uIH0gZnJvbSAnQC9ob29rcy91c2VTY3JvbGxBbmltYXRpb24nXG5pbXBvcnQgeyBTZWFyY2gsIFBhbGV0dGUsIENvZGUsIFJvY2tldCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuY29uc3QgcHJvY2Vzc1N0ZXBzID0gW1xuICB7XG4gICAgbnVtYmVyOiBcIjAxXCIsXG4gICAgaWNvbjogU2VhcmNoLFxuICAgIHRpdGxlOiBcIkTDqWNvdXZlcnRlICYgU3RyYXTDqWdpZVwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIk5vdXMgYW5hbHlzb25zIHZvcyBiZXNvaW5zLCB2b3RyZSBtYXJjaMOpIGV0IHZvcyBvYmplY3RpZnMgcG91ciBkw6lmaW5pciBsYSBzdHJhdMOpZ2llIGRpZ2l0YWxlIG9wdGltYWxlLiBBdGVsaWVycyBjb2xsYWJvcmF0aWZzLCByZWNoZXJjaGUgdXRpbGlzYXRldXIgZXQgZMOpZmluaXRpb24gZHUgTVZQLlwiXG4gIH0sXG4gIHtcbiAgICBudW1iZXI6IFwiMDJcIixcbiAgICBpY29uOiBQYWxldHRlLFxuICAgIHRpdGxlOiBcIkRlc2lnbiAmIFByb3RvdHlwYWdlXCIsXG4gICAgZGVzY3JpcHRpb246IFwiQ3LDqWF0aW9uIGRlIHdpcmVmcmFtZXMsIG1hcXVldHRlcyBldCBwcm90b3R5cGVzIGludGVyYWN0aWZzLiBUZXN0cyB1dGlsaXNhdGV1cnMgZXQgaXTDqXJhdGlvbnMgcG91ciB2YWxpZGVyIGwnZXhww6lyaWVuY2UgYXZhbnQgbGUgZMOpdmVsb3BwZW1lbnQuXCJcbiAgfSxcbiAge1xuICAgIG51bWJlcjogXCIwM1wiLFxuICAgIGljb246IENvZGUsXG4gICAgdGl0bGU6IFwiRMOpdmVsb3BwZW1lbnQgQWdpbGVcIixcbiAgICBkZXNjcmlwdGlvbjogXCJEw6l2ZWxvcHBlbWVudCBlbiBzcHJpbnRzIGF2ZWMgbGl2cmFpc29ucyByw6lndWxpw6hyZXMuIENvZGUgY2xlYW4sIHRlc3RzIGF1dG9tYXRpc8OpcyBldCBpbnTDqWdyYXRpb24gY29udGludWUgcG91ciB1bmUgcXVhbGl0w6kgaXJyw6lwcm9jaGFibGUuXCJcbiAgfSxcbiAge1xuICAgIG51bWJlcjogXCIwNFwiLFxuICAgIGljb246IFJvY2tldCxcbiAgICB0aXRsZTogXCJMYW5jZW1lbnQgJiBPcHRpbWlzYXRpb25cIixcbiAgICBkZXNjcmlwdGlvbjogXCJEw6lwbG9pZW1lbnQgc8OpY3VyaXPDqSwgZm9ybWF0aW9uIGRlIHZvcyDDqXF1aXBlcyBldCBtb25pdG9yaW5nIGRlcyBwZXJmb3JtYW5jZXMuIE9wdGltaXNhdGlvbnMgY29udGludWVzIGJhc8OpZXMgc3VyIGxlcyBkb25uw6llcyBkJ3VzYWdlLlwiXG4gIH1cbl1cblxuY29uc3QgUHJvY2Vzc1Njcm9sbCA9ICgpID0+IHtcbiAgY29uc3QgdGl0bGVBbmltYXRpb24gPSB1c2VTY3JvbGxBbmltYXRpb24oMC4zKVxuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIHsvKiBQcm9jZXNzIEludHJvZHVjdGlvbiBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwic2Nyb2xsLXNlY3Rpb24gcmVsYXRpdmUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctYmctcHJpbWFyeVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICByZWY9e3RpdGxlQW5pbWF0aW9uLnJlZn1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHNjcm9sbC1mYWRlLWluICR7dGl0bGVBbmltYXRpb24uaXNWaXNpYmxlID8gJ3Zpc2libGUnIDogJyd9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwiZm9udC1kaXNwbGF5IHRleHQtNHhsIG1kOnRleHQtNnhsIGxnOnRleHQtN3hsIGZvbnQtYm9sZCBtYi02IHRleHQtdGV4dC1wcmltYXJ5XCI+XG4gICAgICAgICAgICAgIE5vdHJleycgJ31cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZ3JhZGllbnQtdGV4dFwiPlByb2Nlc3N1czwvc3Bhbj5cbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIG1kOnRleHQtMnhsIHRleHQtdGV4dC1zZWNvbmRhcnkgbWF4LXctNHhsIG14LWF1dG8gbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgIFVuZSBtw6l0aG9kb2xvZ2llIMOpcHJvdXbDqWUgcXVpIHRyYW5zZm9ybWUgdm9zIGlkw6llcyBlbiBzb2x1dGlvbnMgZGlnaXRhbGVzIHBlcmZvcm1hbnRlcywgw6l0YXBlIHBhciDDqXRhcGUuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogUHJvY2VzcyBEaXNjb3ZlcnkgU2VjdGlvbnMgKi99XG4gICAgICB7cHJvY2Vzc1N0ZXBzLm1hcCgoc3RlcCwgaW5kZXgpID0+IHtcbiAgICAgICAgY29uc3QgSWNvbiA9IHN0ZXAuaWNvblxuICAgICAgICBjb25zdCB2aXN1YWxBbmltYXRpb24gPSB1c2VTY3JvbGxBbmltYXRpb24oMC4yKVxuICAgICAgICBjb25zdCBjb250ZW50QW5pbWF0aW9uID0gdXNlU2Nyb2xsQW5pbWF0aW9uKDAuMylcblxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxzZWN0aW9uXG4gICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwic2Nyb2xsLXNlY3Rpb24gcmVsYXRpdmUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctYmctc2Vjb25kYXJ5XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC0xNiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICByZWY9e3Zpc3VhbEFuaW1hdGlvbi5yZWZ9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGp1c3RpZnktY2VudGVyIGxnOmp1c3RpZnktc3RhcnQgb3JkZXItMiBsZzpvcmRlci0xIHNjcm9sbC1zbGlkZS1sZWZ0ICR7dmlzdWFsQW5pbWF0aW9uLmlzVmlzaWJsZSA/ICd2aXNpYmxlJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBncm91cFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNDAgaC00MCBiZy1ncmFkaWVudC10by1iciBmcm9tLXByaW1hcnktZ3JlZW4gdG8tcHJpbWFyeS1ncmVlbi1kYXJrIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIGdyb3VwLWhvdmVyOnNjYWxlLTExMCBncm91cC1ob3ZlcjpzaGFkb3ctMnhsIGdyb3VwLWhvdmVyOnNoYWRvdy1wcmltYXJ5LWdyZWVuLzIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1kaXNwbGF5IHRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBncm91cC1ob3ZlcjpzY2FsZS0xMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzdGVwLm51bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC00IC1yaWdodC00IHctMTYgaC0xNiBiZy13aGl0ZSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc2hhZG93LWxnIGJvcmRlciBib3JkZXItYm9yZGVyLWxpZ2h0IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBncm91cC1ob3ZlcjpzY2FsZS0xMTAgZ3JvdXAtaG92ZXI6cm90YXRlLTEyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEljb24gY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXByaW1hcnktZ3JlZW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogRmxvYXRpbmcgZGVjb3JhdGl2ZSBlbGVtZW50cyAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTQgLWxlZnQtNCB3LTYgaC02IGJnLWFjY2VudC1wdXJwbGUgcm91bmRlZC1mdWxsIG9wYWNpdHktMjAgYW5pbWF0ZS1mbG9hdC1zbG93XCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC04IGxlZnQtOCB3LTQgaC00IGJnLXByaW1hcnktZ3JlZW4gcm91bmRlZC1mdWxsIG9wYWNpdHktMzAgYW5pbWF0ZS1mbG9hdC1tZWRpdW1cIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgcmVmPXtjb250ZW50QW5pbWF0aW9uLnJlZn1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtY2VudGVyIGxnOnRleHQtbGVmdCBvcmRlci0xIGxnOm9yZGVyLTIgc2Nyb2xsLXNsaWRlLXJpZ2h0ICR7Y29udGVudEFuaW1hdGlvbi5pc1Zpc2libGUgPyAndmlzaWJsZScgOiAnJ31gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LWRpc3BsYXkgdGV4dC0zeGwgbWQ6dGV4dC00eGwgbGc6dGV4dC01eGwgZm9udC1ib2xkIG1iLTYgdGV4dC10ZXh0LXByaW1hcnlcIj5cbiAgICAgICAgICAgICAgICAgICAge3N0ZXAudGl0bGV9XG4gICAgICAgICAgICAgICAgICA8L2gzPlxuXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIG1kOnRleHQteGwgdGV4dC10ZXh0LXNlY29uZGFyeSBsZWFkaW5nLXJlbGF4ZWQgbWF4LXctMnhsIG14LWF1dG8gbGc6bXgtMFwiPlxuICAgICAgICAgICAgICAgICAgICB7c3RlcC5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L3NlY3Rpb24+XG4gICAgICAgIClcbiAgICAgIH0pfVxuICAgIDwvPlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IFByb2Nlc3NTY3JvbGxcbiJdLCJuYW1lcyI6WyJ1c2VTY3JvbGxBbmltYXRpb24iLCJTZWFyY2giLCJQYWxldHRlIiwiQ29kZSIsIlJvY2tldCIsInByb2Nlc3NTdGVwcyIsIm51bWJlciIsImljb24iLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUHJvY2Vzc1Njcm9sbCIsInRpdGxlQW5pbWF0aW9uIiwic2VjdGlvbiIsImNsYXNzTmFtZSIsImRpdiIsInJlZiIsImlzVmlzaWJsZSIsImgyIiwic3BhbiIsInAiLCJtYXAiLCJzdGVwIiwiaW5kZXgiLCJJY29uIiwidmlzdWFsQW5pbWF0aW9uIiwiY29udGVudEFuaW1hdGlvbiIsImgzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ProcessScroll.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ServicesScroll.tsx":
/*!***************************************!*\
  !*** ./components/ServicesScroll.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useScrollAnimation */ \"(ssr)/./hooks/useScrollAnimation.ts\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lightbulb,Monitor,Palette,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst services = [\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9veloppement Web\",\n        description: \"Applications web modernes et performantes. React, Vue.js, Node.js - nous ma\\xeetrisons les technologies de pointe pour cr\\xe9er des exp\\xe9riences utilisateur exceptionnelles et des architectures scalables.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Applications Mobile\",\n        description: \"Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie \\xe0 vos id\\xe9es sur tous les \\xe9crans.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Design & UX/UI\",\n        description: \"Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous cr\\xe9ons des interfaces intuitives qui racontent l'histoire de votre marque.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Branding Digital\",\n        description: \"Identit\\xe9s visuelles m\\xe9morables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque pour tous les supports digitaux.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Maintenance & Support\",\n        description: \"Accompagnement technique continu pour faire \\xe9voluer vos projets. Monitoring, mises \\xe0 jour, optimisations - nous veillons sur vos applications comme sur nos propres cr\\xe9ations.\"\n    },\n    {\n        icon: _barrel_optimize_names_Globe_Lightbulb_Monitor_Palette_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Consulting Technique\",\n        description: \"Expertise strat\\xe9gique pour orienter vos d\\xe9cisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions pour votre business.\"\n    }\n];\nconst ServicesScroll = ()=>{\n    const titleAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"scroll-section relative flex items-center justify-center bg-bg-primary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: titleAnimation.ref,\n                        className: `scroll-fade-in ${titleAnimation.isVisible ? \"visible\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-display text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-text-primary\",\n                                children: [\n                                    \"Nos\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed\",\n                                children: \"Nous fa\\xe7onnons l'excellence en d\\xe9veloppement web, applications mobiles et design digital. Chaque projet est une œuvre d'art technique.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined),\n            services.map((service, index)=>{\n                const Icon = service.icon;\n                const contentAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.2);\n                const visualAnimation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)(0.3);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"scroll-section relative flex items-center justify-center bg-bg-secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: contentAnimation.ref,\n                                    className: `text-center lg:text-left scroll-slide-left ${contentAnimation.isVisible ? \"visible\" : \"\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-2xl flex items-center justify-center mb-8 mx-auto lg:mx-0 shadow-lg transition-transform duration-300 hover:scale-110 hover:rotate-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-10 h-10 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-display text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-primary\",\n                                            children: service.title\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg md:text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                            children: service.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: visualAnimation.ref,\n                                    className: `flex justify-center lg:justify-end scroll-slide-right ${visualAnimation.isVisible ? \"visible\" : \"\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-80 h-80 bg-gradient-to-br from-primary-green/10 to-primary-green-dark/10 rounded-3xl flex items-center justify-center border border-primary-green/20 transition-all duration-500 group-hover:scale-105 group-hover:shadow-xl group-hover:shadow-primary-green/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-32 h-32 text-primary-green opacity-30 transition-all duration-500 group-hover:opacity-50 group-hover:scale-110\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 -right-4 w-8 h-8 bg-primary-green rounded-full opacity-20 animate-float-slow\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-6 -left-6 w-6 h-6 bg-accent-purple rounded-full opacity-20 animate-float-medium\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, undefined)\n                }, index, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/ServicesScroll.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 11\n                }, undefined);\n            })\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ServicesScroll);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ServicesScroll.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useScrollAnimation.ts":
/*!*************************************!*\
  !*** ./hooks/useScrollAnimation.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useScrollAnimation: () => (/* binding */ useScrollAnimation),\n/* harmony export */   useScrollProgress: () => (/* binding */ useScrollProgress)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useScrollAnimation,useScrollProgress auto */ \nconst useScrollAnimation = (threshold = 0.1)=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const observer = new IntersectionObserver(([entry])=>{\n            if (entry.isIntersecting) {\n                setIsVisible(true);\n            }\n        }, {\n            threshold,\n            rootMargin: \"0px 0px -100px 0px\"\n        });\n        if (ref.current) {\n            observer.observe(ref.current);\n        }\n        return ()=>{\n            if (ref.current) {\n                observer.unobserve(ref.current);\n            }\n        };\n    }, [\n        threshold\n    ]);\n    return {\n        ref,\n        isVisible\n    };\n};\nconst useScrollProgress = ()=>{\n    const [scrollProgress, setScrollProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const scrollTop = window.scrollY;\n            const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n            const progress = scrollTop / docHeight;\n            setScrollProgress(progress);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    return scrollProgress;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useScrollAnimation.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c5cb3093da95\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naWJib25sYWItd2Vic2l0ZS8uL2FwcC9nbG9iYWxzLmNzcz9mOWM5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYzVjYjMwOTNkYTk1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Space_Grotesk_arguments_subsets_latin_variable_font_space_grotesk_variableName_spaceGrotesk___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Space_Grotesk\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-space-grotesk\"}],\"variableName\":\"spaceGrotesk\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Space_Grotesk\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-space-grotesk\\\"}],\\\"variableName\\\":\\\"spaceGrotesk\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Space_Grotesk_arguments_subsets_latin_variable_font_space_grotesk_variableName_spaceGrotesk___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Space_Grotesk_arguments_subsets_latin_variable_font_space_grotesk_variableName_spaceGrotesk___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"GibbonLab: Web & Mobile Design and Development for Global Businesses & Founders\",\n    description: \"Need high-quality design and development work without the hassle? GibbonLab delivers top-tier design and development services with no hiring drama or delays. Flexible, subscription-based solutions tailored to your business needs—pause or start anytime!\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Space_Grotesk_arguments_subsets_latin_variable_font_space_grotesk_variableName_spaceGrotesk___WEBPACK_IMPORTED_MODULE_3___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();