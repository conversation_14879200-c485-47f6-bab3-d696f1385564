/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/home/<USER>/Documents/projects/gibbonlab.com/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp%2Fpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp%2Fpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnJpY2slMkZEb2N1bWVudHMlMkZwcm9qZWN0cyUyRmdpYmJvbmxhYi5jb20lMkZhcHAlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naWJib25sYWItd2Vic2l0ZS8/NjQxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3JpY2svRG9jdW1lbnRzL3Byb2plY3RzL2dpYmJvbmxhYi5jb20vYXBwL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Space_Grotesk%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-space-grotesk%22%7D%5D%2C%22variableName%22%3A%22spaceGrotesk%22%7D&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp%2Fglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Space_Grotesk%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-space-grotesk%22%7D%5D%2C%22variableName%22%3A%22spaceGrotesk%22%7D&modules=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp%2Fglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Globe,Lightbulb,Monitor,Palette,Rocket,Search,Settings,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./components/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst services = [\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"D\\xe9veloppement Web\",\n        description: \"Applications web modernes et performantes. React, Vue.js, Node.js - nous ma\\xeetrisons les technologies de pointe pour cr\\xe9er des exp\\xe9riences utilisateur exceptionnelles.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Applications Mobile\",\n        description: \"Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie \\xe0 vos id\\xe9es sur tous les \\xe9crans.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Design & UX/UI\",\n        description: \"Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous cr\\xe9ons des interfaces intuitives.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Branding Digital\",\n        description: \"Identit\\xe9s visuelles m\\xe9morables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Maintenance & Support\",\n        description: \"Accompagnement technique continu pour faire \\xe9voluer vos projets. Monitoring, mises \\xe0 jour, optimisations - nous veillons sur vos applications.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Consulting Technique\",\n        description: \"Expertise strat\\xe9gique pour orienter vos d\\xe9cisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions.\"\n    }\n];\nconst processes = [\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"D\\xe9couverte\",\n        description: \"Nous analysons vos besoins, votre march\\xe9 et vos objectifs pour d\\xe9finir la strat\\xe9gie digitale optimale. Ateliers collaboratifs, recherche utilisateur et d\\xe9finition du MVP.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Conception\",\n        description: \"Cr\\xe9ation de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et it\\xe9rations pour valider l'exp\\xe9rience avant le d\\xe9veloppement.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"D\\xe9veloppement\",\n        description: \"D\\xe9veloppement en sprints avec livraisons r\\xe9guli\\xe8res. Code clean, tests automatis\\xe9s et int\\xe9gration continue pour une qualit\\xe9 irr\\xe9prochable.\"\n    },\n    {\n        icon: _barrel_optimize_names_Code_Globe_Lightbulb_Monitor_Palette_Rocket_Search_Settings_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: \"Lancement\",\n        description: \"D\\xe9ploiement s\\xe9curis\\xe9, formation de vos \\xe9quipes et monitoring des performances. Optimisations continues bas\\xe9es sur les donn\\xe9es d'usage.\"\n    }\n];\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"snap-y snap-mandatory overflow-y-scroll h-[100dvh] min-h-[100dvh] w-full overflow-x-hidden no-scrollbar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border w-full snap-always snap-center h-[100dvh] min-h-[100dvh] flex flex-col items-center justify-center bg-bg-primary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full flex flex-col items-center justify-center gap-12 px-4 md:px-8 lg:px-12 mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl text-center text-text-primary\",\n                                                children: [\n                                                    \"Nous cr\\xe9ons des\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"gradient-text\",\n                                                        children: \"applications exceptionnelles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl w-full text-center text-text-primary mt-4\",\n                                                children: \"qui transforment vos id\\xe9es\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl md:text-3xl lg:text-3xl tracking-normal text-text-secondary\",\n                                        children: \"Design \\xe9l\\xe9gant, code robuste, r\\xe9sultats mesurables.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-secondary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl md:text-5xl w-full text-center leading-tight whitespace-pre-wrap px-4 md:px-8 lg:px-12\",\n                                children: [\n                                    \"Nous fa\\xe7onnons l'excellence en\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"d\\xe9veloppement web\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this),\n                                    \",\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"applications mobiles\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" et\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"design digital\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            services.map((service, index)=>{\n                const Icon = service.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"snap-always snap-center min-h-screen\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-[100dvh] min-h-[100dvh] flex items-center justify-center text-white\",\n                                style: {\n                                    backgroundColor: \"#1C1B1C\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircleElement, {\n                                        icon: Icon\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fixed w-11/12 lg:w-full bottom-5 md:bottom-10 lg:bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-4 text-center pointer-events-none z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-6xl lg:text-7xl font-light text-white\",\n                                                children: service.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base lg:text-lg lg:w-6/12 text-[#A0A4A1] px-1 lg:px-2\",\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-1/2 lg:left-2 lg:top-1/2 bottom-[1vh] -translate-x-1/2 lg:-translate-x-0 lg:-translate-y-1/2 -translate-y-1/2 flex flex-row lg:flex-col lg:items-start items-center justify-center lg:gap-2 gap-6 px-4 md:px-8 lg:px-12 mx-auto pointer-events-auto z-[999]\",\n                                        children: services.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `text-left transition-colors duration-300 ${i === index ? \"text-white\" : \"text-gray-500\"}`,\n                                                style: {\n                                                    fontFamily: \"monospace\"\n                                                },\n                                                children: service.title.toUpperCase().replace(/[^A-Z]/g, \"\").slice(0, 8)\n                                            }, i, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 13\n                    }, this)\n                }, index, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-primary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl md:text-5xl w-full text-center leading-tight whitespace-pre-wrap px-4 md:px-8 lg:px-12\",\n                                children: [\n                                    \"Notre m\\xe9thodologie \\xe9prouv\\xe9e en\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"4 \\xe9tapes\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    \"pour transformer vos id\\xe9es en\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"succ\\xe8s digitaux\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            processes.map((process, index)=>{\n                const Icon = process.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"snap-always snap-center min-h-screen\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-[100dvh] min-h-[100dvh] flex items-center justify-center text-white\",\n                                style: {\n                                    backgroundColor: \"#1C1B1C\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircleElement, {\n                                        icon: Icon\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fixed w-11/12 lg:w-full bottom-5 md:bottom-10 lg:bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-4 text-center pointer-events-none z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-6xl lg:text-7xl font-light text-white\",\n                                                children: process.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base lg:text-lg lg:w-6/12 text-[#A0A4A1] px-1 lg:px-2\",\n                                                children: process.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-1/2 lg:left-2 lg:top-1/2 bottom-[1vh] -translate-x-1/2 lg:-translate-x-0 lg:-translate-y-1/2 -translate-y-1/2 flex flex-row lg:flex-col lg:items-start items-center justify-center lg:gap-2 gap-6 px-4 md:px-8 lg:px-12 mx-auto pointer-events-auto z-[999]\",\n                                        children: processes.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `text-left transition-colors duration-300 ${i === index ? \"text-white\" : \"text-gray-500\"}`,\n                                                style: {\n                                                    fontFamily: \"monospace\"\n                                                },\n                                                children: process.title.toUpperCase()\n                                            }, i, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)\n                }, index, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"snap-always snap-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-[100dvh] min-h-[100dvh] w-full flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-secondary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6\",\n                                        children: [\n                                            \"Pr\\xeat \\xe0 transformer\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"gradient-text\",\n                                                children: \"votre vision\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            \"en r\\xe9alit\\xe9 ?\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto mb-12\",\n                                        children: \"Discutons de votre projet et d\\xe9couvrons ensemble comment nous pouvons vous aider \\xe0 atteindre vos objectifs.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"shimmer-effect bg-gradient-to-r from-primary-green to-primary-green-dark text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary-green/25\",\n                                        children: \"D\\xe9marrer votre projet\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Header = ()=>{\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 100);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.header, {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? \"py-3 bg-white/98 backdrop-blur-xl shadow-lg\" : \"py-5 bg-white/95 backdrop-blur-lg\"} border-b border-border-light`,\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        transition: {\n            duration: 0.6,\n            ease: \"easeOut\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex items-center gap-2 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gradient-to-br from-primary-green to-primary-green-dark rounded-full flex items-center justify-center transition-transform group-hover:scale-105 group-hover:rotate-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-sm\",\n                                    children: \"G\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-display text-2xl font-bold text-primary-green transition-transform group-hover:scale-105\",\n                                children: \"GibbonLab\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center gap-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mr-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"#\",\n                                        className: \"px-3 py-1.5 rounded-md text-sm font-semibold bg-primary-green text-white\",\n                                        children: \"FR\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"#\",\n                                        className: \"px-3 py-1.5 rounded-md text-sm font-semibold text-text-muted hover:bg-bg-accent hover:text-text-secondary transition-colors\",\n                                        children: \"EN\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined),\n                            [\n                                \"Nos Services\",\n                                \"Notre Expertise\",\n                                \"Notre Processus\",\n                                \"Contact\"\n                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: `#${item.toLowerCase().replace(/\\s+/g, \"-\").replace(\"notre-\", \"\").replace(\"nos-\", \"\")}`,\n                                    className: \"relative text-text-secondary hover:text-primary-green font-medium transition-colors group\",\n                                    children: [\n                                        item,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute bottom-0 left-0 w-0 h-0.5 bg-primary-green transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, item, true, {\n                                    fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#contact\",\n                                className: \"shimmer-effect bg-gradient-to-r from-primary-green to-primary-green-dark text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-primary-green/25\",\n                                children: \"D\\xe9marrer un projet\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/components/Header.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0hlYWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ0w7QUFDVjtBQUU1QixNQUFNSSxTQUFTO0lBQ2IsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdOLCtDQUFRQSxDQUFDO0lBRTdDQyxnREFBU0EsQ0FBQztRQUNSLE1BQU1NLGVBQWU7WUFDbkJELGNBQWNFLE9BQU9DLE9BQU8sR0FBRztRQUNqQztRQUVBRCxPQUFPRSxnQkFBZ0IsQ0FBQyxVQUFVSDtRQUNsQyxPQUFPLElBQU1DLE9BQU9HLG1CQUFtQixDQUFDLFVBQVVKO0lBQ3BELEdBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDTCxpREFBTUEsQ0FBQ1UsTUFBTTtRQUNaQyxXQUFXLENBQUMsNERBQTRELEVBQ3RFUixhQUNJLGdEQUNBLG9DQUNMLDZCQUE2QixDQUFDO1FBQy9CUyxTQUFTO1lBQUVDLEdBQUcsQ0FBQztRQUFJO1FBQ25CQyxTQUFTO1lBQUVELEdBQUc7UUFBRTtRQUNoQkUsWUFBWTtZQUFFQyxVQUFVO1lBQUtDLE1BQU07UUFBVTtrQkFFN0MsNEVBQUNDO1lBQUlQLFdBQVU7c0JBQ2IsNEVBQUNPO2dCQUFJUCxXQUFVOztrQ0FDYiw4REFBQ1Ysa0RBQUlBO3dCQUFDa0IsTUFBSzt3QkFBSVIsV0FBVTs7MENBQ3ZCLDhEQUFDTztnQ0FBSVAsV0FBVTswQ0FDYiw0RUFBQ1M7b0NBQUtULFdBQVU7OENBQStCOzs7Ozs7Ozs7OzswQ0FFakQsOERBQUNTO2dDQUFLVCxXQUFVOzBDQUFnRzs7Ozs7Ozs7Ozs7O2tDQUtsSCw4REFBQ1U7d0JBQUlWLFdBQVU7OzBDQUNiLDhEQUFDTztnQ0FBSVAsV0FBVTs7a0RBQ2IsOERBQUNWLGtEQUFJQTt3Q0FBQ2tCLE1BQUs7d0NBQUlSLFdBQVU7a0RBQTJFOzs7Ozs7a0RBR3BHLDhEQUFDVixrREFBSUE7d0NBQUNrQixNQUFLO3dDQUFJUixXQUFVO2tEQUE4SDs7Ozs7Ozs7Ozs7OzRCQUt4SjtnQ0FBQztnQ0FBZ0I7Z0NBQW1CO2dDQUFtQjs2QkFBVSxDQUFDVyxHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQzVFLDhEQUFDdkIsa0RBQUlBO29DQUVIa0IsTUFBTSxDQUFDLENBQUMsRUFBRUksS0FBS0UsV0FBVyxHQUFHQyxPQUFPLENBQUMsUUFBUSxLQUFLQSxPQUFPLENBQUMsVUFBVSxJQUFJQSxPQUFPLENBQUMsUUFBUSxJQUFJLENBQUM7b0NBQzdGZixXQUFVOzt3Q0FFVFk7c0RBQ0QsOERBQUNIOzRDQUFLVCxXQUFVOzs7Ozs7O21DQUxYWTs7Ozs7MENBU1QsOERBQUN0QixrREFBSUE7Z0NBQ0hrQixNQUFLO2dDQUNMUixXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7QUFFQSxpRUFBZVQsTUFBTUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dpYmJvbmxhYi13ZWJzaXRlLy4vY29tcG9uZW50cy9IZWFkZXIudHN4PzAzNjgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5cbmNvbnN0IEhlYWRlciA9ICgpID0+IHtcbiAgY29uc3QgW2lzU2Nyb2xsZWQsIHNldElzU2Nyb2xsZWRdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVTY3JvbGwgPSAoKSA9PiB7XG4gICAgICBzZXRJc1Njcm9sbGVkKHdpbmRvdy5zY3JvbGxZID4gMTAwKVxuICAgIH1cblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwpXG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwpXG4gIH0sIFtdKVxuXG4gIHJldHVybiAoXG4gICAgPG1vdGlvbi5oZWFkZXJcbiAgICAgIGNsYXNzTmFtZT17YGZpeGVkIHRvcC0wIGxlZnQtMCByaWdodC0wIHotNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgIGlzU2Nyb2xsZWQgXG4gICAgICAgICAgPyAncHktMyBiZy13aGl0ZS85OCBiYWNrZHJvcC1ibHVyLXhsIHNoYWRvdy1sZycgXG4gICAgICAgICAgOiAncHktNSBiZy13aGl0ZS85NSBiYWNrZHJvcC1ibHVyLWxnJ1xuICAgICAgfSBib3JkZXItYiBib3JkZXItYm9yZGVyLWxpZ2h0YH1cbiAgICAgIGluaXRpYWw9e3sgeTogLTEwMCB9fVxuICAgICAgYW5pbWF0ZT17eyB5OiAwIH19XG4gICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGVhc2U6IFwiZWFzZU91dFwiIH19XG4gICAgPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBncm91cFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHJpbWFyeS1ncmVlbiB0by1wcmltYXJ5LWdyZWVuLWRhcmsgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRyYW5zaXRpb24tdHJhbnNmb3JtIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSBncm91cC1ob3Zlcjpyb3RhdGUtM1wiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LXNtXCI+Rzwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1kaXNwbGF5IHRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnktZ3JlZW4gdHJhbnNpdGlvbi10cmFuc2Zvcm0gZ3JvdXAtaG92ZXI6c2NhbGUtMTA1XCI+XG4gICAgICAgICAgICAgIEdpYmJvbkxhYlxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvTGluaz5cblxuICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggaXRlbXMtY2VudGVyIGdhcC0xMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtci01XCI+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwicHgtMyBweS0xLjUgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgYmctcHJpbWFyeS1ncmVlbiB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgRlJcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInB4LTMgcHktMS41IHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtdGV4dC1tdXRlZCBob3ZlcjpiZy1iZy1hY2NlbnQgaG92ZXI6dGV4dC10ZXh0LXNlY29uZGFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgIEVOXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7WydOb3MgU2VydmljZXMnLCAnTm90cmUgRXhwZXJ0aXNlJywgJ05vdHJlIFByb2Nlc3N1cycsICdDb250YWN0J10ubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGtleT17aXRlbX1cbiAgICAgICAgICAgICAgICBocmVmPXtgIyR7aXRlbS50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoL1xccysvZywgJy0nKS5yZXBsYWNlKCdub3RyZS0nLCAnJykucmVwbGFjZSgnbm9zLScsICcnKX1gfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHRleHQtdGV4dC1zZWNvbmRhcnkgaG92ZXI6dGV4dC1wcmltYXJ5LWdyZWVuIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGdyb3VwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpdGVtfVxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0wIGxlZnQtMCB3LTAgaC0wLjUgYmctcHJpbWFyeS1ncmVlbiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZ3JvdXAtaG92ZXI6dy1mdWxsXCIgLz5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCIjY29udGFjdFwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNoaW1tZXItZWZmZWN0IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wcmltYXJ5LWdyZWVuIHRvLXByaW1hcnktZ3JlZW4tZGFyayB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLXhsIGZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNjYWxlLTEwNSBob3ZlcjpzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXByaW1hcnktZ3JlZW4vMjVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBEw6ltYXJyZXIgdW4gcHJvamV0XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9uYXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9tb3Rpb24uaGVhZGVyPlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IEhlYWRlclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwibW90aW9uIiwiTGluayIsIkhlYWRlciIsImlzU2Nyb2xsZWQiLCJzZXRJc1Njcm9sbGVkIiwiaGFuZGxlU2Nyb2xsIiwid2luZG93Iiwic2Nyb2xsWSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwiaW5pdGlhbCIsInkiLCJhbmltYXRlIiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwiZWFzZSIsImRpdiIsImhyZWYiLCJzcGFuIiwibmF2IiwibWFwIiwiaXRlbSIsImluZGV4IiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5a33ce967827\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naWJib25sYWItd2Vic2l0ZS8uL2FwcC9nbG9iYWxzLmNzcz9mOWM5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNWEzM2NlOTY3ODI3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Space_Grotesk_arguments_subsets_latin_variable_font_space_grotesk_variableName_spaceGrotesk___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Space_Grotesk\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-space-grotesk\"}],\"variableName\":\"spaceGrotesk\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Space_Grotesk\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-space-grotesk\\\"}],\\\"variableName\\\":\\\"spaceGrotesk\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Space_Grotesk_arguments_subsets_latin_variable_font_space_grotesk_variableName_spaceGrotesk___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Space_Grotesk_arguments_subsets_latin_variable_font_space_grotesk_variableName_spaceGrotesk___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"GibbonLab: Web & Mobile Design and Development for Global Businesses & Founders\",\n    description: \"Need high-quality design and development work without the hassle? GibbonLab delivers top-tier design and development services with no hiring drama or delays. Flexible, subscription-based solutions tailored to your business needs—pause or start anytime!\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Space_Grotesk_arguments_subsets_latin_variable_font_space_grotesk_variableName_spaceGrotesk___WEBPACK_IMPORTED_MODULE_3___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/projects/gibbonlab.com/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Documents/projects/gibbonlab.com/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Frick%2FDocuments%2Fprojects%2Fgibbonlab.com&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();