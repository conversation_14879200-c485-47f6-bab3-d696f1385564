(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[767],{1932:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=i(2265),r=s&&"object"==typeof s&&"default"in s?s:{default:s};!function(e){if(!e||"undefined"==typeof window)return;let t=document.createElement("style");t.setAttribute("type","text/css"),t.innerHTML=e,document.head.appendChild(t)}('.rfm-marquee-container {\n  overflow-x: hidden;\n  display: flex;\n  flex-direction: row;\n  position: relative;\n  width: var(--width);\n  transform: var(--transform);\n}\n.rfm-marquee-container:hover div {\n  animation-play-state: var(--pause-on-hover);\n}\n.rfm-marquee-container:active div {\n  animation-play-state: var(--pause-on-click);\n}\n\n.rfm-overlay {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n}\n.rfm-overlay::before, .rfm-overlay::after {\n  background: linear-gradient(to right, var(--gradient-color), rgba(255, 255, 255, 0));\n  content: "";\n  height: 100%;\n  position: absolute;\n  width: var(--gradient-width);\n  z-index: 2;\n  pointer-events: none;\n  touch-action: none;\n}\n.rfm-overlay::after {\n  right: 0;\n  top: 0;\n  transform: rotateZ(180deg);\n}\n.rfm-overlay::before {\n  left: 0;\n  top: 0;\n}\n\n.rfm-marquee {\n  flex: 0 0 auto;\n  min-width: var(--min-width);\n  z-index: 1;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  animation: scroll var(--duration) linear var(--delay) var(--iteration-count);\n  animation-play-state: var(--play);\n  animation-delay: var(--delay);\n  animation-direction: var(--direction);\n}\n@keyframes scroll {\n  0% {\n    transform: translateX(0%);\n  }\n  100% {\n    transform: translateX(-100%);\n  }\n}\n\n.rfm-initial-child-container {\n  flex: 0 0 auto;\n  display: flex;\n  min-width: auto;\n  flex-direction: row;\n  align-items: center;\n}\n\n.rfm-child {\n  transform: var(--transform);\n}');let n=s.forwardRef(function(e,t){let{style:i={},className:n="",autoFill:a=!1,play:o=!0,pauseOnHover:l=!1,pauseOnClick:d=!1,direction:u="left",speed:h=50,delay:c=0,loop:p=0,gradient:m=!1,gradientColor:f="white",gradientWidth:v=200,onFinish:g,onCycleComplete:y,onMount:w,children:b}=e,[x,S]=s.useState(0),[T,P]=s.useState(0),[E,C]=s.useState(1),[M,A]=s.useState(!1),k=s.useRef(null),L=t||k,V=s.useRef(null),D=s.useCallback(()=>{if(V.current&&L.current){let e=L.current.getBoundingClientRect(),t=V.current.getBoundingClientRect(),i=e.width,s=t.width;("up"===u||"down"===u)&&(i=e.height,s=t.height),a&&i&&s?C(s<i?Math.ceil(i/s):1):C(1),S(i),P(s)}},[a,L,u]);s.useEffect(()=>{if(M&&(D(),V.current&&L.current)){let e=new ResizeObserver(()=>D());return e.observe(L.current),e.observe(V.current),()=>{e&&e.disconnect()}}},[D,L,M]),s.useEffect(()=>{D()},[D,b]),s.useEffect(()=>{A(!0)},[]),s.useEffect(()=>{"function"==typeof w&&w()},[]);let O=s.useMemo(()=>a?T*E/h:T<x?x/h:T/h,[a,x,T,E,h]),R=s.useMemo(()=>Object.assign(Object.assign({},i),{"--pause-on-hover":!o||l?"paused":"running","--pause-on-click":!o||l&&!d||d?"paused":"running","--width":"up"===u||"down"===u?"100vh":"100%","--transform":"up"===u?"rotate(-90deg)":"down"===u?"rotate(90deg)":"none"}),[i,o,l,d,u]),I=s.useMemo(()=>({"--gradient-color":f,"--gradient-width":"number"==typeof v?"".concat(v,"px"):v}),[f,v]),_=s.useMemo(()=>({"--play":o?"running":"paused","--direction":"left"===u?"normal":"reverse","--duration":"".concat(O,"s"),"--delay":"".concat(c,"s"),"--iteration-count":p?"".concat(p):"infinite","--min-width":a?"auto":"100%"}),[o,u,O,c,p,a]),F=s.useMemo(()=>({"--transform":"up"===u?"rotate(90deg)":"down"===u?"rotate(-90deg)":"none"}),[u]),j=s.useCallback(e=>[...Array(Number.isFinite(e)&&e>=0?e:0)].map((e,t)=>r.default.createElement(s.Fragment,{key:t},s.Children.map(b,e=>r.default.createElement("div",{style:F,className:"rfm-child"},e)))),[F,b]);return M?r.default.createElement("div",{ref:L,style:R,className:"rfm-marquee-container "+n},m&&r.default.createElement("div",{style:I,className:"rfm-overlay"}),r.default.createElement("div",{className:"rfm-marquee",style:_,onAnimationIteration:y,onAnimationEnd:g},r.default.createElement("div",{className:"rfm-initial-child-container",ref:V},s.Children.map(b,e=>r.default.createElement("div",{style:F,className:"rfm-child"},e))),j(E-1)),r.default.createElement("div",{className:"rfm-marquee",style:_},j(E))):null});t.default=n},7322:function(){},3034:function(){},6062:function(e){e.exports={style:{fontFamily:"'__Inter_36bd41', '__Inter_Fallback_36bd41'",fontStyle:"normal"},className:"__className_36bd41"}},6799:function(e){e.exports={style:{fontFamily:"'__clash_f1bfa0', '__clash_Fallback_f1bfa0'"},className:"__className_f1bfa0"}},1362:function(e){e.exports={style:{fontFamily:"'__clash_f1bfa0', '__clash_Fallback_f1bfa0'"},className:"__className_f1bfa0"}},2426:function(e){e.exports={style:{fontFamily:"'__clash_f1bfa0', '__clash_Fallback_f1bfa0'"},className:"__className_f1bfa0"}},6103:function(e){e.exports={style:{fontFamily:"'__clash_f1bfa0', '__clash_Fallback_f1bfa0'"},className:"__className_f1bfa0"}},3544:function(e){e.exports={style:{fontFamily:"'__clash_f1bfa0', '__clash_Fallback_f1bfa0'"},className:"__className_f1bfa0"}},3201:function(e,t,i){"use strict";i.d(t,{n:function(){return a}});var s=i(2265);function r(e,t,i){(0,s.useEffect)(()=>(window.addEventListener(e,t,i),()=>window.removeEventListener(e,t,i)),[e,t])}let n={passive:!0};function a(){let[e,t]=(0,s.useState)({width:0,height:0}),i=(0,s.useCallback)(()=>{t({width:window.innerWidth||0,height:window.innerHeight||0})},[]);return r("resize",i,n),r("orientationchange",i,n),(0,s.useEffect)(i,[]),e}},4446:function(e,t,i){"use strict";i.d(t,{M:function(){return g}});var s=i(7437),r=i(2265),n=i(7797),a=i(458),o=i(9791);class l extends r.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function d({children:e,isPresent:t}){let i=(0,r.useId)(),n=(0,r.useRef)(null),a=(0,r.useRef)({width:0,height:0,top:0,left:0}),{nonce:d}=(0,r.useContext)(o._);return(0,r.useInsertionEffect)(()=>{let{width:e,height:s,top:r,left:o}=a.current;if(t||!n.current||!e||!s)return;n.current.dataset.motionPopId=i;let l=document.createElement("style");return d&&(l.nonce=d),document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${s}px !important;
            top: ${r}px !important;
            left: ${o}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[t]),(0,s.jsx)(l,{isPresent:t,childRef:n,sizeRef:a,children:r.cloneElement(e,{ref:n})})}let u=({children:e,initial:t,isPresent:i,onExitComplete:o,custom:l,presenceAffectsLayout:u,mode:c})=>{let p=(0,a.h)(h),m=(0,r.useId)(),f=(0,r.useMemo)(()=>({id:m,initial:t,isPresent:i,custom:l,onExitComplete:e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;o&&o()},register:e=>(p.set(e,!1),()=>p.delete(e))}),u?[Math.random()]:[i]);return(0,r.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[i]),r.useEffect(()=>{i||p.size||!o||o()},[i]),"popLayout"===c&&(e=(0,s.jsx)(d,{isPresent:i,children:e})),(0,s.jsx)(n.O.Provider,{value:f,children:e})};function h(){return new Map}var c=i(5050),p=i(9047);let m=e=>e.key||"";function f(e){let t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}var v=i(9033);let g=({children:e,exitBeforeEnter:t,custom:i,initial:n=!0,onExitComplete:o,presenceAffectsLayout:l=!0,mode:d="sync"})=>{(0,p.k)(!t,"Replace exitBeforeEnter with mode='wait'");let h=(0,r.useMemo)(()=>f(e),[e]),g=h.map(m),y=(0,r.useRef)(!0),w=(0,r.useRef)(h),b=(0,a.h)(()=>new Map),[x,S]=(0,r.useState)(h),[T,P]=(0,r.useState)(h);(0,v.L)(()=>{y.current=!1,w.current=h;for(let e=0;e<T.length;e++){let t=m(T[e]);g.includes(t)?b.delete(t):!0!==b.get(t)&&b.set(t,!1)}},[T,g.length,g.join("-")]);let E=[];if(h!==x){let e=[...h];for(let t=0;t<T.length;t++){let i=T[t],s=m(i);g.includes(s)||(e.splice(t,0,i),E.push(i))}"wait"===d&&E.length&&(e=E),P(f(e)),S(h);return}let{forceRender:C}=(0,r.useContext)(c.p);return(0,s.jsx)(s.Fragment,{children:T.map(e=>{let t=m(e),r=h===T||g.includes(t);return(0,s.jsx)(u,{isPresent:r,initial:(!y.current||!!n)&&void 0,custom:r?void 0:i,presenceAffectsLayout:l,mode:d,onExitComplete:r?void 0:()=>{if(!b.has(t))return;b.set(t,!0);let e=!0;b.forEach(t=>{t||(e=!1)}),e&&(null==C||C(),P(w.current),o&&o())},children:e},t)})})}},5050:function(e,t,i){"use strict";i.d(t,{p:function(){return s}});let s=(0,i(2265).createContext)({})},9791:function(e,t,i){"use strict";i.d(t,{_:function(){return s}});let s=(0,i(2265).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},7797:function(e,t,i){"use strict";i.d(t,{O:function(){return s}});let s=(0,i(2265).createContext)(null)},4635:function(e,t,i){"use strict";let s;i.d(t,{E:function(){return nu}});var r,n=i(7437),a=i(2265),o=i(9791);let l=(0,a.createContext)({});var d=i(7797),u=i(9033);let h=(0,a.createContext)({strict:!1}),c=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),p="data-"+c("framerAppearId"),m={skipAnimations:!1,useManualTiming:!1},f=["read","resolveKeyframes","update","preRender","render","postRender"];function v(e,t){let i=!1,s=!0,r={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,a=f.reduce((e,t)=>(e[t]=function(e){let t=new Set,i=new Set,s=!1,r=!1,n=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1};function o(t){n.has(t)&&(l.schedule(t),e()),t(a)}let l={schedule:(e,r=!1,a=!1)=>{let o=a&&s?t:i;return r&&n.add(e),o.has(e)||o.add(e),e},cancel:e=>{i.delete(e),n.delete(e)},process:e=>{if(a=e,s){r=!0;return}s=!0,[t,i]=[i,t],i.clear(),t.forEach(o),s=!1,r&&(r=!1,l.process(e))}};return l}(n),e),{}),{read:o,resolveKeyframes:l,update:d,preRender:u,render:h,postRender:c}=a,p=()=>{let n=m.useManualTiming?r.timestamp:performance.now();i=!1,r.delta=s?1e3/60:Math.max(Math.min(n-r.timestamp,40),1),r.timestamp=n,r.isProcessing=!0,o.process(r),l.process(r),d.process(r),u.process(r),h.process(r),c.process(r),r.isProcessing=!1,i&&t&&(s=!1,e(p))},v=()=>{i=!0,s=!0,r.isProcessing||e(p)};return{schedule:f.reduce((e,t)=>{let s=a[t];return e[t]=(e,t=!1,r=!1)=>(i||v(),s.schedule(e,t,r)),e},{}),cancel:e=>{for(let t=0;t<f.length;t++)a[f[t]].cancel(e)},state:r,steps:a}}let{schedule:g,cancel:y}=v(queueMicrotask,!1);function w(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let b=(0,a.createContext)({}),x=!1;function S(){window.HandoffComplete=!0}function T(e){return"string"==typeof e||Array.isArray(e)}function P(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}let E=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],C=["initial",...E];function M(e){return P(e.animate)||C.some(t=>T(e[t]))}function A(e){return!!(M(e)||e.variants)}function k(e){return Array.isArray(e)?e.join(" "):e}let L={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},V={};for(let e in L)V[e]={isEnabled:t=>L[e].some(e=>!!t[e])};var D=i(7282),O=i(5050);let R=Symbol.for("motionComponentSymbol"),I=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function _(e){if("string"!=typeof e||e.includes("-"));else if(I.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}let F={},j=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],B=new Set(j);function z(e,{layout:t,layoutId:i}){return B.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!F[e]||"opacity"===e)}let $=e=>!!(e&&e.getVelocity),N=(e,t)=>t&&"number"==typeof e?t.transform(e):e,G=(e,t,i)=>i>t?t:i<e?e:i,H={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},W={...H,transform:e=>G(0,1,e)},U={...H,default:1},q=e=>Math.round(1e5*e)/1e5,Y=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,X=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,K=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu;function Z(e){return"string"==typeof e}let J=e=>({test:t=>Z(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),Q=J("deg"),ee=J("%"),et=J("px"),ei=J("vh"),es=J("vw"),er={...ee,parse:e=>ee.parse(e)/100,transform:e=>ee.transform(100*e)},en={...H,transform:Math.round},ea={borderWidth:et,borderTopWidth:et,borderRightWidth:et,borderBottomWidth:et,borderLeftWidth:et,borderRadius:et,radius:et,borderTopLeftRadius:et,borderTopRightRadius:et,borderBottomRightRadius:et,borderBottomLeftRadius:et,width:et,maxWidth:et,height:et,maxHeight:et,size:et,top:et,right:et,bottom:et,left:et,padding:et,paddingTop:et,paddingRight:et,paddingBottom:et,paddingLeft:et,margin:et,marginTop:et,marginRight:et,marginBottom:et,marginLeft:et,rotate:Q,rotateX:Q,rotateY:Q,rotateZ:Q,scale:U,scaleX:U,scaleY:U,scaleZ:U,skew:Q,skewX:Q,skewY:Q,distance:et,translateX:et,translateY:et,translateZ:et,x:et,y:et,z:et,perspective:et,transformPerspective:et,opacity:W,originX:er,originY:er,originZ:et,zIndex:en,backgroundPositionX:et,backgroundPositionY:et,fillOpacity:W,strokeOpacity:W,numOctaves:en},eo={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},el=j.length,ed=e=>t=>"string"==typeof t&&t.startsWith(e),eu=ed("--"),eh=ed("var(--"),ec=e=>!!eh(e)&&ep.test(e.split("/*")[0].trim()),ep=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function em(e,t,i){let{style:s,vars:r,transformOrigin:n}=e,a=!1,o=!1;for(let e in t){let i=t[e];if(B.has(e)){a=!0;continue}if(eu(e)){r[e]=i;continue}{let t=N(i,ea[e]);e.startsWith("origin")?(o=!0,n[e]=t):s[e]=t}}if(!t.transform&&(a||i?s.transform=function(e,t,i){let s="",r=!0;for(let n=0;n<el;n++){let a=j[n],o=e[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===(a.startsWith("scale")?1:0):0===parseFloat(o))||i){let e=N(o,ea[a]);if(!l){r=!1;let t=eo[a]||a;s+=`${t}(${e}) `}i&&(t[a]=e)}}return s=s.trim(),i?s=i(t,r?"":s):r&&(s="none"),s}(t,e.transform,i):s.transform&&(s.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=n;s.transformOrigin=`${e} ${t} ${i}`}}let ef=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ev(e,t,i){for(let s in t)$(t[s])||z(s,i)||(e[s]=t[s])}let eg=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ey(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||eg.has(e)}let ew=e=>!ey(e);try{(r=require("@emotion/is-prop-valid").default)&&(ew=e=>e.startsWith("on")?!ey(e):r(e))}catch(e){}function eb(e,t,i){return"string"==typeof e?e:et.transform(t+i*e)}let ex={offset:"stroke-dashoffset",array:"stroke-dasharray"},eS={offset:"strokeDashoffset",array:"strokeDasharray"};function eT(e,{attrX:t,attrY:i,attrScale:s,originX:r,originY:n,pathLength:a,pathSpacing:o=1,pathOffset:l=0,...d},u,h){if(em(e,d,h),u){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:c,style:p,dimensions:m}=e;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==r||void 0!==n||p.transform)&&(p.transformOrigin=function(e,t,i){let s=eb(t,e.x,e.width),r=eb(i,e.y,e.height);return`${s} ${r}`}(m,void 0!==r?r:.5,void 0!==n?n:.5)),void 0!==t&&(c.x=t),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==a&&function(e,t,i=1,s=0,r=!0){e.pathLength=1;let n=r?ex:eS;e[n.offset]=et.transform(-s);let a=et.transform(t),o=et.transform(i);e[n.array]=`${a} ${o}`}(c,a,o,l,!1)}let eP=()=>({...ef(),attrs:{}}),eE=e=>"string"==typeof e&&"svg"===e.toLowerCase();function eC(e,{style:t,vars:i},s,r){for(let n in Object.assign(e.style,t,r&&r.getProjectionStyles(s)),i)e.style.setProperty(n,i[n])}let eM=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function eA(e,t,i,s){for(let i in eC(e,t,void 0,s),t.attrs)e.setAttribute(eM.has(i)?i:c(i),t.attrs[i])}function ek(e,t,i){var s;let{style:r}=e,n={};for(let a in r)($(r[a])||t.style&&$(t.style[a])||z(a,e)||(null===(s=null==i?void 0:i.getValue(a))||void 0===s?void 0:s.liveStyle)!==void 0)&&(n[a]=r[a]);return i&&r&&"string"==typeof r.willChange&&(i.applyWillChange=!1),n}function eL(e,t,i){let s=ek(e,t,i);for(let i in e)($(e[i])||$(t[i]))&&(s[-1!==j.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return s}function eV(e){let t=[{},{}];return null==e||e.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function eD(e,t,i,s){if("function"==typeof t){let[r,n]=eV(s);t=t(void 0!==i?i:e.custom,r,n)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[r,n]=eV(s);t=t(void 0!==i?i:e.custom,r,n)}return t}var eO=i(458);let eR=e=>Array.isArray(e),eI=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),e_=e=>eR(e)?e[e.length-1]||0:e;function eF(e){let t=$(e)?e.get():e;return eI(t)?t.toValue():t}let ej=new Set(["opacity","clipPath","filter","transform"]);function eB(e){return B.has(e)?"transform":ej.has(e)?c(e):void 0}function ez(e,t){-1===e.indexOf(t)&&e.push(t)}function e$(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}let eN=e=>(t,i)=>{let s=(0,a.useContext)(l),r=(0,a.useContext)(d.O),n=()=>(function({applyWillChange:e=!1,scrapeMotionValuesFromProps:t,createRenderState:i,onMount:s},r,n,a,o){let l={latestValues:function(e,t,i,s,r){var n;let a={},o=[],l=s&&(null===(n=e.style)||void 0===n?void 0:n.willChange)===void 0,d=r(e,{});for(let e in d)a[e]=eF(d[e]);let{initial:u,animate:h}=e,c=M(e),p=A(e);t&&p&&!c&&!1!==e.inherit&&(void 0===u&&(u=t.initial),void 0===h&&(h=t.animate));let m=!!i&&!1===i.initial,f=(m=m||!1===u)?h:u;return f&&"boolean"!=typeof f&&!P(f)&&eG(e,f,(e,t)=>{for(let t in e){let i=e[t];if(Array.isArray(i)){let e=m?i.length-1:0;i=i[e]}null!==i&&(a[t]=i)}for(let e in t)a[e]=t[e]}),l&&(h&&!1!==u&&!P(h)&&eG(e,h,e=>{for(let t in e)!function(e,t){let i=eB(t);i&&ez(e,i)}(o,t)}),o.length&&(a.willChange=o.join(","))),a}(r,n,a,!o&&e,t),renderState:i()};return s&&(l.mount=e=>s(r,e,l)),l})(e,t,s,r,i);return i?n():(0,eO.h)(n)};function eG(e,t,i){let s=Array.isArray(t)?t:[t];for(let t=0;t<s.length;t++){let r=eD(e,s[t]);if(r){let{transitionEnd:e,transition:t,...s}=r;i(s,e)}}}var eH=i(9276);let{schedule:eW,cancel:eU,state:eq,steps:eY}=v("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:eH.Z,!0),eX={useVisualState:eN({scrapeMotionValuesFromProps:eL,createRenderState:eP,onMount:(e,t,{renderState:i,latestValues:s})=>{eW.read(()=>{try{i.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){i.dimensions={x:0,y:0,width:0,height:0}}}),eW.render(()=>{eT(i,s,eE(t.tagName),e.transformTemplate),eA(t,i)})}})},eK={useVisualState:eN({applyWillChange:!0,scrapeMotionValuesFromProps:ek,createRenderState:ef})};function eZ(e,t,i,s={passive:!0}){return e.addEventListener(t,i,s),()=>e.removeEventListener(t,i)}let eJ=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eQ(e,t="page"){return{point:{x:e[`${t}X`],y:e[`${t}Y`]}}}let e0=e=>t=>eJ(t)&&e(t,eQ(t));function e1(e,t,i,s){return eZ(e,t,e0(i),s)}let e2=(e,t)=>i=>t(e(i)),e5=(...e)=>e.reduce(e2);function e3(e){let t=null;return()=>null===t&&(t=e,()=>{t=null})}let e9=e3("dragHorizontal"),e6=e3("dragVertical");function e4(e){let t=!1;if("y"===e)t=e6();else if("x"===e)t=e9();else{let e=e9(),i=e6();e&&i?t=()=>{e(),i()}:(e&&e(),i&&i())}return t}function e7(){let e=e4(!0);return!e||(e(),!1)}class e8{constructor(e){this.isMounted=!1,this.node=e}update(){}}function te(e,t){let i=t?"onHoverStart":"onHoverEnd";return e1(e.current,t?"pointerenter":"pointerleave",(s,r)=>{if("touch"===s.pointerType||e7())return;let n=e.getProps();e.animationState&&n.whileHover&&e.animationState.setActive("whileHover",t);let a=n[i];a&&eW.postRender(()=>a(s,r))},{passive:!e.getProps()[i]})}class tt extends e8{mount(){this.unmount=e5(te(this.node,!0),te(this.node,!1))}unmount(){}}class ti extends e8{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=e5(eZ(this.node.current,"focus",()=>this.onFocus()),eZ(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ts=(e,t)=>!!t&&(e===t||ts(e,t.parentElement));function tr(e,t){if(!t)return;let i=new PointerEvent("pointer"+e);t(i,eQ(i))}class tn extends e8{constructor(){super(...arguments),this.removeStartListeners=eH.Z,this.removeEndListeners=eH.Z,this.removeAccessibleListeners=eH.Z,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();let i=this.node.getProps(),s=e1(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;let{onTap:i,onTapCancel:s,globalTapTarget:r}=this.node.getProps(),n=r||ts(this.node.current,e.target)?i:s;n&&eW.update(()=>n(e,t))},{passive:!(i.onTap||i.onPointerUp)}),r=e1(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(i.onTapCancel||i.onPointerCancel)});this.removeEndListeners=e5(s,r),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=eZ(this.node.current,"keydown",e=>{"Enter"!==e.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=eZ(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&tr("up",(e,t)=>{let{onTap:i}=this.node.getProps();i&&eW.postRender(()=>i(e,t))})}),tr("down",(e,t)=>{this.startPress(e,t)}))}),t=eZ(this.node.current,"blur",()=>{this.isPressing&&tr("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=e5(e,t)}}startPress(e,t){this.isPressing=!0;let{onTapStart:i,whileTap:s}=this.node.getProps();s&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),i&&eW.postRender(()=>i(e,t))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!e7()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:i}=this.node.getProps();i&&eW.postRender(()=>i(e,t))}mount(){let e=this.node.getProps(),t=e1(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),i=eZ(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=e5(t,i)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let ta=new WeakMap,to=new WeakMap,tl=e=>{let t=ta.get(e.target);t&&t(e)},td=e=>{e.forEach(tl)},tu={some:0,all:1};class th extends e8{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:s="some",once:r}=e,n={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:tu[s]};return function(e,t,i){let s=function({root:e,...t}){let i=e||document;to.has(i)||to.set(i,{});let s=to.get(i),r=JSON.stringify(t);return s[r]||(s[r]=new IntersectionObserver(td,{root:e,...t})),s[r]}(t);return ta.set(e,i),s.observe(e),()=>{ta.delete(e),s.unobserve(e)}}(this.node.current,n,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,r&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),n=t?i:s;n&&n(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}function tc(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let s=0;s<i;s++)if(t[s]!==e[s])return!1;return!0}function tp(e,t,i){let s=e.getProps();return eD(s,t,void 0!==i?i:s.custom,e)}let tm=e=>1e3*e,tf=e=>e/1e3,tv={type:"spring",stiffness:500,damping:25,restSpeed:10},tg=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),ty={type:"keyframes",duration:.8},tw={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},tb=(e,{keyframes:t})=>t.length>2?ty:B.has(e)?e.startsWith("scale")?tg(t[1]):tv:tw;function tx(e,t){return e[t]||e.default||e}let tS={current:!1},tT=e=>null!==e;function tP(e,{repeat:t,repeatType:i="loop"},s){let r=e.filter(tT),n=t&&"loop"!==i&&t%2==1?0:r.length-1;return n&&void 0!==s?s:r[n]}function tE(){s=void 0}let tC={now:()=>(void 0===s&&tC.set(eq.isProcessing||m.useManualTiming?eq.timestamp:performance.now()),s),set:e=>{s=e,queueMicrotask(tE)}},tM=e=>/^0[^.\s]+$/u.test(e);var tA=i(9047);let tk=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),tL=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tV=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),tD=e=>e===H||e===et,tO=(e,t)=>parseFloat(e.split(", ")[t]),tR=(e,t)=>(i,{transform:s})=>{if("none"===s||!s)return 0;let r=s.match(/^matrix3d\((.+)\)$/u);if(r)return tO(r[1],t);{let t=s.match(/^matrix\((.+)\)$/u);return t?tO(t[1],e):0}},tI=new Set(["x","y","z"]),t_=j.filter(e=>!tI.has(e)),tF={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:tR(4,13),y:tR(5,14)};tF.translateX=tF.x,tF.translateY=tF.y;let tj=e=>t=>t.test(e),tB=[H,et,ee,Q,es,ei,{test:e=>"auto"===e,parse:e=>e}],tz=e=>tB.find(tj(e)),t$=new Set,tN=!1,tG=!1;function tH(){if(tG){let e=Array.from(t$).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return t_.forEach(i=>{let s=e.getValue(i);void 0!==s&&(t.push([i,s.get()]),s.set(i.startsWith("scale")?1:0))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{var s;null===(s=e.getValue(t))||void 0===s||s.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tG=!1,tN=!1,t$.forEach(e=>e.complete()),t$.clear()}function tW(){t$.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tG=!0)})}class tU{constructor(e,t,i,s,r,n=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=s,this.element=r,this.isAsync=n}scheduleResolve(){this.isScheduled=!0,this.isAsync?(t$.add(this),tN||(tN=!0,eW.read(tW),eW.resolveKeyframes(tH))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:s}=this;for(let r=0;r<e.length;r++)if(null===e[r]){if(0===r){let r=null==s?void 0:s.get(),n=e[e.length-1];if(void 0!==r)e[0]=r;else if(i&&t){let s=i.readValue(t,n);null!=s&&(e[0]=s)}void 0===e[0]&&(e[0]=n),s&&void 0===r&&s.set(e[0])}else e[r]=e[r-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),t$.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,t$.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let tq=(e,t)=>i=>!!(Z(i)&&K.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),tY=(e,t,i)=>s=>{if(!Z(s))return s;let[r,n,a,o]=s.match(Y);return{[e]:parseFloat(r),[t]:parseFloat(n),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tX=e=>G(0,255,e),tK={...H,transform:e=>Math.round(tX(e))},tZ={test:tq("rgb","red"),parse:tY("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:s=1})=>"rgba("+tK.transform(e)+", "+tK.transform(t)+", "+tK.transform(i)+", "+q(W.transform(s))+")"},tJ={test:tq("#"),parse:function(e){let t="",i="",s="",r="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),s=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),s=e.substring(3,4),r=e.substring(4,5),t+=t,i+=i,s+=s,r+=r),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}},transform:tZ.transform},tQ={test:tq("hsl","hue"),parse:tY("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:s=1})=>"hsla("+Math.round(e)+", "+ee.transform(q(t))+", "+ee.transform(q(i))+", "+q(W.transform(s))+")"},t0={test:e=>tZ.test(e)||tJ.test(e)||tQ.test(e),parse:e=>tZ.test(e)?tZ.parse(e):tQ.test(e)?tQ.parse(e):tJ.parse(e),transform:e=>Z(e)?e:e.hasOwnProperty("red")?tZ.transform(e):tQ.transform(e)},t1="number",t2="color",t5=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function t3(e){let t=e.toString(),i=[],s={color:[],number:[],var:[]},r=[],n=0,a=t.replace(t5,e=>(t0.test(e)?(s.color.push(n),r.push(t2),i.push(t0.parse(e))):e.startsWith("var(")?(s.var.push(n),r.push("var"),i.push(e)):(s.number.push(n),r.push(t1),i.push(parseFloat(e))),++n,"${}")).split("${}");return{values:i,split:a,indexes:s,types:r}}function t9(e){return t3(e).values}function t6(e){let{split:t,types:i}=t3(e),s=t.length;return e=>{let r="";for(let n=0;n<s;n++)if(r+=t[n],void 0!==e[n]){let t=i[n];t===t1?r+=q(e[n]):t===t2?r+=t0.transform(e[n]):r+=e[n]}return r}}let t4=e=>"number"==typeof e?0:e,t7={test:function(e){var t,i;return isNaN(e)&&Z(e)&&((null===(t=e.match(Y))||void 0===t?void 0:t.length)||0)+((null===(i=e.match(X))||void 0===i?void 0:i.length)||0)>0},parse:t9,createTransformer:t6,getAnimatableNone:function(e){let t=t9(e);return t6(e)(t.map(t4))}},t8=new Set(["brightness","contrast","saturate","opacity"]);function ie(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[s]=i.match(Y)||[];if(!s)return e;let r=i.replace(s,""),n=t8.has(t)?1:0;return s!==i&&(n*=100),t+"("+n+r+")"}let it=/\b([a-z-]*)\(.*?\)/gu,ii={...t7,getAnimatableNone:e=>{let t=e.match(it);return t?t.map(ie).join(" "):e}},is={...ea,color:t0,backgroundColor:t0,outlineColor:t0,fill:t0,stroke:t0,borderColor:t0,borderTopColor:t0,borderRightColor:t0,borderBottomColor:t0,borderLeftColor:t0,filter:ii,WebkitFilter:ii},ir=e=>is[e];function ia(e,t){let i=ir(e);return i!==ii&&(i=t7),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let io=new Set(["auto","none","0"]);class il extends tU{constructor(e,t,i,s){super(e,t,i,s,null==s?void 0:s.owner,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let s=e[i];if("string"==typeof s&&ec(s=s.trim())){let r=function e(t,i,s=1){(0,tA.k)(s<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[r,n]=function(e){let t=tL.exec(e);if(!t)return[,];let[,i,s,r]=t;return[`--${null!=i?i:s}`,r]}(t);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let e=a.trim();return tk(e)?parseFloat(e):e}return ec(n)?e(n,i,s+1):n}(s,t.current);void 0!==r&&(e[i]=r),i===e.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!tV.has(i)||2!==e.length)return;let[s,r]=e,n=tz(s),a=tz(r);if(n!==a){if(tD(n)&&tD(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var s;("number"==typeof(s=e[t])?0===s:null===s||"none"===s||"0"===s||tM(s))&&i.push(t)}i.length&&function(e,t,i){let s,r=0;for(;r<e.length&&!s;){let t=e[r];"string"==typeof t&&!io.has(t)&&t3(t).values.length&&(s=e[r]),r++}if(s&&i)for(let r of t)e[r]=ia(i,s)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tF[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let s=t[t.length-1];void 0!==s&&e.getValue(i,s).jump(s,!1)}measureEndState(){var e;let{element:t,name:i,unresolvedKeyframes:s}=this;if(!t.current)return;let r=t.getValue(i);r&&r.jump(this.measuredOrigin,!1);let n=s.length-1,a=s[n];s[n]=tF[i](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),(null===(e=this.removedTransforms)||void 0===e?void 0:e.length)&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}function id(e){let t;return()=>(void 0===t&&(t=e()),t)}let iu=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(t7.test(e)||"0"===e)&&!e.startsWith("url("));class ih{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:n="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.options={autoplay:e,delay:t,type:i,repeat:s,repeatDelay:r,repeatType:n,...a},this.updateFinishedPromise()}get resolved(){return this._resolved||this.hasAttemptedResolve||(tW(),tH()),this._resolved}onKeyframesResolved(e,t){this.hasAttemptedResolve=!0;let{name:i,type:s,velocity:r,delay:n,onComplete:a,onUpdate:o,isGenerator:l}=this.options;if(!l&&!function(e,t,i,s){let r=e[0];if(null===r)return!1;if("display"===t||"visibility"===t)return!0;let n=e[e.length-1],a=iu(r,t),o=iu(n,t);return(0,tA.K)(a===o,`You are trying to animate ${t} from "${r}" to "${n}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${n} via the \`style\` property.`),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||"spring"===i&&s)}(e,i,s,r)){if(tS.current||!n){null==o||o(tP(e,this.options,t)),null==a||a(),this.resolveFinishedPromise();return}this.options.duration=0}let d=this.initPlayback(e,t);!1!==d&&(this._resolved={keyframes:e,finalKeyframe:t,...d},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}function ic(e,t,i){var s,r;let n=Math.max(t-5,0);return s=i-e(n),(r=t-n)?1e3/r*s:0}function ip(e,t){return e*Math.sqrt(1-t*t)}let im=["duration","bounce"],iv=["stiffness","damping","mass"];function ig(e,t){return t.some(t=>void 0!==e[t])}function iy({keyframes:e,restDelta:t,restSpeed:i,...s}){let r;let n=e[0],a=e[e.length-1],o={done:!1,value:n},{stiffness:l,damping:d,mass:u,duration:h,velocity:c,isResolvedFromDuration:p}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!ig(e,iv)&&ig(e,im)){let i=function({duration:e=800,bounce:t=.25,velocity:i=0,mass:s=1}){let r,n;(0,tA.K)(e<=tm(10),"Spring duration must be 10 seconds or less");let a=1-t;a=G(.05,1,a),e=G(.01,10,tf(e)),a<1?(r=t=>{let s=t*a,r=s*e;return .001-(s-i)/ip(t,a)*Math.exp(-r)},n=t=>{let s=t*a*e,n=Math.pow(a,2)*Math.pow(t,2)*e,o=ip(Math.pow(t,2),a);return(s*i+i-n)*Math.exp(-s)*(-r(t)+.001>0?-1:1)/o}):(r=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),n=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let s=i;for(let i=1;i<12;i++)s-=e(s)/t(s);return s}(r,n,5/e);if(e=tm(e),isNaN(o))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(o,2)*s;return{stiffness:t,damping:2*a*Math.sqrt(s*t),duration:e}}}(e);(t={...t,...i,mass:1}).isResolvedFromDuration=!0}return t}({...s,velocity:-tf(s.velocity||0)}),m=c||0,f=d/(2*Math.sqrt(l*u)),v=a-n,g=tf(Math.sqrt(l/u)),y=5>Math.abs(v);if(i||(i=y?.01:2),t||(t=y?.005:.5),f<1){let e=ip(g,f);r=t=>a-Math.exp(-f*g*t)*((m+f*g*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===f)r=e=>a-Math.exp(-g*e)*(v+(m+g*v)*e);else{let e=g*Math.sqrt(f*f-1);r=t=>{let i=Math.exp(-f*g*t),s=Math.min(e*t,300);return a-i*((m+f*g*v)*Math.sinh(s)+e*v*Math.cosh(s))/e}}return{calculatedDuration:p&&h||null,next:e=>{let s=r(e);if(p)o.done=e>=h;else{let n=m;0!==e&&(n=f<1?ic(r,e,s):0);let l=Math.abs(n)<=i,d=Math.abs(a-s)<=t;o.done=l&&d}return o.value=o.done?a:s,o}}}function iw({keyframes:e,velocity:t=0,power:i=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:n=500,modifyTarget:a,min:o,max:l,restDelta:d=.5,restSpeed:u}){let h,c;let p=e[0],m={done:!1,value:p},f=e=>void 0!==o&&e<o||void 0!==l&&e>l,v=e=>void 0===o?l:void 0===l?o:Math.abs(o-e)<Math.abs(l-e)?o:l,g=i*t,y=p+g,w=void 0===a?y:a(y);w!==y&&(g=w-p);let b=e=>-g*Math.exp(-e/s),x=e=>w+b(e),S=e=>{let t=b(e),i=x(e);m.done=Math.abs(t)<=d,m.value=m.done?w:i},T=e=>{f(m.value)&&(h=e,c=iy({keyframes:[m.value,v(m.value)],velocity:ic(x,e,m.value),damping:r,stiffness:n,restDelta:d,restSpeed:u}))};return T(0),{calculatedDuration:null,next:e=>{let t=!1;return(c||void 0!==h||(t=!0,S(e),T(e)),void 0!==h&&e>=h)?c.next(e-h):(t||S(e),m)}}}let ib=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function ix(e,t,i,s){if(e===t&&i===s)return eH.Z;let r=t=>(function(e,t,i,s,r){let n,a;let o=0;do(n=ib(a=t+(i-t)/2,s,r)-e)>0?i=a:t=a;while(Math.abs(n)>1e-7&&++o<12);return a})(t,0,1,e,i);return e=>0===e||1===e?e:ib(r(e),t,s)}let iS=ix(.42,0,1,1),iT=ix(0,0,.58,1),iP=ix(.42,0,.58,1),iE=e=>Array.isArray(e)&&"number"!=typeof e[0],iC=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,iM=e=>t=>1-e(1-t),iA=e=>1-Math.sin(Math.acos(e)),ik=iM(iA),iL=iC(iA),iV=ix(.33,1.53,.69,.99),iD=iM(iV),iO=iC(iD),iR={linear:eH.Z,easeIn:iS,easeInOut:iP,easeOut:iT,circIn:iA,circInOut:iL,circOut:ik,backIn:iD,backInOut:iO,backOut:iV,anticipate:e=>(e*=2)<1?.5*iD(e):.5*(2-Math.pow(2,-10*(e-1)))},iI=e=>{if(Array.isArray(e)){(0,tA.k)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,s,r]=e;return ix(t,i,s,r)}return"string"==typeof e?((0,tA.k)(void 0!==iR[e],`Invalid easing type '${e}'`),iR[e]):e},i_=(e,t,i)=>{let s=t-e;return 0===s?1:(i-e)/s},iF=(e,t,i)=>e+(t-e)*i;function ij(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function iB(e,t){return i=>i>0?t:e}let iz=(e,t,i)=>{let s=e*e,r=i*(t*t-s)+s;return r<0?0:Math.sqrt(r)},i$=[tJ,tZ,tQ],iN=e=>i$.find(t=>t.test(e));function iG(e){let t=iN(e);if((0,tA.K)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let i=t.parse(e);return t===tQ&&(i=function({hue:e,saturation:t,lightness:i,alpha:s}){e/=360,i/=100;let r=0,n=0,a=0;if(t/=100){let s=i<.5?i*(1+t):i+t-i*t,o=2*i-s;r=ij(o,s,e+1/3),n=ij(o,s,e),a=ij(o,s,e-1/3)}else r=n=a=i;return{red:Math.round(255*r),green:Math.round(255*n),blue:Math.round(255*a),alpha:s}}(i)),i}let iH=(e,t)=>{let i=iG(e),s=iG(t);if(!i||!s)return iB(e,t);let r={...i};return e=>(r.red=iz(i.red,s.red,e),r.green=iz(i.green,s.green,e),r.blue=iz(i.blue,s.blue,e),r.alpha=iF(i.alpha,s.alpha,e),tZ.transform(r))},iW=new Set(["none","hidden"]);function iU(e,t){return i=>iF(e,t,i)}function iq(e){return"number"==typeof e?iU:"string"==typeof e?ec(e)?iB:t0.test(e)?iH:iK:Array.isArray(e)?iY:"object"==typeof e?t0.test(e)?iH:iX:iB}function iY(e,t){let i=[...e],s=i.length,r=e.map((e,i)=>iq(e)(e,t[i]));return e=>{for(let t=0;t<s;t++)i[t]=r[t](e);return i}}function iX(e,t){let i={...e,...t},s={};for(let r in i)void 0!==e[r]&&void 0!==t[r]&&(s[r]=iq(e[r])(e[r],t[r]));return e=>{for(let t in s)i[t]=s[t](e);return i}}let iK=(e,t)=>{let i=t7.createTransformer(t),s=t3(e),r=t3(t);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?iW.has(e)&&!r.values.length||iW.has(t)&&!s.values.length?iW.has(e)?i=>i<=0?e:t:i=>i>=1?t:e:e5(iY(function(e,t){var i;let s=[],r={color:0,var:0,number:0};for(let n=0;n<t.values.length;n++){let a=t.types[n],o=e.indexes[a][r[a]],l=null!==(i=e.values[o])&&void 0!==i?i:0;s[n]=l,r[a]++}return s}(s,r),r.values),i):((0,tA.K)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),iB(e,t))};function iZ(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?iF(e,t,i):iq(e)(e,t)}function iJ({duration:e=300,keyframes:t,times:i,ease:s="easeInOut"}){let r=iE(s)?s.map(iI):iI(s),n={done:!1,value:t[0]},a=function(e,t,{clamp:i=!0,ease:s,mixer:r}={}){let n=e.length;if((0,tA.k)(n===t.length,"Both input and output ranges must be the same length"),1===n)return()=>t[0];if(2===n&&e[0]===e[1])return()=>t[1];e[0]>e[n-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,i){let s=[],r=i||iZ,n=e.length-1;for(let i=0;i<n;i++){let n=r(e[i],e[i+1]);t&&(n=e5(Array.isArray(t)?t[i]||eH.Z:t,n)),s.push(n)}return s}(t,s,r),o=a.length,l=t=>{let i=0;if(o>1)for(;i<e.length-2&&!(t<e[i+1]);i++);let s=i_(e[i],e[i+1],t);return a[i](s)};return i?t=>l(G(e[0],e[n-1],t)):l}((i&&i.length===t.length?i:function(e){let t=[0];return function(e,t){let i=e[e.length-1];for(let s=1;s<=t;s++){let r=i_(0,t,s);e.push(iF(i,1,r))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(r)?r:t.map(()=>r||iP).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(n.value=a(t),n.done=t>=e,n)}}let iQ=e=>{let t=({timestamp:t})=>e(t);return{start:()=>eW.update(t,!0),stop:()=>eU(t),now:()=>eq.isProcessing?eq.timestamp:tC.now()}},i0={decay:iw,inertia:iw,tween:iJ,keyframes:iJ,spring:iy},i1=e=>e/100;class i2 extends ih{constructor({KeyframeResolver:e=tU,...t}){super(t),this.holdTime=null,this.startTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()};let{name:i,motionValue:s,keyframes:r}=this.options,n=(e,t)=>this.onKeyframesResolved(e,t);i&&s&&s.owner?this.resolver=s.owner.resolveKeyframes(r,n,i,s):this.resolver=new e(r,n,i,s),this.resolver.scheduleResolve()}initPlayback(e){let t,i;let{type:s="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:a,velocity:o=0}=this.options,l=i0[s]||iJ;l!==iJ&&"number"!=typeof e[0]&&(t=e5(i1,iZ(e[0],e[1])),e=[0,100]);let d=l({...this.options,keyframes:e});"mirror"===a&&(i=l({...this.options,keyframes:[...e].reverse(),velocity:-o})),null===d.calculatedDuration&&(d.calculatedDuration=function(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}(d));let{calculatedDuration:u}=d,h=u+n;return{generator:d,mirroredGenerator:i,mapPercentToKeyframes:t,calculatedDuration:u,resolvedDuration:h,totalDuration:h*(r+1)-n}}onPostResolved(){let{autoplay:e=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){let{resolved:i}=this;if(!i){let{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}let{finalKeyframe:s,generator:r,mirroredGenerator:n,mapPercentToKeyframes:a,keyframes:o,calculatedDuration:l,totalDuration:d,resolvedDuration:u}=i;if(null===this.startTime)return r.next(0);let{delay:h,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-d/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;let v=this.currentTime-h*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>d;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let y=this.currentTime,w=r;if(c){let e=Math.min(this.currentTime,d)/u,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/u)):"mirror"===p&&(w=n)),y=G(0,1,i)*u}let b=g?{done:!1,value:o[0]}:w.next(y);a&&(b.value=a(b.value));let{done:x}=b;g||null===l||(x=this.speed>=0?this.currentTime>=d:this.currentTime<=0);let S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return S&&void 0!==s&&(b.value=tP(o,this.options,s)),f&&f(b.value),S&&this.finish(),b}get duration(){let{resolved:e}=this;return e?tf(e.calculatedDuration):0}get time(){return tf(this.currentTime)}set time(e){e=tm(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=tf(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:e=iQ,onPlay:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let i=this.driver.now();null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime&&"finished"!==this.state||(this.startTime=i),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(e=this.currentTime)&&void 0!==e?e:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}let i5=e=>Array.isArray(e)&&"number"==typeof e[0],i3=([e,t,i,s])=>`cubic-bezier(${e}, ${t}, ${i}, ${s})`,i9={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:i3([0,.65,.55,1]),circOut:i3([.55,0,1,.45]),backIn:i3([.31,.01,.66,-.59]),backOut:i3([.33,1.53,.69,.99])};function i6(e){return i4(e)||i9.easeOut}function i4(e){if(e)return i5(e)?i3(e):Array.isArray(e)?e.map(i6):i9[e]}let i7=id(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class i8 extends ih{constructor(e){super(e);let{name:t,motionValue:i,keyframes:s}=this.options;this.resolver=new il(s,(e,t)=>this.onKeyframesResolved(e,t),t,i),this.resolver.scheduleResolve()}initPlayback(e,t){var i,s;let{duration:r=300,times:n,ease:a,type:o,motionValue:l,name:d}=this.options;if(!(null===(i=l.owner)||void 0===i?void 0:i.current))return!1;if("spring"===(s=this.options).type||!function e(t){return!!(!t||"string"==typeof t&&t in i9||i5(t)||Array.isArray(t)&&t.every(e))}(s.ease)){let{onComplete:t,onUpdate:i,motionValue:s,...l}=this.options,d=function(e,t){let i=new i2({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0}),s={done:!1,value:e[0]},r=[],n=0;for(;!s.done&&n<2e4;)r.push((s=i.sample(n)).value),n+=10;return{times:void 0,keyframes:r,duration:n-10,ease:"linear"}}(e,l);1===(e=d.keyframes).length&&(e[1]=e[0]),r=d.duration,n=d.times,a=d.ease,o="keyframes"}let u=function(e,t,i,{delay:s=0,duration:r=300,repeat:n=0,repeatType:a="loop",ease:o,times:l}={}){let d={[t]:i};l&&(d.offset=l);let u=i4(o);return Array.isArray(u)&&(d.easing=u),e.animate(d,{delay:s,duration:r,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:n+1,direction:"reverse"===a?"alternate":"normal"})}(l.owner.current,d,e,{...this.options,duration:r,times:n,ease:a});return u.startTime=tC.now(),this.pendingTimeline?(u.timeline=this.pendingTimeline,this.pendingTimeline=void 0):u.onfinish=()=>{let{onComplete:i}=this.options;l.set(tP(e,this.options,t)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:u,duration:r,times:n,type:o,ease:a,keyframes:e}}get duration(){let{resolved:e}=this;if(!e)return 0;let{duration:t}=e;return tf(t)}get time(){let{resolved:e}=this;if(!e)return 0;let{animation:t}=e;return tf(t.currentTime||0)}set time(e){let{resolved:t}=this;if(!t)return;let{animation:i}=t;i.currentTime=tm(e)}get speed(){let{resolved:e}=this;if(!e)return 1;let{animation:t}=e;return t.playbackRate}set speed(e){let{resolved:t}=this;if(!t)return;let{animation:i}=t;i.playbackRate=e}get state(){let{resolved:e}=this;if(!e)return"idle";let{animation:t}=e;return t.playState}attachTimeline(e){if(this._resolved){let{resolved:t}=this;if(!t)return eH.Z;let{animation:i}=t;i.timeline=e,i.onfinish=null}else this.pendingTimeline=e;return eH.Z}play(){if(this.isStopped)return;let{resolved:e}=this;if(!e)return;let{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){let{resolved:e}=this;if(!e)return;let{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:e}=this;if(!e)return;let{animation:t,keyframes:i,duration:s,type:r,ease:n,times:a}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){let{motionValue:e,onUpdate:t,onComplete:o,...l}=this.options,d=new i2({...l,keyframes:i,duration:s,type:r,ease:n,times:a,isGenerator:!0}),u=tm(this.time);e.setWithVelocity(d.sample(u-10).value,d.sample(u).value,10)}let{onStop:o}=this.options;o&&o(),this.cancel()}complete(){let{resolved:e}=this;e&&e.animation.finish()}cancel(){let{resolved:e}=this;e&&e.animation.cancel()}static supports(e){let{motionValue:t,name:i,repeatDelay:s,repeatType:r,damping:n,type:a}=e;return i7()&&i&&ej.has(i)&&t&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate&&!s&&"mirror"!==r&&0!==n&&"inertia"!==a}}let se=id(()=>void 0!==window.ScrollTimeline);class st{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}then(e,t){return Promise.all(this.animations).then(e).catch(t)}getAll(e){return this.animations[0][e]}setAll(e,t){for(let i=0;i<this.animations.length;i++)this.animations[i][e]=t}attachTimeline(e){let t=this.animations.map(t=>{if(!se()||!t.attachTimeline)return t.pause(),function(e,t){let i;let s=()=>{let{currentTime:s}=t,r=(null===s?0:s.value)/100;i!==r&&e(r),i=r};return eW.update(s,!0),()=>eU(s)}(e=>{t.time=t.duration*e},e);t.attachTimeline(e)});return()=>{t.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}let si=(e,t,i,s={},r,n,a)=>o=>{let l=tx(s,e)||{},d=l.delay||s.delay||0,{elapsed:u=0}=s;u-=tm(d);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-u,onUpdate:e=>{t.set(e),l.onUpdate&&l.onUpdate(e)},onComplete:()=>{o(),l.onComplete&&l.onComplete(),a&&a()},onStop:a,name:e,motionValue:t,element:n?void 0:r};!function({when:e,delay:t,delayChildren:i,staggerChildren:s,staggerDirection:r,repeat:n,repeatType:a,repeatDelay:o,from:l,elapsed:d,...u}){return!!Object.keys(u).length}(l)&&(h={...h,...tb(e,h)}),h.duration&&(h.duration=tm(h.duration)),h.repeatDelay&&(h.repeatDelay=tm(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0!==h.delay||(c=!0)),(tS.current||m.skipAnimations)&&(c=!0,h.duration=0,h.delay=0),c&&!n&&void 0!==t.get()){let e=tP(h.keyframes,l);if(void 0!==e)return eW.update(()=>{h.onUpdate(e),h.onComplete()}),new st([])}return!n&&i8.supports(h)?new i8(h):new i2(h)};class ss{constructor(){this.subscriptions=[]}add(e){return ez(this.subscriptions,e),()=>e$(this.subscriptions,e)}notify(e,t,i){let s=this.subscriptions.length;if(s){if(1===s)this.subscriptions[0](e,t,i);else for(let r=0;r<s;r++){let s=this.subscriptions[r];s&&s(e,t,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let sr=e=>!isNaN(parseFloat(e)),sn={current:void 0};class sa{constructor(e,t={}){this.version="11.3.21",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let i=tC.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=tC.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=sr(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new ss);let i=this.events[e].add(t);return"change"===e?()=>{i(),eW.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return sn.current&&sn.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=tC.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function so(e,t){return new sa(e,t)}function sl(e){return e.getProps()[p]}class sd extends sa{constructor(){super(...arguments),this.output=[],this.counts=new Map}add(e){let t=eB(e);if(!t)return;let i=this.counts.get(t)||0;this.counts.set(t,i+1),0===i&&(this.output.push(t),this.update());let s=!1;return()=>{if(s)return;s=!0;let e=this.counts.get(t)-1;this.counts.set(t,e),0===e&&(e$(this.output,t),this.update())}}update(){this.set(this.output.length?this.output.join(", "):"auto")}}function su(e,t){var i,s;if(!e.applyWillChange)return;let r=e.getValue("willChange");if(r||(null===(i=e.props.style)||void 0===i?void 0:i.willChange)||(r=new sd("auto"),e.addValue("willChange",r)),$(s=r)&&s.add)return r.add(t)}function sh(e,t,{delay:i=0,transitionOverride:s,type:r}={}){var n;let{transition:a=e.getDefaultTransition(),transitionEnd:o,...l}=t;s&&(a=s);let d=[],u=r&&e.animationState&&e.animationState.getState()[r];for(let t in l){let s=e.getValue(t,null!==(n=e.latestValues[t])&&void 0!==n?n:null),r=l[t];if(void 0===r||u&&function({protectedKeys:e,needsAnimating:t},i){let s=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,s}(u,t))continue;let o={delay:i,elapsed:0,...tx(a||{},t)},h=!1;if(window.HandoffAppearAnimations){let i=sl(e);if(i){let e=window.HandoffAppearAnimations(i,t,s,eW);null!==e&&(o.elapsed=e,h=!0)}}s.start(si(t,s,r,e.shouldReduceMotion&&B.has(t)?{type:!1}:o,e,h,su(e,t)));let c=s.animation;c&&d.push(c)}return o&&Promise.all(d).then(()=>{eW.update(()=>{o&&function(e,t){let{transitionEnd:i={},transition:s={},...r}=tp(e,t)||{};for(let t in r={...r,...i}){let i=e_(r[t]);e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,so(i))}}(e,o)})}),d}function sc(e,t,i={}){var s;let r=tp(e,t,"exit"===i.type?null===(s=e.presenceContext)||void 0===s?void 0:s.custom:void 0),{transition:n=e.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let a=r?()=>Promise.all(sh(e,r,i)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=n;return function(e,t,i=0,s=0,r=1,n){let a=[],o=(e.variantChildren.size-1)*s,l=1===r?(e=0)=>e*s:(e=0)=>o-e*s;return Array.from(e.variantChildren).sort(sp).forEach((e,s)=>{e.notify("AnimationStart",t),a.push(sc(e,t,{...n,delay:i+l(s)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,r+s,a,o,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([a(),o(i.delay)]);{let[e,t]="beforeChildren"===l?[a,o]:[o,a];return e().then(()=>t())}}function sp(e,t){return e.sortNodePosition(t)}let sm=[...E].reverse(),sf=E.length;function sv(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function sg(){return{animate:sv(!0),whileInView:sv(),whileHover:sv(),whileTap:sv(),whileDrag:sv(),whileFocus:sv(),exit:sv()}}class sy extends e8{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let s;if(e.notify("AnimationStart",t),Array.isArray(t))s=Promise.all(t.map(t=>sc(e,t,i)));else if("string"==typeof t)s=sc(e,t,i);else{let r="function"==typeof t?tp(e,t,i.custom):t;s=Promise.all(sh(e,r,i))}return s.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=sg(),s=!0,r=t=>(i,s)=>{var r;let n=tp(e,s,"exit"===t?null===(r=e.presenceContext)||void 0===r?void 0:r.custom:void 0);if(n){let{transition:e,transitionEnd:t,...s}=n;i={...i,...s,...t}}return i};function n(n){let a=e.getProps(),o=e.getVariantContext(!0)||{},l=[],d=new Set,u={},h=1/0;for(let t=0;t<sf;t++){var c;let p=sm[t],m=i[p],f=void 0!==a[p]?a[p]:o[p],v=T(f),g=p===n?m.isActive:null;!1===g&&(h=t);let y=f===o[p]&&f!==a[p]&&v;if(y&&s&&e.manuallyAnimateOnMount&&(y=!1),m.protectedKeys={...u},!m.isActive&&null===g||!f&&!m.prevProp||P(f)||"boolean"==typeof f)continue;let w=(c=m.prevProp,("string"==typeof f?f!==c:!!Array.isArray(f)&&!tc(f,c))||p===n&&m.isActive&&!y&&v||t>h&&v),b=!1,x=Array.isArray(f)?f:[f],S=x.reduce(r(p),{});!1===g&&(S={});let{prevResolvedValues:E={}}=m,C={...E,...S},M=t=>{w=!0,d.has(t)&&(b=!0,d.delete(t)),m.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in C){let t=S[e],i=E[e];if(!u.hasOwnProperty(e))(eR(t)&&eR(i)?tc(t,i):t===i)?void 0!==t&&d.has(e)?M(e):m.protectedKeys[e]=!0:null!=t?M(e):d.add(e)}m.prevProp=f,m.prevResolvedValues=S,m.isActive&&(u={...u,...S}),s&&e.blockInitialAnimation&&(w=!1),w&&(!y||b)&&l.push(...x.map(e=>({animation:e,options:{type:p}})))}if(d.size){let t={};d.forEach(i=>{let s=e.getBaseTarget(i),r=e.getValue(i);r&&(r.liveStyle=!0),t[i]=null!=s?s:null}),l.push({animation:t})}let p=!!l.length;return s&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(p=!1),s=!1,p?t(l):Promise.resolve()}return{animateChanges:n,setActive:function(t,s){var r;if(i[t].isActive===s)return Promise.resolve();null===(r=e.variantChildren)||void 0===r||r.forEach(e=>{var i;return null===(i=e.animationState)||void 0===i?void 0:i.setActive(t,s)}),i[t].isActive=s;let a=n(t);for(let e in i)i[e].protectedKeys={};return a},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=sg(),s=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();P(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null===(e=this.unmountControls)||void 0===e||e.call(this)}}let sw=0;class sb extends e8{constructor(){super(...arguments),this.id=sw++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let s=this.node.animationState.setActive("exit",!e);t&&!e&&s.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}let sx=(e,t)=>Math.abs(e-t);class sS{constructor(e,t,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var e,t;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let i=sE(this.lastMoveEventInfo,this.history),s=null!==this.startEvent,r=(e=i.offset,t={x:0,y:0},Math.sqrt(sx(e.x,t.x)**2+sx(e.y,t.y)**2)>=3);if(!s&&!r)return;let{point:n}=i,{timestamp:a}=eq;this.history.push({...n,timestamp:a});let{onStart:o,onMove:l}=this.handlers;s||(o&&o(this.lastMoveEvent,i),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,i)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=sT(t,this.transformPagePoint),eW.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=sE("pointercancel"===e.type?this.lastMoveEventInfo:sT(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,n),s&&s(e,n)},!eJ(e))return;this.dragSnapToOrigin=r,this.handlers=t,this.transformPagePoint=i,this.contextWindow=s||window;let n=sT(eQ(e),this.transformPagePoint),{point:a}=n,{timestamp:o}=eq;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,sE(n,this.history)),this.removeListeners=e5(e1(this.contextWindow,"pointermove",this.handlePointerMove),e1(this.contextWindow,"pointerup",this.handlePointerUp),e1(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),eU(this.updatePoint)}}function sT(e,t){return t?{point:t(e.point)}:e}function sP(e,t){return{x:e.x-t.x,y:e.y-t.y}}function sE({point:e},t){return{point:e,delta:sP(e,sC(t)),offset:sP(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,s=null,r=sC(e);for(;i>=0&&(s=e[i],!(r.timestamp-s.timestamp>tm(.1)));)i--;if(!s)return{x:0,y:0};let n=tf(r.timestamp-s.timestamp);if(0===n)return{x:0,y:0};let a={x:(r.x-s.x)/n,y:(r.y-s.y)/n};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,0)}}function sC(e){return e[e.length-1]}function sM(e){return e.max-e.min}function sA(e,t,i,s=.5){e.origin=s,e.originPoint=iF(t.min,t.max,e.origin),e.scale=sM(i)/sM(t),e.translate=iF(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function sk(e,t,i,s){sA(e.x,t.x,i.x,s?s.originX:void 0),sA(e.y,t.y,i.y,s?s.originY:void 0)}function sL(e,t,i){e.min=i.min+t.min,e.max=e.min+sM(t)}function sV(e,t,i){e.min=t.min-i.min,e.max=e.min+sM(t)}function sD(e,t,i){sV(e.x,t.x,i.x),sV(e.y,t.y,i.y)}function sO(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function sR(e,t){let i=t.min-e.min,s=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,s]=[s,i]),{min:i,max:s}}function sI(e,t,i){return{min:s_(e,t),max:s_(e,i)}}function s_(e,t){return"number"==typeof e?e:e[t]||0}let sF=()=>({translate:0,scale:1,origin:0,originPoint:0}),sj=()=>({x:sF(),y:sF()}),sB=()=>({min:0,max:0}),sz=()=>({x:sB(),y:sB()});function s$(e){return[e("x"),e("y")]}function sN({top:e,left:t,right:i,bottom:s}){return{x:{min:t,max:i},y:{min:e,max:s}}}function sG(e){return void 0===e||1===e}function sH({scale:e,scaleX:t,scaleY:i}){return!sG(e)||!sG(t)||!sG(i)}function sW(e){return sH(e)||sU(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function sU(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function sq(e,t,i,s,r){return void 0!==r&&(e=s+r*(e-s)),s+i*(e-s)+t}function sY(e,t=0,i=1,s,r){e.min=sq(e.min,t,i,s,r),e.max=sq(e.max,t,i,s,r)}function sX(e,{x:t,y:i}){sY(e.x,t.translate,t.scale,t.originPoint),sY(e.y,i.translate,i.scale,i.originPoint)}function sK(e,t){e.min=e.min+t,e.max=e.max+t}function sZ(e,t,i,s,r=.5){let n=iF(e.min,e.max,r);sY(e,t,i,n,s)}function sJ(e,t){sZ(e.x,t.x,t.scaleX,t.scale,t.originX),sZ(e.y,t.y,t.scaleY,t.scale,t.originY)}function sQ(e,t){return sN(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),s=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(e.getBoundingClientRect(),t))}let s0=({current:e})=>e?e.ownerDocument.defaultView:null,s1=new WeakMap;class s2{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=sz(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new sS(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(eQ(e,"page").point)},onStart:(e,t)=>{var i;let{drag:s,dragPropagation:r,onDragStart:n}=this.getProps();if(s&&!r&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=e4(s),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),s$(e=>{let t=this.getAxisMotionValue(e).get()||0;if(ee.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[e];if(s){let e=sM(s);t=parseFloat(t)/100*e}}}this.originPoint[e]=t}),n&&eW.postRender(()=>n(e,t)),null===(i=this.removeWillChange)||void 0===i||i.call(this),this.removeWillChange=su(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:r,onDrag:n}=this.getProps();if(!i&&!this.openGlobalLock)return;let{offset:a}=t;if(s&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),n&&n(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>s$(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:s0(this.visualElement)})}stop(e,t){var i;null===(i=this.removeWillChange)||void 0===i||i.call(this);let s=this.isDragging;if(this.cancel(),!s)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&eW.postRender(()=>n(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:s}=this.getProps();if(!i||!s5(e,s,this.currentDirection))return;let r=this.getAxisMotionValue(e),n=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(n=function(e,{min:t,max:i},s){return void 0!==t&&e<t?e=s?iF(t,e,s.min):Math.max(e,t):void 0!==i&&e>i&&(e=s?iF(i,e,s.max):Math.min(e,i)),e}(n,this.constraints[e],this.elastic[e])),r.set(n)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,r=this.constraints;t&&w(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&s?this.constraints=function(e,{top:t,left:i,bottom:s,right:r}){return{x:sO(e.x,i,r),y:sO(e.y,t,s)}}(s.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:sI(e,"left","right"),y:sI(e,"top","bottom")}}(i),r!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&s$(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(s.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!w(t))return!1;let s=t.current;(0,tA.k)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let n=function(e,t,i){let s=sQ(e,i),{scroll:r}=t;return r&&(sK(s.x,r.offset.x),sK(s.y,r.offset.y)),s}(s,r.root,this.visualElement.getTransformPagePoint()),a={x:sR((e=r.layout.layoutBox).x,n.x),y:sR(e.y,n.y)};if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=sN(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:s,dragTransition:r,dragSnapToOrigin:n,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(s$(a=>{if(!s5(a,t,this.currentDirection))return;let l=o&&o[a]||{};n&&(l={min:0,max:0});let d={type:"inertia",velocity:i?e[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,d)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return i.start(si(e,i,0,t,this.visualElement,!1,su(this.visualElement,e)))}stopAnimation(){s$(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){s$(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){s$(t=>{let{drag:i}=this.getProps();if(!s5(t,i,this.currentDirection))return;let{projection:s}=this.visualElement,r=this.getAxisMotionValue(t);if(s&&s.layout){let{min:i,max:n}=s.layout.layoutBox[t];r.set(e[t]-iF(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!w(t)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};s$(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();s[e]=function(e,t){let i=.5,s=sM(e),r=sM(t);return r>s?i=i_(t.min,t.max-s,e.min):s>r&&(i=i_(e.min,e.max-r,t.min)),G(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),s$(t=>{if(!s5(t,e,null))return;let i=this.getAxisMotionValue(t),{min:r,max:n}=this.constraints[t];i.set(iF(r,n,s[t]))})}addListeners(){if(!this.visualElement.current)return;s1.set(this.visualElement,this);let e=e1(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();w(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),eW.read(t);let r=eZ(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(s$(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),e(),s(),n&&n()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:n=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:s,dragConstraints:r,dragElastic:n,dragMomentum:a}}}function s5(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class s3 extends e8{constructor(e){super(e),this.removeGroupControls=eH.Z,this.removeListeners=eH.Z,this.controls=new s2(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eH.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let s9=e=>(t,i)=>{e&&eW.postRender(()=>e(t,i))};class s6 extends e8{constructor(){super(...arguments),this.removePointerDownListener=eH.Z}onPointerDown(e){this.session=new sS(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:s0(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:s9(e),onStart:s9(t),onMove:i,onEnd:(e,t)=>{delete this.session,s&&eW.postRender(()=>s(e,t))}}}mount(){this.removePointerDownListener=e1(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let s4={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function s7(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let s8={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!et.test(e))return e;e=parseFloat(e)}let i=s7(e,t.target.x),s=s7(e,t.target.y);return`${i}% ${s}%`}};class re extends a.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=e;Object.assign(F,ri),r&&(t.group&&t.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),s4.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:s,isPresent:r}=this.props,n=i.projection;return n&&(n.isPresent=r,s||e.layoutDependency!==t||void 0===t?n.willUpdate():this.safeToRemove(),e.isPresent===r||(r?n.promote():n.relegate()||eW.postRender(()=>{let e=n.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),g.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:s}=e;s&&(s.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rt(e){let[t,i]=function(){let e=(0,a.useContext)(d.O);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:i,register:s}=e,r=(0,a.useId)();(0,a.useEffect)(()=>s(r),[]);let n=(0,a.useCallback)(()=>i&&i(r),[r,i]);return!t&&i?[!1,n]:[!0]}(),s=(0,a.useContext)(O.p);return(0,n.jsx)(re,{...e,layoutGroup:s,switchLayoutGroup:(0,a.useContext)(b),isPresent:t,safeToRemove:i})}let ri={borderRadius:{...s8,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:s8,borderTopRightRadius:s8,borderBottomLeftRadius:s8,borderBottomRightRadius:s8,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let s=t7.parse(e);if(s.length>5)return e;let r=t7.createTransformer(e),n="number"!=typeof s[0]?1:0,a=i.x.scale*t.x,o=i.y.scale*t.y;s[0+n]/=a,s[1+n]/=o;let l=iF(a,o,.5);return"number"==typeof s[2+n]&&(s[2+n]/=l),"number"==typeof s[3+n]&&(s[3+n]/=l),r(s)}}},rs=["TopLeft","TopRight","BottomLeft","BottomRight"],rr=rs.length,rn=e=>"string"==typeof e?parseFloat(e):e,ra=e=>"number"==typeof e||et.test(e);function ro(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rl=ru(0,.5,ik),rd=ru(.5,.95,eH.Z);function ru(e,t,i){return s=>s<e?0:s>t?1:i(i_(e,t,s))}function rh(e,t){e.min=t.min,e.max=t.max}function rc(e,t){rh(e.x,t.x),rh(e.y,t.y)}function rp(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rm(e,t,i,s,r){return e-=t,e=s+1/i*(e-s),void 0!==r&&(e=s+1/r*(e-s)),e}function rf(e,t,[i,s,r],n,a){!function(e,t=0,i=1,s=.5,r,n=e,a=e){if(ee.test(t)&&(t=parseFloat(t),t=iF(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=iF(n.min,n.max,s);e===n&&(o-=t),e.min=rm(e.min,t,i,o,r),e.max=rm(e.max,t,i,o,r)}(e,t[i],t[s],t[r],t.scale,n,a)}let rv=["x","scaleX","originX"],rg=["y","scaleY","originY"];function ry(e,t,i,s){rf(e.x,t,rv,i?i.x:void 0,s?s.x:void 0),rf(e.y,t,rg,i?i.y:void 0,s?s.y:void 0)}function rw(e){return 0===e.translate&&1===e.scale}function rb(e){return rw(e.x)&&rw(e.y)}function rx(e,t){return e.min===t.min&&e.max===t.max}function rS(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rT(e,t){return rS(e.x,t.x)&&rS(e.y,t.y)}function rP(e){return sM(e.x)/sM(e.y)}function rE(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rC{constructor(){this.members=[]}add(e){ez(this.members,e),e.scheduleRender()}remove(e){if(e$(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:s}=e.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rM=(e,t)=>e.depth-t.depth;class rA{constructor(){this.children=[],this.isDirty=!1}add(e){ez(this.children,e),this.isDirty=!0}remove(e){e$(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rM),this.isDirty=!1,this.children.forEach(e)}}let rk={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},rL="undefined"!=typeof window&&void 0!==window.MotionDebug,rV=["","X","Y","Z"],rD={visibility:"hidden"},rO=0;function rR(e,t,i,s){let{latestValues:r}=t;r[e]&&(i[e]=r[e],t.setStaticValue(e,0),s&&(s[e]=0))}function rI({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(e={},i=null==t?void 0:t()){this.id=rO++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rL&&(rk.totalNodes=rk.resolvedTargetDeltas=rk.recalculatedProjection=0),this.nodes.forEach(rj),this.nodes.forEach(rW),this.nodes.forEach(rU),this.nodes.forEach(rB),rL&&window.MotionDebug.record(rk)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rA)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new ss),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:s,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(r||s)&&(this.isLayoutDirty=!0),e){let i;let s=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=tC.now(),s=({timestamp:t})=>{let r=t-i;r>=250&&(eU(s),e(r-250))};return eW.read(s,!0),()=>eU(s)}(s,0),s4.hasAnimatedSinceResize&&(s4.hasAnimatedSinceResize=!1,this.nodes.forEach(rH))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&n&&(s||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||rJ,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!rT(this.targetLayout,s)||i,d=!t&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||d||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,d);let t={...tx(r,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||rH(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,eU(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rq),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.HandoffCancelAllAnimations&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return!1;let{visualElement:i}=t.options;return!!i&&(!!sl(i)||!!t.parent&&!t.parent.hasCheckedOptimisedAppear&&e(t.parent))}(this)&&window.HandoffCancelAllAnimations(),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r$);return}this.isUpdating||this.nodes.forEach(rN),this.isUpdating=!1,this.nodes.forEach(rG),this.nodes.forEach(r_),this.nodes.forEach(rF),this.clearAllSnapshots();let e=tC.now();eq.delta=G(0,1e3/60,e-eq.timestamp),eq.timestamp=e,eq.isProcessing=!0,eY.update.process(eq),eY.preRender.process(eq),eY.render.process(eq),eq.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,g.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rz),this.sharedNodes.forEach(rY)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eW.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eW.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=sz(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){let t=s(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!r)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rb(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,n=s!==this.prevTransformTemplateValue;e&&(t||sW(this.latestValues)||n)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),s=this.removeElementScroll(i);return e&&(s=this.removeTransform(s)),r1((t=s).x),r1(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return sz();let i=t.measureViewportBox();if(!((null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)||this.path.some(r5))){let{scroll:e}=this.root;e&&(sK(i.x,e.offset.x),sK(i.y,e.offset.y))}return i}removeElementScroll(e){var t;let i=sz();if(rc(i,e),null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)return i;for(let t=0;t<this.path.length;t++){let s=this.path[t],{scroll:r,options:n}=s;s!==this.root&&r&&n.layoutScroll&&(r.wasRoot&&rc(i,e),sK(i.x,r.offset.x),sK(i.y,r.offset.y))}return i}applyTransform(e,t=!1){let i=sz();rc(i,e);for(let e=0;e<this.path.length;e++){let s=this.path[e];!t&&s.options.layoutScroll&&s.scroll&&s!==s.root&&sJ(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),sW(s.latestValues)&&sJ(i,s.latestValues)}return sW(this.latestValues)&&sJ(i,this.latestValues),i}removeTransform(e){let t=sz();rc(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!sW(i.latestValues))continue;sH(i.latestValues)&&i.updateSnapshot();let s=sz();rc(s,i.measurePageBox()),ry(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return sW(this.latestValues)&&ry(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eq.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,i,s,r;let n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==n;if(!(e||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:o,layoutId:l}=this.options;if(this.layout&&(o||l)){if(this.resolvedRelativeTargetAt=eq.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=sz(),this.relativeTargetOrigin=sz(),sD(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rc(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=sz(),this.targetWithTransforms=sz()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,s=this.relativeTarget,r=this.relativeParent.target,sL(i.x,s.x,r.x),sL(i.y,s.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rc(this.target,this.layout.layoutBox),sX(this.target,this.targetDelta)):rc(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=sz(),this.relativeTargetOrigin=sz(),sD(this.relativeTargetOrigin,this.target,e.target),rc(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rL&&rk.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||sH(this.parent.latestValues)||sU(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),i=!!this.resumingFrom||this!==t,s=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===eq.timestamp&&(s=!1),s)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;rc(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,o=this.treeScale.y;!function(e,t,i,s=!1){let r,n;let a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){n=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&sJ(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,sX(e,n)),s&&sW(r.latestValues)&&sJ(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=sz());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rp(this.prevProjectionDelta.x,this.projectionDelta.x),rp(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),sk(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===a&&this.treeScale.y===o&&rE(this.projectionDelta.x,this.prevProjectionDelta.x)&&rE(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),rL&&rk.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null===(t=this.options.visualElement)||void 0===t||t.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=sj(),this.projectionDelta=sj(),this.projectionDeltaWithTransform=sj()}setAnimationOrigin(e,t=!1){let i;let s=this.snapshot,r=s?s.latestValues:{},n={...this.latestValues},a=sj();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=sz(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),d=this.getStack(),u=!d||d.members.length<=1,h=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(rZ));this.animationProgress=0,this.mixTargetDelta=t=>{let s=t/1e3;if(rX(a.x,e.x,s),rX(a.y,e.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var d,c,p,m;sD(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,rK(p.x,m.x,o.x,s),rK(p.y,m.y,o.y,s),i&&(d=this.relativeTarget,c=i,rx(d.x,c.x)&&rx(d.y,c.y))&&(this.isProjectionDirty=!1),i||(i=sz()),rc(i,this.relativeTarget)}l&&(this.animationValues=n,function(e,t,i,s,r,n){r?(e.opacity=iF(0,void 0!==i.opacity?i.opacity:1,rl(s)),e.opacityExit=iF(void 0!==t.opacity?t.opacity:1,0,rd(s))):n&&(e.opacity=iF(void 0!==t.opacity?t.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let r=0;r<rr;r++){let n=`border${rs[r]}Radius`,a=ro(t,n),o=ro(i,n);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||ra(a)===ra(o)?(e[n]=Math.max(iF(rn(a),rn(o),s),0),(ee.test(o)||ee.test(a))&&(e[n]+="%")):e[n]=o)}(t.rotate||i.rotate)&&(e.rotate=iF(t.rotate||0,i.rotate||0,s))}(n,r,this.latestValues,s,h,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(eU(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eW.update(()=>{s4.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,i){let s=$(0)?0:so(0);return s.start(si("",s,1e3,i)),s.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:s,latestValues:r}=e;if(t&&i&&s){if(this!==e&&this.layout&&s&&r2(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||sz();let t=sM(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let s=sM(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+s}rc(t,i),sJ(t,r),sk(this.projectionDeltaWithTransform,this.layoutCorrected,t,r)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rC),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let s={};i.z&&rR("z",e,s,this.animationValues);for(let t=0;t<rV.length;t++)rR(`rotate${rV[t]}`,e,s,this.animationValues),rR(`skew${rV[t]}`,e,s,this.animationValues);for(let t in e.render(),s)e.setStaticValue(t,s[t]),this.animationValues&&(this.animationValues[t]=s[t]);e.scheduleRender()}getProjectionStyles(e){var t,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return rD;let s={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=eF(null==e?void 0:e.pointerEvents)||"",s.transform=r?r(this.latestValues,""):"none",s;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eF(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!sW(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1),t}let a=n.animationValues||n.latestValues;this.applyTransformsToTarget(),s.transform=function(e,t,i){let s="",r=e.x.translate/t.x,n=e.y.translate/t.y,a=(null==i?void 0:i.z)||0;if((r||n||a)&&(s=`translate3d(${r}px, ${n}px, ${a}px) `),(1!==t.x||1!==t.y)&&(s+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:r,rotateY:n,skewX:a,skewY:o}=i;e&&(s=`perspective(${e}px) ${s}`),t&&(s+=`rotate(${t}deg) `),r&&(s+=`rotateX(${r}deg) `),n&&(s+=`rotateY(${n}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,a),r&&(s.transform=r(a,s.transform));let{x:o,y:l}=this.projectionDelta;for(let e in s.transformOrigin=`${100*o.origin}% ${100*l.origin}% 0`,n.animationValues?s.opacity=n===this?null!==(i=null!==(t=a.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:s.opacity=n===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0,F){if(void 0===a[e])continue;let{correct:t,applyTo:i}=F[e],r="none"===s.transform?a[e]:t(a[e],n);if(i){let e=i.length;for(let t=0;t<e;t++)s[i[t]]=r}else s[e]=r}return this.options.layoutId&&(s.pointerEvents=n===this?eF(null==e?void 0:e.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(r$),this.root.sharedNodes.clear()}}}function r_(e){e.updateLayout()}function rF(e){var t;let i=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&i&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:s}=e.layout,{animationType:r}=e.options,n=i.source!==e.layout.source;"size"===r?s$(e=>{let s=n?i.measuredBox[e]:i.layoutBox[e],r=sM(s);s.min=t[e].min,s.max=s.min+r}):r2(r,i.layoutBox,t)&&s$(s=>{let r=n?i.measuredBox[s]:i.layoutBox[s],a=sM(t[s]);r.max=r.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[s].max=e.relativeTarget[s].min+a)});let a=sj();sk(a,t,i.layoutBox);let o=sj();n?sk(o,e.applyTransform(s,!0),i.measuredBox):sk(o,t,i.layoutBox);let l=!rb(a),d=!1;if(!e.resumeFrom){let s=e.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:n}=s;if(r&&n){let a=sz();sD(a,i.layoutBox,r.layoutBox);let o=sz();sD(o,t,n.layoutBox),rT(a,o)||(d=!0),s.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=s)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:i,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:d})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rj(e){rL&&rk.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rB(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rz(e){e.clearSnapshot()}function r$(e){e.clearMeasurements()}function rN(e){e.isLayoutDirty=!1}function rG(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rH(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rW(e){e.resolveTargetDelta()}function rU(e){e.calcProjection()}function rq(e){e.resetSkewAndRotation()}function rY(e){e.removeLeadSnapshot()}function rX(e,t,i){e.translate=iF(t.translate,0,i),e.scale=iF(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function rK(e,t,i,s){e.min=iF(t.min,i.min,s),e.max=iF(t.max,i.max,s)}function rZ(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let rJ={duration:.45,ease:[.4,0,.1,1]},rQ=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r0=rQ("applewebkit/")&&!rQ("chrome/")?Math.round:eH.Z;function r1(e){e.min=r0(e.min),e.max=r0(e.max)}function r2(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rP(t)-rP(i)))}function r5(e){var t;return e!==e.root&&(null===(t=e.scroll)||void 0===t?void 0:t.wasRoot)}let r3=rI({attachResizeListener:(e,t)=>eZ(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),r9={current:void 0},r6=rI({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!r9.current){let e=new r3({});e.mount(window),e.setOptions({layoutScroll:!0}),r9.current=e}return r9.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position}),r4={current:null},r7={current:!1},r8=new WeakMap,ne=[...tB,t0,t7],nt=e=>ne.find(tj(e)),ni=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],ns=C.length;class nr{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:r,visualState:n},a={}){this.applyWillChange=!1,this.resolveKeyframes=(e,t,i,s)=>new this.KeyframeResolver(e,t,i,s,this),this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tU,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.isRenderScheduled=!1,this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.isRenderScheduled=!1,this.scheduleRender=()=>{this.isRenderScheduled||(this.isRenderScheduled=!0,eW.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=n;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=M(t),this.isVariantNode=A(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:d,...u}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in u){let t=u[e];void 0!==o[e]&&$(t)&&t.set(o[e],!1)}}mount(e){this.current=e,r8.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),r7.current||function(){if(r7.current=!0,D.j){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>r4.current=e.matches;e.addListener(t),t()}else r4.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||r4.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in r8.delete(this.current),this.projection&&this.projection.unmount(),eU(this.notifyUpdate),eU(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let i=B.has(e),s=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eW.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),r=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{s(),r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in V){let t=V[e];if(!t)continue;let{isEnabled:i,Feature:s}=t;if(!this.features[e]&&s&&i(this.props)&&(this.features[e]=new s(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):sz()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ni.length;t++){let i=ni[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=e["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(e,t,i){for(let s in t){let r=t[s],n=i[s];if($(r))e.addValue(s,r);else if($(n))e.addValue(s,so(r,{owner:e}));else if(n!==r){if(e.hasValue(s)){let t=e.getValue(s);!0===t.liveStyle?t.jump(r):t.hasAnimated||t.set(r)}else{let t=e.getStaticValue(s);e.addValue(s,so(void 0!==t?t:r,{owner:e}))}}}for(let s in i)void 0===t[s]&&e.removeValue(s);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}let t={};for(let e=0;e<ns;e++){let i=C[e],s=this.props[i];(T(s)||!1===s)&&(t[i]=s)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=so(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){var i;let s=void 0===this.latestValues[e]&&this.current?null!==(i=this.getBaseTargetFromProps(this.props,e))&&void 0!==i?i:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=s&&("string"==typeof s&&(tk(s)||tM(s))?s=parseFloat(s):!nt(s)&&t7.test(t)&&(s=ia(e,t)),this.setBaseTarget(e,$(s)?s.get():s)),$(s)?s.get():s}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let i;let{initial:s}=this.props;if("string"==typeof s||"object"==typeof s){let r=eD(this.props,s,null===(t=this.presenceContext)||void 0===t?void 0:t.custom);r&&(i=r[e])}if(s&&void 0!==i)return i;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||$(r)?void 0!==this.initialValues[e]&&void 0===i?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new ss),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class nn extends nr{constructor(){super(...arguments),this.KeyframeResolver=il}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}}class na extends nn{constructor(){super(...arguments),this.type="html",this.applyWillChange=!0,this.renderInstance=eC}readValueFromInstance(e,t){if(B.has(t)){let e=ir(t);return e&&e.default||0}{let i=window.getComputedStyle(e),s=(eu(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(e,{transformPagePoint:t}){return sQ(e,t)}build(e,t,i){em(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return ek(e,t,i)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;$(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}class no extends nn{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=sz}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(B.has(t)){let e=ir(t);return e&&e.default||0}return t=eM.has(t)?t:c(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return eL(e,t,i)}build(e,t,i){eT(e,t,this.isSVGTag,i.transformTemplate)}renderInstance(e,t,i,s){eA(e,t,i,s)}mount(e){this.isSVGTag=eE(e.tagName),super.mount(e)}}let nl=(e,t)=>_(e)?new no(t):new na(t,{allowProjection:e!==a.Fragment}),nd={animation:{Feature:sy},exit:{Feature:sb},inView:{Feature:th},tap:{Feature:tn},focus:{Feature:ti},hover:{Feature:tt},pan:{Feature:s6},drag:{Feature:s3,ProjectionNode:r6,MeasureLayout:rt},layout:{ProjectionNode:r6,MeasureLayout:rt}},nu=function(e){function t(t,i={}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:s,Component:r}){e&&function(e){for(let t in e)V[t]={...V[t],...e[t]}}(e);let c=(0,a.forwardRef)(function(e,c){var m;let f;let v={...(0,a.useContext)(o._),...e,layoutId:function({layoutId:e}){let t=(0,a.useContext)(O.p).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:y}=v,P=function(e){let{initial:t,animate:i}=function(e,t){if(M(e)){let{initial:t,animate:i}=e;return{initial:!1===t||T(t)?t:void 0,animate:T(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,a.useContext)(l));return(0,a.useMemo)(()=>({initial:t,animate:i}),[k(t),k(i)])}(e),E=s(e,y);if(!y&&D.j){(0,a.useContext)(h).strict;let e=function(e){let{drag:t,layout:i}=V;if(!t&&!i)return{};let s={...t,...i};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==i?void 0:i.isEnabled(e))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(v);f=e.MeasureLayout,P.visualElement=function(e,t,i,s,r){let{visualElement:n}=(0,a.useContext)(l),c=(0,a.useContext)(h),m=(0,a.useContext)(d.O),f=(0,a.useContext)(o._).reducedMotion,v=(0,a.useRef)();s=s||c.renderer,!v.current&&s&&(v.current=s(e,{visualState:t,parent:n,props:i,presenceContext:m,blockInitialAnimation:!!m&&!1===m.initial,reducedMotionConfig:f}));let y=v.current,T=(0,a.useContext)(b);y&&!y.projection&&r&&("html"===y.type||"svg"===y.type)&&function(e,t,i,s){let{layoutId:r,layout:n,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:d}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:r,layout:n,alwaysMeasureLayout:!!a||o&&w(o),visualElement:e,animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:d})}(v.current,i,r,T),(0,a.useInsertionEffect)(()=>{y&&y.update(i,m)});let P=(0,a.useRef)(!!(i[p]&&!window.HandoffComplete));return(0,u.L)(()=>{y&&(y.updateFeatures(),g.render(y.render),P.current&&y.animationState&&y.animationState.animateChanges())}),(0,a.useEffect)(()=>{y&&(!P.current&&y.animationState&&y.animationState.animateChanges(),P.current&&(P.current=!1,x||(x=!0,queueMicrotask(S))))}),y}(r,E,v,t,e.ProjectionNode)}return(0,n.jsxs)(l.Provider,{value:P,children:[f&&P.visualElement?(0,n.jsx)(f,{visualElement:P.visualElement,...v}):null,i(r,e,(m=P.visualElement,(0,a.useCallback)(e=>{e&&E.mount&&E.mount(e),m&&(e?m.mount(e):m.unmount()),c&&("function"==typeof c?c(e):w(c)&&(c.current=e))},[m])),E,y,P.visualElement)]})});return c[R]=r,c}(e(t,i))}if("undefined"==typeof Proxy)return t;let i=new Map;return new Proxy(t,{get:(e,s)=>(i.has(s)||i.set(s,t(s)),i.get(s))})}((e,t)=>(function(e,{forwardMotionProps:t=!1},i,s){return{..._(e)?eX:eK,preloadedFeatures:i,useRender:function(e=!1){return(t,i,s,{latestValues:r},n)=>{let o=(_(t)?function(e,t,i,s){let r=(0,a.useMemo)(()=>{let i=eP();return eT(i,t,eE(s),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};ev(t,e.style,e),r.style={...t,...r.style}}return r}:function(e,t){let i={},s=function(e,t){let i=e.style||{},s={};return ev(s,i,e),Object.assign(s,function({transformTemplate:e},t){return(0,a.useMemo)(()=>{let i=ef();return em(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),s}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,r,n,t),l=function(e,t,i){let s={};for(let r in e)("values"!==r||"object"!=typeof e.values)&&(ew(r)||!0===i&&ey(r)||!t&&!ey(r)||e.draggable&&r.startsWith("onDrag"))&&(s[r]=e[r]);return s}(i,"string"==typeof t,e),d=t!==a.Fragment?{...l,...o,ref:s}:{},{children:u}=i,h=(0,a.useMemo)(()=>$(u)?u.get():u,[u]);return(0,a.createElement)(t,{...d,children:h})}}(t),createVisualElement:s,Component:e}})(e,t,nd,nl))},9047:function(e,t,i){"use strict";i.d(t,{K:function(){return r},k:function(){return n}});var s=i(9276);let r=s.Z,n=s.Z},7282:function(e,t,i){"use strict";i.d(t,{j:function(){return s}});let s="undefined"!=typeof window},9276:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});let s=e=>e},458:function(e,t,i){"use strict";i.d(t,{h:function(){return r}});var s=i(2265);function r(e){let t=(0,s.useRef)(null);return null===t.current&&(t.current=e()),t.current}},9033:function(e,t,i){"use strict";i.d(t,{L:function(){return r}});var s=i(2265);let r=i(7282).j?s.useLayoutEffect:s.useEffect},7805:function(e,t,i){"use strict";i.d(t,{tl:function(){return n}}),i(3711);var s=i(9007);function r(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function n(e){let t,{swiper:i,extendParams:n,on:a,emit:o}=e,l="swiper-pagination";n({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${l}-bullet`,bulletActiveClass:`${l}-bullet-active`,modifierClass:`${l}-`,currentClass:`${l}-current`,totalClass:`${l}-total`,hiddenClass:`${l}-hidden`,progressbarFillClass:`${l}-progressbar-fill`,progressbarOppositeClass:`${l}-progressbar-opposite`,clickableClass:`${l}-clickable`,lockClass:`${l}-lock`,horizontalClass:`${l}-horizontal`,verticalClass:`${l}-vertical`,paginationDisabledClass:`${l}-disabled`}}),i.pagination={el:null,bullets:[]};let d=0;function u(){return!i.params.pagination.el||!i.pagination.el||Array.isArray(i.pagination.el)&&0===i.pagination.el.length}function h(e,t){let{bulletActiveClass:s}=i.params.pagination;e&&(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&(e.classList.add(`${s}-${t}`),(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&e.classList.add(`${s}-${t}-${t}`))}function c(e){let t=e.target.closest(r(i.params.pagination.bulletClass));if(!t)return;e.preventDefault();let n=(0,s.h)(t)*i.params.slidesPerGroup;if(i.params.loop){if(i.realIndex===n)return;i.slideToLoop(n)}else i.slideTo(n)}function p(){let e,n;let a=i.rtl,l=i.params.pagination;if(u())return;let c=i.pagination.el;c=(0,s.m)(c);let p=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,m=i.params.loop?Math.ceil(p/i.params.slidesPerGroup):i.snapGrid.length;if(i.params.loop?(n=i.previousRealIndex||0,e=i.params.slidesPerGroup>1?Math.floor(i.realIndex/i.params.slidesPerGroup):i.realIndex):void 0!==i.snapIndex?(e=i.snapIndex,n=i.previousSnapIndex):(n=i.previousIndex||0,e=i.activeIndex||0),"bullets"===l.type&&i.pagination.bullets&&i.pagination.bullets.length>0){let r,o,u;let p=i.pagination.bullets;if(l.dynamicBullets&&(t=(0,s.f)(p[0],i.isHorizontal()?"width":"height",!0),c.forEach(e=>{e.style[i.isHorizontal()?"width":"height"]=`${t*(l.dynamicMainBullets+4)}px`}),l.dynamicMainBullets>1&&void 0!==n&&((d+=e-(n||0))>l.dynamicMainBullets-1?d=l.dynamicMainBullets-1:d<0&&(d=0)),u=((o=(r=Math.max(e-d,0))+(Math.min(p.length,l.dynamicMainBullets)-1))+r)/2),p.forEach(e=>{let t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${l.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),c.length>1)p.forEach(t=>{let n=(0,s.h)(t);n===e?t.classList.add(...l.bulletActiveClass.split(" ")):i.isElement&&t.setAttribute("part","bullet"),l.dynamicBullets&&(n>=r&&n<=o&&t.classList.add(...`${l.bulletActiveClass}-main`.split(" ")),n===r&&h(t,"prev"),n===o&&h(t,"next"))});else{let t=p[e];if(t&&t.classList.add(...l.bulletActiveClass.split(" ")),i.isElement&&p.forEach((t,i)=>{t.setAttribute("part",i===e?"bullet-active":"bullet")}),l.dynamicBullets){let e=p[r],t=p[o];for(let e=r;e<=o;e+=1)p[e]&&p[e].classList.add(...`${l.bulletActiveClass}-main`.split(" "));h(e,"prev"),h(t,"next")}}if(l.dynamicBullets){let e=Math.min(p.length,l.dynamicMainBullets+4),s=(t*e-t)/2-u*t,r=a?"right":"left";p.forEach(e=>{e.style[i.isHorizontal()?r:"top"]=`${s}px`})}}c.forEach((t,s)=>{if("fraction"===l.type&&(t.querySelectorAll(r(l.currentClass)).forEach(t=>{t.textContent=l.formatFractionCurrent(e+1)}),t.querySelectorAll(r(l.totalClass)).forEach(e=>{e.textContent=l.formatFractionTotal(m)})),"progressbar"===l.type){let s;s=l.progressbarOpposite?i.isHorizontal()?"vertical":"horizontal":i.isHorizontal()?"horizontal":"vertical";let n=(e+1)/m,a=1,o=1;"horizontal"===s?a=n:o=n,t.querySelectorAll(r(l.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${a}) scaleY(${o})`,e.style.transitionDuration=`${i.params.speed}ms`})}"custom"===l.type&&l.renderCustom?(t.innerHTML=l.renderCustom(i,e+1,m),0===s&&o("paginationRender",t)):(0===s&&o("paginationRender",t),o("paginationUpdate",t)),i.params.watchOverflow&&i.enabled&&t.classList[i.isLocked?"add":"remove"](l.lockClass)})}function m(){let e=i.params.pagination;if(u())return;let t=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.grid&&i.params.grid.rows>1?i.slides.length/Math.ceil(i.params.grid.rows):i.slides.length,n=i.pagination.el;n=(0,s.m)(n);let a="";if("bullets"===e.type){let s=i.params.loop?Math.ceil(t/i.params.slidesPerGroup):i.snapGrid.length;i.params.freeMode&&i.params.freeMode.enabled&&s>t&&(s=t);for(let t=0;t<s;t+=1)e.renderBullet?a+=e.renderBullet.call(i,t,e.bulletClass):a+=`<${e.bulletElement} ${i.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(a=e.renderFraction?e.renderFraction.call(i,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(a=e.renderProgressbar?e.renderProgressbar.call(i,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),i.pagination.bullets=[],n.forEach(t=>{"custom"!==e.type&&(t.innerHTML=a||""),"bullets"===e.type&&i.pagination.bullets.push(...t.querySelectorAll(r(e.bulletClass)))}),"custom"!==e.type&&o("paginationRender",n[0])}function f(){var e,t,r;let n;i.params.pagination=(e=i.originalParams.pagination,t=i.params.pagination,r={el:"swiper-pagination"},i.params.createElements&&Object.keys(r).forEach(n=>{if(!t[n]&&!0===t.auto){let a=(0,s.e)(i.el,`.${r[n]}`)[0];a||((a=(0,s.c)("div",r[n])).className=r[n],i.el.append(a)),t[n]=a,e[n]=a}}),t);let a=i.params.pagination;a.el&&("string"==typeof a.el&&i.isElement&&(n=i.el.querySelector(a.el)),n||"string"!=typeof a.el||(n=[...document.querySelectorAll(a.el)]),n||(n=a.el),n&&0!==n.length&&(i.params.uniqueNavElements&&"string"==typeof a.el&&Array.isArray(n)&&n.length>1&&(n=[...i.el.querySelectorAll(a.el)]).length>1&&(n=n.filter(e=>(0,s.a)(e,".swiper")[0]===i.el)[0]),Array.isArray(n)&&1===n.length&&(n=n[0]),Object.assign(i.pagination,{el:n}),(n=(0,s.m)(n)).forEach(e=>{"bullets"===a.type&&a.clickable&&e.classList.add(...(a.clickableClass||"").split(" ")),e.classList.add(a.modifierClass+a.type),e.classList.add(i.isHorizontal()?a.horizontalClass:a.verticalClass),"bullets"===a.type&&a.dynamicBullets&&(e.classList.add(`${a.modifierClass}${a.type}-dynamic`),d=0,a.dynamicMainBullets<1&&(a.dynamicMainBullets=1)),"progressbar"===a.type&&a.progressbarOpposite&&e.classList.add(a.progressbarOppositeClass),a.clickable&&e.addEventListener("click",c),i.enabled||e.classList.add(a.lockClass)})))}function v(){let e=i.params.pagination;if(u())return;let t=i.pagination.el;t&&(t=(0,s.m)(t)).forEach(t=>{t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(i.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(t.classList.remove(...(e.clickableClass||"").split(" ")),t.removeEventListener("click",c))}),i.pagination.bullets&&i.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}a("changeDirection",()=>{if(!i.pagination||!i.pagination.el)return;let e=i.params.pagination,{el:t}=i.pagination;(t=(0,s.m)(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(i.isHorizontal()?e.horizontalClass:e.verticalClass)})}),a("init",()=>{!1===i.params.pagination.enabled?g():(f(),m(),p())}),a("activeIndexChange",()=>{void 0===i.snapIndex&&p()}),a("snapIndexChange",()=>{p()}),a("snapGridLengthChange",()=>{m(),p()}),a("destroy",()=>{v()}),a("enable disable",()=>{let{el:e}=i.pagination;e&&(e=(0,s.m)(e)).forEach(e=>e.classList[i.enabled?"remove":"add"](i.params.pagination.lockClass))}),a("lock unlock",()=>{p()}),a("click",(e,t)=>{let r=t.target,n=(0,s.m)(i.pagination.el);if(i.params.pagination.el&&i.params.pagination.hideOnClick&&n&&n.length>0&&!r.classList.contains(i.params.pagination.bulletClass)){if(i.navigation&&(i.navigation.nextEl&&r===i.navigation.nextEl||i.navigation.prevEl&&r===i.navigation.prevEl))return;!0===n[0].classList.contains(i.params.pagination.hiddenClass)?o("paginationShow"):o("paginationHide"),n.forEach(e=>e.classList.toggle(i.params.pagination.hiddenClass))}});let g=()=>{i.el.classList.add(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,s.m)(e)).forEach(e=>e.classList.add(i.params.pagination.paginationDisabledClass)),v()};Object.assign(i.pagination,{enable:()=>{i.el.classList.remove(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,s.m)(e)).forEach(e=>e.classList.remove(i.params.pagination.paginationDisabledClass)),f(),m(),p()},disable:g,render:m,update:p,init:f,destroy:v})}},3711:function(e,t,i){"use strict";function s(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function r(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Object.keys(t).forEach(i=>{void 0===e[i]?e[i]=t[i]:s(t[i])&&s(e[i])&&Object.keys(t[i]).length>0&&r(e[i],t[i])})}i.d(t,{a:function(){return l},g:function(){return a}});let n={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function a(){let e="undefined"!=typeof document?document:{};return r(e,n),e}let o={document:n,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function l(){let e="undefined"!=typeof window?window:{};return r(e,o),e}},9007:function(e,t,i){"use strict";i.d(t,{a:function(){return w},c:function(){return m},d:function(){return a},e:function(){return h},f:function(){return b},h:function(){return y},j:function(){return o},m:function(){return x},n:function(){return n},p:function(){return g},q:function(){return v},r:function(){return f},s:function(){return d},t:function(){return u},u:function(){return p},v:function(){return c},w:function(){return function e(){let t=Object(arguments.length<=0?void 0:arguments[0]),i=["__proto__","constructor","prototype"];for(let s=1;s<arguments.length;s+=1){let r=s<0||arguments.length<=s?void 0:arguments[s];if(null!=r&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(r instanceof HTMLElement):!r||1!==r.nodeType&&11!==r.nodeType)){let s=Object.keys(Object(r)).filter(e=>0>i.indexOf(e));for(let i=0,n=s.length;i<n;i+=1){let n=s[i],a=Object.getOwnPropertyDescriptor(r,n);void 0!==a&&a.enumerable&&(l(t[n])&&l(r[n])?r[n].__swiper__?t[n]=r[n]:e(t[n],r[n]):!l(t[n])&&l(r[n])?(t[n]={},r[n].__swiper__?t[n]=r[n]:e(t[n],r[n])):t[n]=r[n])}}}return t}},x:function(){return r}});var s=i(3711);function r(e){Object.keys(e).forEach(t=>{try{e[t]=null}catch(e){}try{delete e[t]}catch(e){}})}function n(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function a(){return Date.now()}function o(e,t){let i,r,n;void 0===t&&(t="x");let a=(0,s.a)(),o=function(e){let t;let i=(0,s.a)();return i.getComputedStyle&&(t=i.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return a.WebKitCSSMatrix?((r=o.transform||o.webkitTransform).split(",").length>6&&(r=r.split(", ").map(e=>e.replace(",",".")).join(", ")),n=new a.WebKitCSSMatrix("none"===r?"":r)):i=(n=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(r=a.WebKitCSSMatrix?n.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(r=a.WebKitCSSMatrix?n.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),r||0}function l(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function d(e,t,i){e.style.setProperty(t,i)}function u(e){let t,{swiper:i,targetPosition:r,side:n}=e,a=(0,s.a)(),o=-i.translate,l=null,d=i.params.speed;i.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(i.cssModeFrameID);let u=r>o?"next":"prev",h=(e,t)=>"next"===u&&e>=t||"prev"===u&&e<=t,c=()=>{t=new Date().getTime(),null===l&&(l=t);let e=o+(.5-Math.cos(Math.max(Math.min((t-l)/d,1),0)*Math.PI)/2)*(r-o);if(h(e,r)&&(e=r),i.wrapperEl.scrollTo({[n]:e}),h(e,r)){i.wrapperEl.style.overflow="hidden",i.wrapperEl.style.scrollSnapType="",setTimeout(()=>{i.wrapperEl.style.overflow="",i.wrapperEl.scrollTo({[n]:e})}),a.cancelAnimationFrame(i.cssModeFrameID);return}i.cssModeFrameID=a.requestAnimationFrame(c)};c()}function h(e,t){void 0===t&&(t="");let i=[...e.children];return(e instanceof HTMLSlotElement&&i.push(...e.assignedElements()),t)?i.filter(e=>e.matches(t)):i}function c(e,t){let i=t.contains(e);return!i&&t instanceof HTMLSlotElement?[...t.assignedElements()].includes(e):i}function p(e){try{console.warn(e);return}catch(e){}}function m(e,t){var i;void 0===t&&(t=[]);let s=document.createElement(e);return s.classList.add(...Array.isArray(t)?t:(void 0===(i=t)&&(i=""),i.trim().split(" ").filter(e=>!!e.trim()))),s}function f(e,t){let i=[];for(;e.previousElementSibling;){let s=e.previousElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}function v(e,t){let i=[];for(;e.nextElementSibling;){let s=e.nextElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}function g(e,t){return(0,s.a)().getComputedStyle(e,null).getPropertyValue(t)}function y(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function w(e,t){let i=[],s=e.parentElement;for(;s;)t?s.matches(t)&&i.push(s):i.push(s),s=s.parentElement;return i}function b(e,t,i){let r=(0,s.a)();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function x(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}},3267:function(e,t,i){"use strict";let s,r,n;i.d(t,{tq:function(){return W},o5:function(){return U}});var a=i(2265),o=i(3711),l=i(9007);function d(){return s||(s=function(){let e=(0,o.a)(),t=(0,o.g)();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),s}function u(e){return void 0===e&&(e={}),r||(r=function(e){let{userAgent:t}=void 0===e?{}:e,i=d(),s=(0,o.a)(),r=s.navigator.platform,n=t||s.navigator.userAgent,a={ios:!1,android:!1},l=s.screen.width,u=s.screen.height,h=n.match(/(Android);?[\s\/]+([\d.]+)?/),c=n.match(/(iPad).*OS\s([\d_]+)/),p=n.match(/(iPod)(.*OS\s([\d_]+))?/),m=!c&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),f="MacIntel"===r;return!c&&f&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${l}x${u}`)>=0&&((c=n.match(/(Version)\/([\d.]+)/))||(c=[0,1,"13_0_0"]),f=!1),h&&"Win32"!==r&&(a.os="android",a.android=!0),(c||m||p)&&(a.os="ios",a.ios=!0),a}(e)),r}let h=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},c=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},p=(e,t)=>{if(!e||e.destroyed||!e.params)return;let i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&t.remove()})),t&&t.remove()}},m=(e,t)=>{if(!e.slides[t])return;let i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},f=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);let s="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let i=[r-t];i.push(...Array.from({length:t}).map((e,t)=>r+s+t)),e.slides.forEach((t,s)=>{i.includes(t.column)&&m(e,s)});return}let n=r+s-1;if(e.params.rewind||e.params.loop)for(let s=r-t;s<=n+t;s+=1){let t=(s%i+i)%i;(t<r||t>n)&&m(e,t)}else for(let s=Math.max(r-t,0);s<=Math.min(n+t,i-1);s+=1)s!==r&&(s>n||s<r)&&m(e,s)};function v(e){let{swiper:t,runCallbacks:i,direction:s,step:r}=e,{activeIndex:n,previousIndex:a}=t,o=s;if(o||(o=n>a?"next":n<a?"prev":"reset"),t.emit(`transition${r}`),i&&n!==a){if("reset"===o){t.emit(`slideResetTransition${r}`);return}t.emit(`slideChangeTransition${r}`),"next"===o?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`)}}function g(e,t,i){let s=(0,o.a)(),{params:r}=e,n=r.edgeSwipeDetection,a=r.edgeSwipeThreshold;return!n||!(i<=a)&&!(i>=s.innerWidth-a)||"prevent"===n&&(t.preventDefault(),!0)}function y(e){let t=(0,o.g)(),i=e;i.originalEvent&&(i=i.originalEvent);let s=this.touchEventsData;if("pointerdown"===i.type){if(null!==s.pointerId&&s.pointerId!==i.pointerId)return;s.pointerId=i.pointerId}else"touchstart"===i.type&&1===i.targetTouches.length&&(s.touchId=i.targetTouches[0].identifier);if("touchstart"===i.type){g(this,i,i.targetTouches[0].pageX);return}let{params:r,touches:n,enabled:a}=this;if(!a||!r.simulateTouch&&"mouse"===i.pointerType||this.animating&&r.preventInteractionOnTransition)return;!this.animating&&r.cssMode&&r.loop&&this.loopFix();let d=i.target;if("wrapper"===r.touchEventsTarget&&!(0,l.v)(d,this.wrapperEl)||"which"in i&&3===i.which||"button"in i&&i.button>0||s.isTouched&&s.isMoved)return;let u=!!r.noSwipingClass&&""!==r.noSwipingClass,h=i.composedPath?i.composedPath():i.path;u&&i.target&&i.target.shadowRoot&&h&&(d=h[0]);let c=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,p=!!(i.target&&i.target.shadowRoot);if(r.noSwiping&&(p?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===(0,o.g)()||i===(0,o.a)())return null;i.assignedSlot&&(i=i.assignedSlot);let s=i.closest(e);return s||i.getRootNode?s||t(i.getRootNode().host):null}(t)}(c,d):d.closest(c))){this.allowClick=!0;return}if(r.swipeHandler&&!d.closest(r.swipeHandler))return;n.currentX=i.pageX,n.currentY=i.pageY;let m=n.currentX,f=n.currentY;if(!g(this,i,m))return;Object.assign(s,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),n.startX=m,n.startY=f,s.touchStartTime=(0,l.d)(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,r.threshold>0&&(s.allowThresholdMove=!1);let v=!0;d.matches(s.focusableElements)&&(v=!1,"SELECT"===d.nodeName&&(s.isTouched=!1)),t.activeElement&&t.activeElement.matches(s.focusableElements)&&t.activeElement!==d&&t.activeElement.blur();let y=v&&this.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||y)&&!d.isContentEditable&&i.preventDefault(),r.freeMode&&r.freeMode.enabled&&this.freeMode&&this.animating&&!r.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",i)}function w(e){let t,i;let s=(0,o.g)(),r=this.touchEventsData,{params:n,touches:a,rtlTranslate:d,enabled:u}=this;if(!u||!n.simulateTouch&&"mouse"===e.pointerType)return;let h=e;if(h.originalEvent&&(h=h.originalEvent),"pointermove"===h.type&&(null!==r.touchId||h.pointerId!==r.pointerId))return;if("touchmove"===h.type){if(!(t=[...h.changedTouches].filter(e=>e.identifier===r.touchId)[0])||t.identifier!==r.touchId)return}else t=h;if(!r.isTouched){r.startMoving&&r.isScrolling&&this.emit("touchMoveOpposite",h);return}let c=t.pageX,p=t.pageY;if(h.preventedByNestedSwiper){a.startX=c,a.startY=p;return}if(!this.allowTouchMove){h.target.matches(r.focusableElements)||(this.allowClick=!1),r.isTouched&&(Object.assign(a,{startX:c,startY:p,currentX:c,currentY:p}),r.touchStartTime=(0,l.d)());return}if(n.touchReleaseOnEdges&&!n.loop){if(this.isVertical()){if(p<a.startY&&this.translate<=this.maxTranslate()||p>a.startY&&this.translate>=this.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else if(c<a.startX&&this.translate<=this.maxTranslate()||c>a.startX&&this.translate>=this.minTranslate())return}if(s.activeElement&&h.target===s.activeElement&&h.target.matches(r.focusableElements)){r.isMoved=!0,this.allowClick=!1;return}r.allowTouchCallbacks&&this.emit("touchMove",h),a.previousX=a.currentX,a.previousY=a.currentY,a.currentX=c,a.currentY=p;let m=a.currentX-a.startX,f=a.currentY-a.startY;if(this.params.threshold&&Math.sqrt(m**2+f**2)<this.params.threshold)return;if(void 0===r.isScrolling){let e;this.isHorizontal()&&a.currentY===a.startY||this.isVertical()&&a.currentX===a.startX?r.isScrolling=!1:m*m+f*f>=25&&(e=180*Math.atan2(Math.abs(f),Math.abs(m))/Math.PI,r.isScrolling=this.isHorizontal()?e>n.touchAngle:90-e>n.touchAngle)}if(r.isScrolling&&this.emit("touchMoveOpposite",h),void 0===r.startMoving&&(a.currentX!==a.startX||a.currentY!==a.startY)&&(r.startMoving=!0),r.isScrolling||"touchmove"===h.type&&r.preventTouchMoveFromPointerMove){r.isTouched=!1;return}if(!r.startMoving)return;this.allowClick=!1,!n.cssMode&&h.cancelable&&h.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&h.stopPropagation();let v=this.isHorizontal()?m:f,g=this.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;n.oneWayMovement&&(v=Math.abs(v)*(d?1:-1),g=Math.abs(g)*(d?1:-1)),a.diff=v,v*=n.touchRatio,d&&(v=-v,g=-g);let y=this.touchesDirection;this.swipeDirection=v>0?"prev":"next",this.touchesDirection=g>0?"prev":"next";let w=this.params.loop&&!n.cssMode,b="next"===this.touchesDirection&&this.allowSlideNext||"prev"===this.touchesDirection&&this.allowSlidePrev;if(!r.isMoved){if(w&&b&&this.loopFix({direction:this.swipeDirection}),r.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});this.wrapperEl.dispatchEvent(e)}r.allowMomentumBounce=!1,n.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",h)}if(new Date().getTime(),r.isMoved&&r.allowThresholdMove&&y!==this.touchesDirection&&w&&b&&Math.abs(v)>=1){Object.assign(a,{startX:c,startY:p,currentX:c,currentY:p,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;return}this.emit("sliderMove",h),r.isMoved=!0,r.currentTranslate=v+r.startTranslate;let x=!0,S=n.resistanceRatio;if(n.touchReleaseOnEdges&&(S=0),v>0?(w&&b&&!i&&r.allowThresholdMove&&r.currentTranslate>(n.centeredSlides?this.minTranslate()-this.slidesSizesGrid[this.activeIndex+1]:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>this.minTranslate()&&(x=!1,n.resistance&&(r.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+r.startTranslate+v)**S))):v<0&&(w&&b&&!i&&r.allowThresholdMove&&r.currentTranslate<(n.centeredSlides?this.maxTranslate()+this.slidesSizesGrid[this.slidesSizesGrid.length-1]:this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===n.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),r.currentTranslate<this.maxTranslate()&&(x=!1,n.resistance&&(r.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-r.startTranslate-v)**S))),x&&(h.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),this.allowSlidePrev||this.allowSlideNext||(r.currentTranslate=r.startTranslate),n.threshold>0){if(Math.abs(v)>n.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,r.currentTranslate=r.startTranslate,a.diff=this.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY;return}}else{r.currentTranslate=r.startTranslate;return}}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&this.freeMode||n.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(r.currentTranslate),this.setTranslate(r.currentTranslate))}function b(e){let t,i;let s=this,r=s.touchEventsData,n=e;if(n.originalEvent&&(n=n.originalEvent),"touchend"===n.type||"touchcancel"===n.type){if(!(t=[...n.changedTouches].filter(e=>e.identifier===r.touchId)[0])||t.identifier!==r.touchId)return}else{if(null!==r.touchId||n.pointerId!==r.pointerId)return;t=n}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(n.type)&&!(["pointercancel","contextmenu"].includes(n.type)&&(s.browser.isSafari||s.browser.isWebView)))return;r.pointerId=null,r.touchId=null;let{params:a,touches:o,rtlTranslate:d,slidesGrid:u,enabled:h}=s;if(!h||!a.simulateTouch&&"mouse"===n.pointerType)return;if(r.allowTouchCallbacks&&s.emit("touchEnd",n),r.allowTouchCallbacks=!1,!r.isTouched){r.isMoved&&a.grabCursor&&s.setGrabCursor(!1),r.isMoved=!1,r.startMoving=!1;return}a.grabCursor&&r.isMoved&&r.isTouched&&(!0===s.allowSlideNext||!0===s.allowSlidePrev)&&s.setGrabCursor(!1);let c=(0,l.d)(),p=c-r.touchStartTime;if(s.allowClick){let e=n.path||n.composedPath&&n.composedPath();s.updateClickedSlide(e&&e[0]||n.target,e),s.emit("tap click",n),p<300&&c-r.lastClickTime<300&&s.emit("doubleTap doubleClick",n)}if(r.lastClickTime=(0,l.d)(),(0,l.n)(()=>{s.destroyed||(s.allowClick=!0)}),!r.isTouched||!r.isMoved||!s.swipeDirection||0===o.diff&&!r.loopSwapReset||r.currentTranslate===r.startTranslate&&!r.loopSwapReset){r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;return}if(r.isTouched=!1,r.isMoved=!1,r.startMoving=!1,i=a.followFinger?d?s.translate:-s.translate:-r.currentTranslate,a.cssMode)return;if(a.freeMode&&a.freeMode.enabled){s.freeMode.onTouchEnd({currentPos:i});return}let m=i>=-s.maxTranslate()&&!s.params.loop,f=0,v=s.slidesSizesGrid[0];for(let e=0;e<u.length;e+=e<a.slidesPerGroupSkip?1:a.slidesPerGroup){let t=e<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;void 0!==u[e+t]?(m||i>=u[e]&&i<u[e+t])&&(f=e,v=u[e+t]-u[e]):(m||i>=u[e])&&(f=e,v=u[u.length-1]-u[u.length-2])}let g=null,y=null;a.rewind&&(s.isBeginning?y=a.virtual&&a.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1:s.isEnd&&(g=0));let w=(i-u[f])/v,b=f<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(p>a.longSwipesMs){if(!a.longSwipes){s.slideTo(s.activeIndex);return}"next"===s.swipeDirection&&(w>=a.longSwipesRatio?s.slideTo(a.rewind&&s.isEnd?g:f+b):s.slideTo(f)),"prev"===s.swipeDirection&&(w>1-a.longSwipesRatio?s.slideTo(f+b):null!==y&&w<0&&Math.abs(w)>a.longSwipesRatio?s.slideTo(y):s.slideTo(f))}else{if(!a.shortSwipes){s.slideTo(s.activeIndex);return}s.navigation&&(n.target===s.navigation.nextEl||n.target===s.navigation.prevEl)?n.target===s.navigation.nextEl?s.slideTo(f+b):s.slideTo(f):("next"===s.swipeDirection&&s.slideTo(null!==g?g:f+b),"prev"===s.swipeDirection&&s.slideTo(null!==y?y:f))}}function x(){let e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:s,allowSlidePrev:r,snapGrid:n}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let o=a&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||o?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=r,e.allowSlideNext=s,e.params.watchOverflow&&n!==e.snapGrid&&e.checkOverflow()}function S(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function T(){let{wrapperEl:e,rtlTranslate:t,enabled:i}=this;if(!i)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-e.scrollLeft:this.translate=-e.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let s=this.maxTranslate()-this.minTranslate();(0===s?0:(this.translate-this.minTranslate())/s)!==this.progress&&this.updateProgress(t?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function P(e){p(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}function E(){!this.documentTouchHandlerProceeded&&(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}let C=(e,t)=>{let i=(0,o.g)(),{params:s,el:r,wrapperEl:n,device:a}=e,l=!!s.nested,d="on"===t?"addEventListener":"removeEventListener";r&&"string"!=typeof r&&(i[d]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:l}),r[d]("touchstart",e.onTouchStart,{passive:!1}),r[d]("pointerdown",e.onTouchStart,{passive:!1}),i[d]("touchmove",e.onTouchMove,{passive:!1,capture:l}),i[d]("pointermove",e.onTouchMove,{passive:!1,capture:l}),i[d]("touchend",e.onTouchEnd,{passive:!0}),i[d]("pointerup",e.onTouchEnd,{passive:!0}),i[d]("pointercancel",e.onTouchEnd,{passive:!0}),i[d]("touchcancel",e.onTouchEnd,{passive:!0}),i[d]("pointerout",e.onTouchEnd,{passive:!0}),i[d]("pointerleave",e.onTouchEnd,{passive:!0}),i[d]("contextmenu",e.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[d]("click",e.onClick,!0),s.cssMode&&n[d]("scroll",e.onScroll),s.updateOnWindowResize?e[t](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",x,!0):e[t]("observerUpdate",x,!0),r[d]("load",e.onLoad,{capture:!0}))},M=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var A={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let k={eventsEmitter:{on(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;let r=i?"unshift":"push";return e.split(" ").forEach(e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][r](t)}),s},once(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;function r(){s.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var i=arguments.length,n=Array(i),a=0;a<i;a++)n[a]=arguments[a];t.apply(s,n)}return r.__emitterProxy=t,s.on(e,r,i)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let i=this;return i.eventsListeners&&!i.destroyed&&i.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((s,r)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(r,1)})}),i},emit(){let e,t,i;let s=this;if(!s.eventsListeners||s.destroyed||!s.eventsListeners)return s;for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return"string"==typeof n[0]||Array.isArray(n[0])?(e=n[0],t=n.slice(1,n.length),i=s):(e=n[0].events,t=n[0].data,i=n[0].context||s),t.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(e=>{s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(s=>{s.apply(i,[e,...t])}),s.eventsListeners&&s.eventsListeners[e]&&s.eventsListeners[e].forEach(e=>{e.apply(i,t)})}),s}},update:{updateSize:function(){let e,t;let i=this.el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:i.clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:i.clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt((0,l.p)(i,"padding-left")||0,10)-parseInt((0,l.p)(i,"padding-right")||0,10),t=t-parseInt((0,l.p)(i,"padding-top")||0,10)-parseInt((0,l.p)(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e;let t=this;function i(e,i){return parseFloat(e.getPropertyValue(t.getDirectionLabel(i))||0)}let s=t.params,{wrapperEl:r,slidesEl:n,size:a,rtlTranslate:o,wrongRTL:d}=t,u=t.virtual&&s.virtual.enabled,h=u?t.virtual.slides.length:t.slides.length,c=(0,l.e)(n,`.${t.params.slideClass}, swiper-slide`),p=u?t.virtual.slides.length:c.length,m=[],f=[],v=[],g=s.slidesOffsetBefore;"function"==typeof g&&(g=s.slidesOffsetBefore.call(t));let y=s.slidesOffsetAfter;"function"==typeof y&&(y=s.slidesOffsetAfter.call(t));let w=t.snapGrid.length,b=t.slidesGrid.length,x=s.spaceBetween,S=-g,T=0,P=0;if(void 0===a)return;"string"==typeof x&&x.indexOf("%")>=0?x=parseFloat(x.replace("%",""))/100*a:"string"==typeof x&&(x=parseFloat(x)),t.virtualSize=-x,c.forEach(e=>{o?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),s.centeredSlides&&s.cssMode&&((0,l.s)(r,"--swiper-centered-offset-before",""),(0,l.s)(r,"--swiper-centered-offset-after",""));let E=s.grid&&s.grid.rows>1&&t.grid;E?t.grid.initSlides(c):t.grid&&t.grid.unsetSlides();let C="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter(e=>void 0!==s.breakpoints[e].slidesPerView).length>0;for(let r=0;r<p;r+=1){let n;if(e=0,c[r]&&(n=c[r]),E&&t.grid.updateSlide(r,n,c),!c[r]||"none"!==(0,l.p)(n,"display")){if("auto"===s.slidesPerView){C&&(c[r].style[t.getDirectionLabel("width")]="");let a=getComputedStyle(n),o=n.style.transform,d=n.style.webkitTransform;if(o&&(n.style.transform="none"),d&&(n.style.webkitTransform="none"),s.roundLengths)e=t.isHorizontal()?(0,l.f)(n,"width",!0):(0,l.f)(n,"height",!0);else{let t=i(a,"width"),s=i(a,"padding-left"),r=i(a,"padding-right"),o=i(a,"margin-left"),l=i(a,"margin-right"),d=a.getPropertyValue("box-sizing");if(d&&"border-box"===d)e=t+o+l;else{let{clientWidth:i,offsetWidth:a}=n;e=t+s+r+o+l+(a-i)}}o&&(n.style.transform=o),d&&(n.style.webkitTransform=d),s.roundLengths&&(e=Math.floor(e))}else e=(a-(s.slidesPerView-1)*x)/s.slidesPerView,s.roundLengths&&(e=Math.floor(e)),c[r]&&(c[r].style[t.getDirectionLabel("width")]=`${e}px`);c[r]&&(c[r].swiperSlideSize=e),v.push(e),s.centeredSlides?(S=S+e/2+T/2+x,0===T&&0!==r&&(S=S-a/2-x),0===r&&(S=S-a/2-x),.001>Math.abs(S)&&(S=0),s.roundLengths&&(S=Math.floor(S)),P%s.slidesPerGroup==0&&m.push(S),f.push(S)):(s.roundLengths&&(S=Math.floor(S)),(P-Math.min(t.params.slidesPerGroupSkip,P))%t.params.slidesPerGroup==0&&m.push(S),f.push(S),S=S+e+x),t.virtualSize+=e+x,T=e,P+=1}}if(t.virtualSize=Math.max(t.virtualSize,a)+y,o&&d&&("slide"===s.effect||"coverflow"===s.effect)&&(r.style.width=`${t.virtualSize+x}px`),s.setWrapperSize&&(r.style[t.getDirectionLabel("width")]=`${t.virtualSize+x}px`),E&&t.grid.updateWrapperSize(e,m),!s.centeredSlides){let e=[];for(let i=0;i<m.length;i+=1){let r=m[i];s.roundLengths&&(r=Math.floor(r)),m[i]<=t.virtualSize-a&&e.push(r)}m=e,Math.floor(t.virtualSize-a)-Math.floor(m[m.length-1])>1&&m.push(t.virtualSize-a)}if(u&&s.loop){let e=v[0]+x;if(s.slidesPerGroup>1){let i=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/s.slidesPerGroup),r=e*s.slidesPerGroup;for(let e=0;e<i;e+=1)m.push(m[m.length-1]+r)}for(let i=0;i<t.virtual.slidesBefore+t.virtual.slidesAfter;i+=1)1===s.slidesPerGroup&&m.push(m[m.length-1]+e),f.push(f[f.length-1]+e),t.virtualSize+=e}if(0===m.length&&(m=[0]),0!==x){let e=t.isHorizontal()&&o?"marginLeft":t.getDirectionLabel("marginRight");c.filter((e,t)=>!s.cssMode||!!s.loop||t!==c.length-1).forEach(t=>{t.style[e]=`${x}px`})}if(s.centeredSlides&&s.centeredSlidesBounds){let e=0;v.forEach(t=>{e+=t+(x||0)});let t=(e-=x)-a;m=m.map(e=>e<=0?-g:e>t?t+y:e)}if(s.centerInsufficientSlides){let e=0;v.forEach(t=>{e+=t+(x||0)}),e-=x;let t=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(e+t<a){let i=(a-e-t)/2;m.forEach((e,t)=>{m[t]=e-i}),f.forEach((e,t)=>{f[t]=e+i})}}if(Object.assign(t,{slides:c,snapGrid:m,slidesGrid:f,slidesSizesGrid:v}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){(0,l.s)(r,"--swiper-centered-offset-before",`${-m[0]}px`),(0,l.s)(r,"--swiper-centered-offset-after",`${t.size/2-v[v.length-1]/2}px`);let e=-t.snapGrid[0],i=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(e=>e+i)}if(p!==h&&t.emit("slidesLengthChange"),m.length!==w&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),f.length!==b&&t.emit("slidesGridLengthChange"),s.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!u&&!s.cssMode&&("slide"===s.effect||"fade"===s.effect)){let e=`${s.containerModifierClass}backface-hidden`,i=t.el.classList.contains(e);p<=s.maxBackfaceHiddenSlides?i||t.el.classList.add(e):i&&t.el.classList.remove(e)}},updateAutoHeight:function(e){let t;let i=this,s=[],r=i.virtual&&i.params.virtual.enabled,n=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);let a=e=>r?i.slides[i.getSlideIndexByData(e)]:i.slides[e];if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1){if(i.params.centeredSlides)(i.visibleSlides||[]).forEach(e=>{s.push(e)});else for(t=0;t<Math.ceil(i.params.slidesPerView);t+=1){let e=i.activeIndex+t;if(e>i.slides.length&&!r)break;s.push(a(e))}}else s.push(a(i.activeIndex));for(t=0;t<s.length;t+=1)if(void 0!==s[t]){let e=s[t].offsetHeight;n=e>n?e:n}(n||0===n)&&(i.wrapperEl.style.height=`${n}px`)},updateSlidesOffset:function(){let e=this.slides,t=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(this.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-this.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);let t=this.params,{slides:i,rtlTranslate:s,snapGrid:r}=this;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&this.updateSlidesOffset();let n=-e;s&&(n=e),this.visibleSlidesIndexes=[],this.visibleSlides=[];let a=t.spaceBetween;"string"==typeof a&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*this.size:"string"==typeof a&&(a=parseFloat(a));for(let e=0;e<i.length;e+=1){let o=i[e],l=o.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(l-=i[0].swiperSlideOffset);let d=(n+(t.centeredSlides?this.minTranslate():0)-l)/(o.swiperSlideSize+a),u=(n-r[0]+(t.centeredSlides?this.minTranslate():0)-l)/(o.swiperSlideSize+a),c=-(n-l),p=c+this.slidesSizesGrid[e],m=c>=0&&c<=this.size-this.slidesSizesGrid[e],f=c>=0&&c<this.size-1||p>1&&p<=this.size||c<=0&&p>=this.size;f&&(this.visibleSlides.push(o),this.visibleSlidesIndexes.push(e)),h(o,f,t.slideVisibleClass),h(o,m,t.slideFullyVisibleClass),o.progress=s?-d:d,o.originalProgress=s?-u:u}},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let t=this.params,i=this.maxTranslate()-this.minTranslate(),{progress:s,isBeginning:r,isEnd:n,progressLoop:a}=this,o=r,l=n;if(0===i)s=0,r=!0,n=!0;else{s=(e-this.minTranslate())/i;let t=1>Math.abs(e-this.minTranslate()),a=1>Math.abs(e-this.maxTranslate());r=t||s<=0,n=a||s>=1,t&&(s=0),a&&(s=1)}if(t.loop){let t=this.getSlideIndexByData(0),i=this.getSlideIndexByData(this.slides.length-1),s=this.slidesGrid[t],r=this.slidesGrid[i],n=this.slidesGrid[this.slidesGrid.length-1],o=Math.abs(e);(a=o>=s?(o-s)/n:(o+n-r)/n)>1&&(a-=1)}Object.assign(this,{progress:s,progressLoop:a,isBeginning:r,isEnd:n}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&this.updateSlidesProgress(e),r&&!o&&this.emit("reachBeginning toEdge"),n&&!l&&this.emit("reachEnd toEdge"),(o&&!r||l&&!n)&&this.emit("fromEdge"),this.emit("progress",s)},updateSlidesClasses:function(){let e,t,i;let{slides:s,params:r,slidesEl:n,activeIndex:a}=this,o=this.virtual&&r.virtual.enabled,d=this.grid&&r.grid&&r.grid.rows>1,u=e=>(0,l.e)(n,`.${r.slideClass}${e}, swiper-slide${e}`)[0];if(o){if(r.loop){let t=a-this.virtual.slidesBefore;t<0&&(t=this.virtual.slides.length+t),t>=this.virtual.slides.length&&(t-=this.virtual.slides.length),e=u(`[data-swiper-slide-index="${t}"]`)}else e=u(`[data-swiper-slide-index="${a}"]`)}else d?(e=s.filter(e=>e.column===a)[0],i=s.filter(e=>e.column===a+1)[0],t=s.filter(e=>e.column===a-1)[0]):e=s[a];e&&!d&&(i=(0,l.q)(e,`.${r.slideClass}, swiper-slide`)[0],r.loop&&!i&&(i=s[0]),t=(0,l.r)(e,`.${r.slideClass}, swiper-slide`)[0],r.loop),s.forEach(s=>{c(s,s===e,r.slideActiveClass),c(s,s===i,r.slideNextClass),c(s,s===t,r.slidePrevClass)}),this.emitSlidesClasses()},updateActiveIndex:function(e){let t,i;let s=this,r=s.rtlTranslate?s.translate:-s.translate,{snapGrid:n,params:a,activeIndex:o,realIndex:l,snapIndex:d}=s,u=e,h=e=>{let t=e-s.virtual.slidesBefore;return t<0&&(t=s.virtual.slides.length+t),t>=s.virtual.slides.length&&(t-=s.virtual.slides.length),t};if(void 0===u&&(u=function(e){let t;let{slidesGrid:i,params:s}=e,r=e.rtlTranslate?e.translate:-e.translate;for(let e=0;e<i.length;e+=1)void 0!==i[e+1]?r>=i[e]&&r<i[e+1]-(i[e+1]-i[e])/2?t=e:r>=i[e]&&r<i[e+1]&&(t=e+1):r>=i[e]&&(t=e);return s.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(s)),n.indexOf(r)>=0)t=n.indexOf(r);else{let e=Math.min(a.slidesPerGroupSkip,u);t=e+Math.floor((u-e)/a.slidesPerGroup)}if(t>=n.length&&(t=n.length-1),u===o&&!s.params.loop){t!==d&&(s.snapIndex=t,s.emit("snapIndexChange"));return}if(u===o&&s.params.loop&&s.virtual&&s.params.virtual.enabled){s.realIndex=h(u);return}let c=s.grid&&a.grid&&a.grid.rows>1;if(s.virtual&&a.virtual.enabled&&a.loop)i=h(u);else if(c){let e=s.slides.filter(e=>e.column===u)[0],t=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(t)&&(t=Math.max(s.slides.indexOf(e),0)),i=Math.floor(t/a.grid.rows)}else if(s.slides[u]){let e=s.slides[u].getAttribute("data-swiper-slide-index");i=e?parseInt(e,10):u}else i=u;Object.assign(s,{previousSnapIndex:d,snapIndex:t,previousRealIndex:l,realIndex:i,previousIndex:o,activeIndex:u}),s.initialized&&f(s),s.emit("activeIndexChange"),s.emit("snapIndexChange"),(s.initialized||s.params.runCallbacksOnInit)&&(l!==i&&s.emit("realIndexChange"),s.emit("slideChange"))},updateClickedSlide:function(e,t){let i;let s=this.params,r=e.closest(`.${s.slideClass}, swiper-slide`);!r&&this.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!r&&e.matches&&e.matches(`.${s.slideClass}, swiper-slide`)&&(r=e)});let n=!1;if(r){for(let e=0;e<this.slides.length;e+=1)if(this.slides[e]===r){n=!0,i=e;break}}if(r&&n)this.clickedSlide=r,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=i;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}s.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");let{params:t,rtlTranslate:i,translate:s,wrapperEl:r}=this;if(t.virtualTranslate)return i?-s:s;if(t.cssMode)return s;let n=(0,l.j)(r,e);return n+=this.cssOverflowAdjustment(),i&&(n=-n),n||0},setTranslate:function(e,t){let{rtlTranslate:i,params:s,wrapperEl:r,progress:n}=this,a=0,o=0;this.isHorizontal()?a=i?-e:e:o=e,s.roundLengths&&(a=Math.floor(a),o=Math.floor(o)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?a:o,s.cssMode?r[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-a:-o:s.virtualTranslate||(this.isHorizontal()?a-=this.cssOverflowAdjustment():o-=this.cssOverflowAdjustment(),r.style.transform=`translate3d(${a}px, ${o}px, 0px)`);let l=this.maxTranslate()-this.minTranslate();(0===l?0:(e-this.minTranslate())/l)!==n&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,s,r){let n;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===s&&(s=!0);let a=this,{params:o,wrapperEl:d}=a;if(a.animating&&o.preventInteractionOnTransition)return!1;let u=a.minTranslate(),h=a.maxTranslate();if(n=s&&e>u?u:s&&e<h?h:e,a.updateProgress(n),o.cssMode){let e=a.isHorizontal();if(0===t)d[e?"scrollLeft":"scrollTop"]=-n;else{if(!a.support.smoothScroll)return(0,l.t)({swiper:a,targetPosition:-n,side:e?"left":"top"}),!0;d.scrollTo({[e?"left":"top"]:-n,behavior:"smooth"})}return!0}return 0===t?(a.setTransition(0),a.setTranslate(n),i&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(n),i&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,a.animating=!1,i&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${e}ms`,this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);let{params:i}=this;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),v({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);let{params:i}=this;this.animating=!1,i.cssMode||(this.setTransition(0),v({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,s,r){let n;void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let a=this,o=e;o<0&&(o=0);let{params:d,snapGrid:u,slidesGrid:h,previousIndex:c,activeIndex:p,rtlTranslate:m,wrapperEl:f,enabled:v}=a;if(!v&&!s&&!r||a.destroyed||a.animating&&d.preventInteractionOnTransition)return!1;void 0===t&&(t=a.params.speed);let g=Math.min(a.params.slidesPerGroupSkip,o),y=g+Math.floor((o-g)/a.params.slidesPerGroup);y>=u.length&&(y=u.length-1);let w=-u[y];if(d.normalizeSlideIndex)for(let e=0;e<h.length;e+=1){let t=-Math.floor(100*w),i=Math.floor(100*h[e]),s=Math.floor(100*h[e+1]);void 0!==h[e+1]?t>=i&&t<s-(s-i)/2?o=e:t>=i&&t<s&&(o=e+1):t>=i&&(o=e)}if(a.initialized&&o!==p&&(!a.allowSlideNext&&(m?w>a.translate&&w>a.minTranslate():w<a.translate&&w<a.minTranslate())||!a.allowSlidePrev&&w>a.translate&&w>a.maxTranslate()&&(p||0)!==o))return!1;if(o!==(c||0)&&i&&a.emit("beforeSlideChangeStart"),a.updateProgress(w),n=o>p?"next":o<p?"prev":"reset",m&&-w===a.translate||!m&&w===a.translate)return a.updateActiveIndex(o),d.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==d.effect&&a.setTranslate(w),"reset"!==n&&(a.transitionStart(i,n),a.transitionEnd(i,n)),!1;if(d.cssMode){let e=a.isHorizontal(),i=m?w:-w;if(0===t){let t=a.virtual&&a.params.virtual.enabled;t&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),t&&!a._cssModeVirtualInitialSet&&a.params.initialSlide>0?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{f[e?"scrollLeft":"scrollTop"]=i})):f[e?"scrollLeft":"scrollTop"]=i,t&&requestAnimationFrame(()=>{a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1})}else{if(!a.support.smoothScroll)return(0,l.t)({swiper:a,targetPosition:i,side:e?"left":"top"}),!0;f.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}return a.setTransition(t),a.setTranslate(w),a.updateActiveIndex(o),a.updateSlidesClasses(),a.emit("beforeTransitionStart",t,s),a.transitionStart(i,n),0===t?a.transitionEnd(i,n):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(i,n))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,s){void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let r=this;if(r.destroyed)return;void 0===t&&(t=r.params.speed);let n=r.grid&&r.params.grid&&r.params.grid.rows>1,a=e;if(r.params.loop){if(r.virtual&&r.params.virtual.enabled)a+=r.virtual.slidesBefore;else{let e;if(n){let t=a*r.params.grid.rows;e=r.slides.filter(e=>1*e.getAttribute("data-swiper-slide-index")===t)[0].column}else e=r.getSlideIndexByData(a);let t=n?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:i}=r.params,o=r.params.slidesPerView;"auto"===o?o=r.slidesPerViewDynamic():(o=Math.ceil(parseFloat(r.params.slidesPerView,10)),i&&o%2==0&&(o+=1));let l=t-e<o;if(i&&(l=l||e<Math.ceil(o/2)),s&&i&&"auto"!==r.params.slidesPerView&&!n&&(l=!1),l){let s=i?e<r.activeIndex?"prev":"next":e-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:s,slideTo:!0,activeSlideIndex:"next"===s?e+1:e-t+1,slideRealIndex:"next"===s?r.realIndex:void 0})}if(n){let e=a*r.params.grid.rows;a=r.slides.filter(t=>1*t.getAttribute("data-swiper-slide-index")===e)[0].column}else a=r.getSlideIndexByData(a)}}return requestAnimationFrame(()=>{r.slideTo(a,t,i,s)}),r},slideNext:function(e,t,i){void 0===t&&(t=!0);let s=this,{enabled:r,params:n,animating:a}=s;if(!r||s.destroyed)return s;void 0===e&&(e=s.params.speed);let o=n.slidesPerGroup;"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(o=Math.max(s.slidesPerViewDynamic("current",!0),1));let l=s.activeIndex<n.slidesPerGroupSkip?1:o,d=s.virtual&&n.virtual.enabled;if(n.loop){if(a&&!d&&n.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&n.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+l,e,t,i)}),!0}return n.rewind&&s.isEnd?s.slideTo(0,e,t,i):s.slideTo(s.activeIndex+l,e,t,i)},slidePrev:function(e,t,i){void 0===t&&(t=!0);let s=this,{params:r,snapGrid:n,slidesGrid:a,rtlTranslate:o,enabled:l,animating:d}=s;if(!l||s.destroyed)return s;void 0===e&&(e=s.params.speed);let u=s.virtual&&r.virtual.enabled;if(r.loop){if(d&&!u&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}function h(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let c=h(o?s.translate:-s.translate),p=n.map(e=>h(e)),m=n[p.indexOf(c)-1];if(void 0===m&&r.cssMode){let e;n.forEach((t,i)=>{c>=t&&(e=i)}),void 0!==e&&(m=n[e>0?e-1:e])}let f=0;if(void 0!==m&&((f=a.indexOf(m))<0&&(f=s.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(f=Math.max(f=f-s.slidesPerViewDynamic("previous",!0)+1,0))),r.rewind&&s.isBeginning){let r=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(r,e,t,i)}return r.loop&&0===s.activeIndex&&r.cssMode?(requestAnimationFrame(()=>{s.slideTo(f,e,t,i)}),!0):s.slideTo(f,e,t,i)},slideReset:function(e,t,i){if(void 0===t&&(t=!0),!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,s){if(void 0===t&&(t=!0),void 0===s&&(s=.5),this.destroyed)return;void 0===e&&(e=this.params.speed);let r=this.activeIndex,n=Math.min(this.params.slidesPerGroupSkip,r),a=n+Math.floor((r-n)/this.params.slidesPerGroup),o=this.rtlTranslate?this.translate:-this.translate;if(o>=this.snapGrid[a]){let e=this.snapGrid[a];o-e>(this.snapGrid[a+1]-e)*s&&(r+=this.params.slidesPerGroup)}else{let e=this.snapGrid[a-1];o-e<=(this.snapGrid[a]-e)*s&&(r-=this.params.slidesPerGroup)}return r=Math.min(r=Math.max(r,0),this.slidesGrid.length-1),this.slideTo(r,e,t,i)},slideToClickedSlide:function(){let e;let t=this;if(t.destroyed)return;let{params:i,slidesEl:s}=t,r="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,n=t.clickedIndex,a=t.isElement?"swiper-slide":`.${i.slideClass}`;if(i.loop){if(t.animating)return;e=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),i.centeredSlides?n<t.loopedSlides-r/2||n>t.slides.length-t.loopedSlides+r/2?(t.loopFix(),n=t.getSlideIndex((0,l.e)(s,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,l.n)(()=>{t.slideTo(n)})):t.slideTo(n):n>t.slides.length-r?(t.loopFix(),n=t.getSlideIndex((0,l.e)(s,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,l.n)(()=>{t.slideTo(n)})):t.slideTo(n)}else t.slideTo(n)}},loop:{loopCreate:function(e){let t=this,{params:i,slidesEl:s}=t;if(!i.loop||t.virtual&&t.params.virtual.enabled)return;let r=t.grid&&i.grid&&i.grid.rows>1,n=i.slidesPerGroup*(r?i.grid.rows:1),a=t.slides.length%n!=0,o=r&&t.slides.length%i.grid.rows!=0,d=e=>{for(let s=0;s<e;s+=1){let e=t.isElement?(0,l.c)("swiper-slide",[i.slideBlankClass]):(0,l.c)("div",[i.slideClass,i.slideBlankClass]);t.slidesEl.append(e)}};a?i.loopAddBlankSlides?(d(n-t.slides.length%n),t.recalcSlides(),t.updateSlides()):(0,l.u)("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"):o&&(i.loopAddBlankSlides?(d(i.grid.rows-t.slides.length%i.grid.rows),t.recalcSlides(),t.updateSlides()):(0,l.u)("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)")),(0,l.e)(s,`.${i.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}),t.loopFix({slideRealIndex:e,direction:i.centeredSlides?void 0:"next"})},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:s,setTranslate:r,activeSlideIndex:n,byController:a,byMousewheel:o}=void 0===e?{}:e,d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");let{slides:u,allowSlidePrev:h,allowSlideNext:c,slidesEl:p,params:m}=d,{centeredSlides:f}=m;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&m.virtual.enabled){i&&(m.centeredSlides||0!==d.snapIndex?m.centeredSlides&&d.snapIndex<m.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0):d.slideTo(d.virtual.slides.length,0,!1,!0)),d.allowSlidePrev=h,d.allowSlideNext=c,d.emit("loopFix");return}let v=m.slidesPerView;"auto"===v?v=d.slidesPerViewDynamic():(v=Math.ceil(parseFloat(m.slidesPerView,10)),f&&v%2==0&&(v+=1));let g=m.slidesPerGroupAuto?v:m.slidesPerGroup,y=g;y%g!=0&&(y+=g-y%g),y+=m.loopAdditionalSlides,d.loopedSlides=y;let w=d.grid&&m.grid&&m.grid.rows>1;u.length<v+y?(0,l.u)("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):w&&"row"===m.grid.fill&&(0,l.u)("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let b=[],x=[],S=d.activeIndex;void 0===n?n=d.getSlideIndex(u.filter(e=>e.classList.contains(m.slideActiveClass))[0]):S=n;let T="next"===s||!s,P="prev"===s||!s,E=0,C=0,M=w?Math.ceil(u.length/m.grid.rows):u.length,A=(w?u[n].column:n)+(f&&void 0===r?-v/2+.5:0);if(A<y){E=Math.max(y-A,g);for(let e=0;e<y-A;e+=1){let t=e-Math.floor(e/M)*M;if(w){let e=M-t-1;for(let t=u.length-1;t>=0;t-=1)u[t].column===e&&b.push(t)}else b.push(M-t-1)}}else if(A+v>M-y){C=Math.max(A-(M-2*y),g);for(let e=0;e<C;e+=1){let t=e-Math.floor(e/M)*M;w?u.forEach((e,i)=>{e.column===t&&x.push(i)}):x.push(t)}}if(d.__preventObserver__=!0,requestAnimationFrame(()=>{d.__preventObserver__=!1}),P&&b.forEach(e=>{u[e].swiperLoopMoveDOM=!0,p.prepend(u[e]),u[e].swiperLoopMoveDOM=!1}),T&&x.forEach(e=>{u[e].swiperLoopMoveDOM=!0,p.append(u[e]),u[e].swiperLoopMoveDOM=!1}),d.recalcSlides(),"auto"===m.slidesPerView?d.updateSlides():w&&(b.length>0&&P||x.length>0&&T)&&d.slides.forEach((e,t)=>{d.grid.updateSlide(t,e,d.slides)}),m.watchSlidesProgress&&d.updateSlidesOffset(),i){if(b.length>0&&P){if(void 0===t){let e=d.slidesGrid[S],t=d.slidesGrid[S+E]-e;o?d.setTranslate(d.translate-t):(d.slideTo(S+Math.ceil(E),0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else if(r){let e=w?b.length/m.grid.rows:b.length;d.slideTo(d.activeIndex+e,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(x.length>0&&T){if(void 0===t){let e=d.slidesGrid[S],t=d.slidesGrid[S-C]-e;o?d.setTranslate(d.translate-t):(d.slideTo(S-C,0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else{let e=w?x.length/m.grid.rows:x.length;d.slideTo(d.activeIndex-e,0,!1,!0)}}}if(d.allowSlidePrev=h,d.allowSlideNext=c,d.controller&&d.controller.control&&!a){let e={slideRealIndex:t,direction:s,setTranslate:r,activeSlideIndex:n,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===m.slidesPerView&&i})}):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...e,slideTo:d.controller.control.params.slidesPerView===m.slidesPerView&&i})}d.emit("loopFix")},loopDestroy:function(){let{params:e,slidesEl:t}=this;if(!e.loop||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let i=[];this.slides.forEach(e=>{i[void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),i.forEach(e=>{t.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(e){let t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;let i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){let e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){let{params:e}=this;this.onTouchStart=y.bind(this),this.onTouchMove=w.bind(this),this.onTouchEnd=b.bind(this),this.onDocumentTouchStart=E.bind(this),e.cssMode&&(this.onScroll=T.bind(this)),this.onClick=S.bind(this),this.onLoad=P.bind(this),C(this,"on")},detachEvents:function(){C(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{realIndex:t,initialized:i,params:s,el:r}=e,n=s.breakpoints;if(!n||n&&0===Object.keys(n).length)return;let a=e.getBreakpoint(n,e.params.breakpointsBase,e.el);if(!a||e.currentBreakpoint===a)return;let o=(a in n?n[a]:void 0)||e.originalParams,d=M(e,s),u=M(e,o),h=e.params.grabCursor,c=o.grabCursor,p=s.enabled;d&&!u?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),e.emitContainerClasses()):!d&&u&&(r.classList.add(`${s.containerModifierClass}grid`),(o.grid.fill&&"column"===o.grid.fill||!o.grid.fill&&"column"===s.grid.fill)&&r.classList.add(`${s.containerModifierClass}grid-column`),e.emitContainerClasses()),h&&!c?e.unsetGrabCursor():!h&&c&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===o[t])return;let i=s[t]&&s[t].enabled,r=o[t]&&o[t].enabled;i&&!r&&e[t].disable(),!i&&r&&e[t].enable()});let m=o.direction&&o.direction!==s.direction,f=s.loop&&(o.slidesPerView!==s.slidesPerView||m),v=s.loop;m&&i&&e.changeDirection(),(0,l.w)(e.params,o);let g=e.params.enabled,y=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),p&&!g?e.disable():!p&&g&&e.enable(),e.currentBreakpoint=a,e.emit("_beforeBreakpoint",o),i&&(f?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!v&&y?(e.loopCreate(t),e.updateSlides()):v&&!y&&e.loopDestroy()),e.emit("breakpoint",o)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let s=!1,r=(0,o.a)(),n="window"===t?r.innerHeight:i.clientHeight,a=Object.keys(e).map(e=>"string"==typeof e&&0===e.indexOf("@")?{value:n*parseFloat(e.substr(1)),point:e}:{value:e,point:e});a.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<a.length;e+=1){let{point:n,value:o}=a[e];"window"===t?r.matchMedia(`(min-width: ${o}px)`).matches&&(s=n):o<=i.clientWidth&&(s=n)}return s||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:e,params:t}=this,{slidesOffsetBefore:i}=t;if(i){let e=this.slides.length-1,t=this.slidesGrid[e]+this.slidesSizesGrid[e]+2*i;this.isLocked=this.size>t}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:i,el:s,device:r}=this,n=function(e,t){let i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(s=>{e[s]&&i.push(t+s)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",t.direction,{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...n),s.classList.add(...e),this.emitContainerClasses()},removeClasses:function(){let{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},L={};class V{constructor(){let e,t;for(var i=arguments.length,s=Array(i),r=0;r<i;r++)s[r]=arguments[r];1===s.length&&s[0].constructor&&"Object"===Object.prototype.toString.call(s[0]).slice(8,-1)?t=s[0]:[e,t]=s,t||(t={}),t=(0,l.w)({},t),e&&!t.el&&(t.el=e);let a=(0,o.g)();if(t.el&&"string"==typeof t.el&&a.querySelectorAll(t.el).length>1){let e=[];return a.querySelectorAll(t.el).forEach(i=>{let s=(0,l.w)({},t,{el:i});e.push(new V(s))}),e}let h=this;h.__swiper__=!0,h.support=d(),h.device=u({userAgent:t.userAgent}),h.browser=(n||(n=function(){let e=(0,o.a)(),t=u(),i=!1;function s(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}if(s()){let t=String(e.navigator.userAgent);if(t.includes("Version/")){let[e,s]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));i=e<16||16===e&&s<2}}let r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),n=s(),a=n||r&&t.ios;return{isSafari:i||n,needPerspectiveFix:i,need3dFix:a,isWebView:r}}()),n),h.eventsListeners={},h.eventsAnyListeners=[],h.modules=[...h.__modules__],t.modules&&Array.isArray(t.modules)&&h.modules.push(...t.modules);let c={};h.modules.forEach(e=>{var i;e({params:t,swiper:h,extendParams:(i=t,function(e){void 0===e&&(e={});let t=Object.keys(e)[0],s=e[t];if("object"!=typeof s||null===s||(!0===i[t]&&(i[t]={enabled:!0}),"navigation"===t&&i[t]&&i[t].enabled&&!i[t].prevEl&&!i[t].nextEl&&(i[t].auto=!0),["pagination","scrollbar"].indexOf(t)>=0&&i[t]&&i[t].enabled&&!i[t].el&&(i[t].auto=!0),!(t in i&&"enabled"in s))){(0,l.w)(c,e);return}"object"!=typeof i[t]||"enabled"in i[t]||(i[t].enabled=!0),i[t]||(i[t]={enabled:!1}),(0,l.w)(c,e)}),on:h.on.bind(h),once:h.once.bind(h),off:h.off.bind(h),emit:h.emit.bind(h)})});let p=(0,l.w)({},A,c);return h.params=(0,l.w)({},p,L,t),h.originalParams=(0,l.w)({},h.params),h.passedParams=(0,l.w)({},t),h.params&&h.params.on&&Object.keys(h.params.on).forEach(e=>{h.on(e,h.params.on[e])}),h.params&&h.params.onAny&&h.onAny(h.params.onAny),Object.assign(h,{enabled:h.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===h.params.direction,isVertical:()=>"vertical"===h.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:h.params.allowSlideNext,allowSlidePrev:h.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:h.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:h.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),h.emit("_swiper"),h.params.init&&h.init(),h}getDirectionLabel(e){return this.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}getSlideIndex(e){let{slidesEl:t,params:i}=this,s=(0,l.e)(t,`.${i.slideClass}, swiper-slide`),r=(0,l.h)(s[0]);return(0,l.h)(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter(t=>1*t.getAttribute("data-swiper-slide-index")===e)[0])}recalcSlides(){let{slidesEl:e,params:t}=this;this.slides=(0,l.e)(e,`.${t.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let i=this.minTranslate(),s=(this.maxTranslate()-i)*e+i;this.translateTo(s,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(i=>{let s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:i,slides:s,slidesGrid:r,slidesSizesGrid:n,size:a,activeIndex:o}=this,l=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=s[o]?Math.ceil(s[o].swiperSlideSize):0;for(let i=o+1;i<s.length;i+=1)s[i]&&!e&&(t+=Math.ceil(s[i].swiperSlideSize),l+=1,t>a&&(e=!0));for(let i=o-1;i>=0;i-=1)s[i]&&!e&&(t+=s[i].swiperSlideSize,l+=1,t>a&&(e=!0))}else if("current"===e)for(let e=o+1;e<s.length;e+=1)(t?r[e]+n[e]-r[o]<a:r[e]-r[o]<a)&&(l+=1);else for(let e=o-1;e>=0;e-=1)r[o]-r[e]<a&&(l+=1);return l}update(){let e;let t=this;if(!t||t.destroyed)return;let{snapGrid:i,params:s}=t;function r(){let e=Math.min(Math.max(t.rtlTranslate?-1*t.translate:t.translate,t.maxTranslate()),t.minTranslate());t.setTranslate(e),t.updateActiveIndex(),t.updateSlidesClasses()}if(s.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&p(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),s.freeMode&&s.freeMode.enabled&&!s.cssMode)r(),s.autoHeight&&t.updateAutoHeight();else{if(("auto"===s.slidesPerView||s.slidesPerView>1)&&t.isEnd&&!s.centeredSlides){let i=t.virtual&&s.virtual.enabled?t.virtual.slides:t.slides;e=t.slideTo(i.length-1,0,!1,!0)}else e=t.slideTo(t.activeIndex,0,!1,!0);e||r()}s.watchOverflow&&i!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let i=this.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(this.el.classList.remove(`${this.params.containerModifierClass}${i}`),this.el.classList.add(`${this.params.containerModifierClass}${e}`),this.emitContainerClasses(),this.params.direction=e,this.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),this.emit("changeDirection"),t&&this.update()),this}changeLanguageDirection(e){(!this.rtl||"rtl"!==e)&&(this.rtl||"ltr"!==e)&&(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(e){let t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let s=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,r=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(s()):(0,l.e)(i,s())[0];return!r&&t.params.createElements&&(r=(0,l.c)("div",t.params.wrapperClass),i.append(r),(0,l.e)(i,`.${t.params.slideClass}`).forEach(e=>{r.append(e)})),Object.assign(t,{el:i,wrapperEl:r,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:r,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===(0,l.p)(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===(0,l.p)(i,"direction")),wrongRTL:"-webkit-box"===(0,l.p)(r,"display")}),!0}init(e){let t=this;if(t.initialized||!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();let i=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&i.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(e=>{e.complete?p(t,e):e.addEventListener("load",e=>{p(t,e.target)})}),f(t),t.initialized=!0,f(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let i=this,{params:s,el:r,wrapperEl:n,slides:a}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),t&&(i.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),n&&n.removeAttribute("style"),a&&a.length&&a.forEach(e=>{e.classList.remove(s.slideVisibleClass,s.slideFullyVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),(0,l.x)(i)),i.destroyed=!0),null}static extendDefaults(e){(0,l.w)(L,e)}static get extendedDefaults(){return L}static get defaults(){return A}static installModule(e){V.prototype.__modules__||(V.prototype.__modules__=[]);let t=V.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>V.installModule(e)):V.installModule(e),V}}Object.keys(k).forEach(e=>{Object.keys(k[e]).forEach(t=>{V.prototype[t]=k[e][t]})}),V.use([function(e){let{swiper:t,on:i,emit:s}=e,r=(0,o.a)(),n=null,a=null,l=()=>{t&&!t.destroyed&&t.initialized&&(s("beforeResize"),s("resize"))},d=()=>{t&&!t.destroyed&&t.initialized&&(n=new ResizeObserver(e=>{a=r.requestAnimationFrame(()=>{let{width:i,height:s}=t,r=i,n=s;e.forEach(e=>{let{contentBoxSize:i,contentRect:s,target:a}=e;a&&a!==t.el||(r=s?s.width:(i[0]||i).inlineSize,n=s?s.height:(i[0]||i).blockSize)}),(r!==i||n!==s)&&l()})})).observe(t.el)},u=()=>{a&&r.cancelAnimationFrame(a),n&&n.unobserve&&t.el&&(n.unobserve(t.el),n=null)},h=()=>{t&&!t.destroyed&&t.initialized&&s("orientationchange")};i("init",()=>{if(t.params.resizeObserver&&void 0!==r.ResizeObserver){d();return}r.addEventListener("resize",l),r.addEventListener("orientationchange",h)}),i("destroy",()=>{u(),r.removeEventListener("resize",l),r.removeEventListener("orientationchange",h)})},function(e){let{swiper:t,extendParams:i,on:s,emit:r}=e,n=[],a=(0,o.a)(),d=function(e,i){void 0===i&&(i={});let s=new(a.MutationObserver||a.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length){r("observerUpdate",e[0]);return}let i=function(){r("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(i):a.setTimeout(i,0)});s.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:t.isElement||(void 0===i.childList||i).childList,characterData:void 0===i.characterData||i.characterData}),n.push(s)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",()=>{if(t.params.observer){if(t.params.observeParents){let e=(0,l.a)(t.hostEl);for(let t=0;t<e.length;t+=1)d(e[t])}d(t.hostEl,{childList:t.params.observeSlideChildren}),d(t.wrapperEl,{attributes:!1})}}),s("destroy",()=>{n.forEach(e=>{e.disconnect()}),n.splice(0,n.length)})}]);let D=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function O(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function R(e,t){let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:O(t[i])&&O(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:R(e[i],t[i]):e[i]=t[i]})}function I(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function _(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function F(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function j(e){void 0===e&&(e="");let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),i=[];return t.forEach(e=>{0>i.indexOf(e)&&i.push(e)}),i.join(" ")}let B=e=>{e&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function z(){return(z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(e[s]=i[s])}return e}).apply(this,arguments)}function $(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function N(e,t){return"undefined"==typeof window?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)}let G=(0,a.createContext)(null),H=(0,a.createContext)(null),W=(0,a.forwardRef)(function(e,t){var i;let{className:s,tag:r="div",wrapperTag:n="div",children:o,onSwiper:l,...d}=void 0===e?{}:e,u=!1,[h,c]=(0,a.useState)("swiper"),[p,m]=(0,a.useState)(null),[f,v]=(0,a.useState)(!1),g=(0,a.useRef)(!1),y=(0,a.useRef)(null),w=(0,a.useRef)(null),b=(0,a.useRef)(null),x=(0,a.useRef)(null),S=(0,a.useRef)(null),T=(0,a.useRef)(null),P=(0,a.useRef)(null),E=(0,a.useRef)(null),{params:C,passedParams:M,rest:k,events:L}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);let i={on:{}},s={},r={};R(i,A),i._emitClasses=!0,i.init=!1;let n={},a=D.map(e=>e.replace(/_/,""));return Object.keys(Object.assign({},e)).forEach(o=>{void 0!==e[o]&&(a.indexOf(o)>=0?O(e[o])?(i[o]={},r[o]={},R(i[o],e[o]),R(r[o],e[o])):(i[o]=e[o],r[o]=e[o]):0===o.search(/on[A-Z]/)&&"function"==typeof e[o]?t?s[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:i.on[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:n[o]=e[o])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===i[e]&&(i[e]={}),!1===i[e]&&delete i[e]}),{params:i,passedParams:r,rest:n,events:s}}(d),{slides:G,slots:W}=function(e){let t=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return a.Children.toArray(e).forEach(e=>{if($(e))t.push(e);else if(e.props&&e.props.slot&&i[e.props.slot])i[e.props.slot].push(e);else if(e.props&&e.props.children){let s=function e(t){let i=[];return a.Children.toArray(t).forEach(t=>{$(t)?i.push(t):t.props&&t.props.children&&e(t.props.children).forEach(e=>i.push(e))}),i}(e.props.children);s.length>0?s.forEach(e=>t.push(e)):i["container-end"].push(e)}else i["container-end"].push(e)}),{slides:t,slots:i}}(o),U=()=>{v(!f)};Object.assign(C.on,{_containerClasses(e,t){c(t)}});let q=()=>{Object.assign(C.on,L),u=!0;let e={...C};if(delete e.wrapperClass,w.current=new V(e),w.current.virtual&&w.current.params.virtual.enabled){w.current.virtual.slides=G;let e={cache:!1,slides:G,renderExternal:m,renderExternalUpdate:!1};R(w.current.params.virtual,e),R(w.current.originalParams.virtual,e)}};y.current||q(),w.current&&w.current.on("_beforeBreakpoint",U);let Y=()=>{!u&&L&&w.current&&Object.keys(L).forEach(e=>{w.current.on(e,L[e])})},X=()=>{L&&w.current&&Object.keys(L).forEach(e=>{w.current.off(e,L[e])})};return(0,a.useEffect)(()=>()=>{w.current&&w.current.off("_beforeBreakpoint",U)}),(0,a.useEffect)(()=>{!g.current&&w.current&&(w.current.emitSlidesClasses(),g.current=!0)}),N(()=>{if(t&&(t.current=y.current),y.current)return w.current.destroyed&&q(),function(e,t){let{el:i,nextEl:s,prevEl:r,paginationEl:n,scrollbarEl:a,swiper:o}=e;I(t)&&s&&r&&(o.params.navigation.nextEl=s,o.originalParams.navigation.nextEl=s,o.params.navigation.prevEl=r,o.originalParams.navigation.prevEl=r),_(t)&&n&&(o.params.pagination.el=n,o.originalParams.pagination.el=n),F(t)&&a&&(o.params.scrollbar.el=a,o.originalParams.scrollbar.el=a),o.init(i)}({el:y.current,nextEl:S.current,prevEl:T.current,paginationEl:P.current,scrollbarEl:E.current,swiper:w.current},C),l&&!w.current.destroyed&&l(w.current),()=>{w.current&&!w.current.destroyed&&w.current.destroy(!0,!1)}},[]),N(()=>{Y();let e=function(e,t,i,s,r){let n=[];if(!t)return n;let a=e=>{0>n.indexOf(e)&&n.push(e)};if(i&&s){let e=s.map(r),t=i.map(r);e.join("")!==t.join("")&&a("children"),s.length!==i.length&&a("children")}return D.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,"")).forEach(i=>{if(i in e&&i in t){if(O(e[i])&&O(t[i])){let s=Object.keys(e[i]),r=Object.keys(t[i]);s.length!==r.length?a(i):(s.forEach(s=>{e[i][s]!==t[i][s]&&a(i)}),r.forEach(s=>{e[i][s]!==t[i][s]&&a(i)}))}else e[i]!==t[i]&&a(i)}}),n}(M,b.current,G,x.current,e=>e.key);return b.current=M,x.current=G,e.length&&w.current&&!w.current.destroyed&&function(e){let t,i,s,r,n,a,o,l,{swiper:d,slides:u,passedParams:h,changedParams:c,nextEl:p,prevEl:m,scrollbarEl:f,paginationEl:v}=e,g=c.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:y,pagination:w,navigation:b,scrollbar:x,virtual:S,thumbs:T}=d;c.includes("thumbs")&&h.thumbs&&h.thumbs.swiper&&y.thumbs&&!y.thumbs.swiper&&(t=!0),c.includes("controller")&&h.controller&&h.controller.control&&y.controller&&!y.controller.control&&(i=!0),c.includes("pagination")&&h.pagination&&(h.pagination.el||v)&&(y.pagination||!1===y.pagination)&&w&&!w.el&&(s=!0),c.includes("scrollbar")&&h.scrollbar&&(h.scrollbar.el||f)&&(y.scrollbar||!1===y.scrollbar)&&x&&!x.el&&(r=!0),c.includes("navigation")&&h.navigation&&(h.navigation.prevEl||m)&&(h.navigation.nextEl||p)&&(y.navigation||!1===y.navigation)&&b&&!b.prevEl&&!b.nextEl&&(n=!0);let P=e=>{d[e]&&(d[e].destroy(),"navigation"===e?(d.isElement&&(d[e].prevEl.remove(),d[e].nextEl.remove()),y[e].prevEl=void 0,y[e].nextEl=void 0,d[e].prevEl=void 0,d[e].nextEl=void 0):(d.isElement&&d[e].el.remove(),y[e].el=void 0,d[e].el=void 0))};c.includes("loop")&&d.isElement&&(y.loop&&!h.loop?a=!0:!y.loop&&h.loop?o=!0:l=!0),g.forEach(e=>{if(O(y[e])&&O(h[e]))Object.assign(y[e],h[e]),("navigation"===e||"pagination"===e||"scrollbar"===e)&&"enabled"in h[e]&&!h[e].enabled&&P(e);else{let t=h[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&P(e):y[e]=h[e]}}),g.includes("controller")&&!i&&d.controller&&d.controller.control&&y.controller&&y.controller.control&&(d.controller.control=y.controller.control),c.includes("children")&&u&&S&&y.virtual.enabled?(S.slides=u,S.update(!0)):c.includes("virtual")&&S&&y.virtual.enabled&&(u&&(S.slides=u),S.update(!0)),c.includes("children")&&u&&y.loop&&(l=!0),t&&T.init()&&T.update(!0),i&&(d.controller.control=y.controller.control),s&&(d.isElement&&(!v||"string"==typeof v)&&((v=document.createElement("div")).classList.add("swiper-pagination"),v.part.add("pagination"),d.el.appendChild(v)),v&&(y.pagination.el=v),w.init(),w.render(),w.update()),r&&(d.isElement&&(!f||"string"==typeof f)&&((f=document.createElement("div")).classList.add("swiper-scrollbar"),f.part.add("scrollbar"),d.el.appendChild(f)),f&&(y.scrollbar.el=f),x.init(),x.updateSize(),x.setTranslate()),n&&(d.isElement&&(p&&"string"!=typeof p||((p=document.createElement("div")).classList.add("swiper-button-next"),p.innerHTML=d.hostEl.constructor.nextButtonSvg,p.part.add("button-next"),d.el.appendChild(p)),m&&"string"!=typeof m||((m=document.createElement("div")).classList.add("swiper-button-prev"),m.innerHTML=d.hostEl.constructor.prevButtonSvg,m.part.add("button-prev"),d.el.appendChild(m))),p&&(y.navigation.nextEl=p),m&&(y.navigation.prevEl=m),b.init(),b.update()),c.includes("allowSlideNext")&&(d.allowSlideNext=h.allowSlideNext),c.includes("allowSlidePrev")&&(d.allowSlidePrev=h.allowSlidePrev),c.includes("direction")&&d.changeDirection(h.direction,!1),(a||l)&&d.loopDestroy(),(o||l)&&d.loopCreate(),d.update()}({swiper:w.current,slides:G,passedParams:M,changedParams:e,nextEl:S.current,prevEl:T.current,scrollbarEl:E.current,paginationEl:P.current}),()=>{X()}}),N(()=>{B(w.current)},[p]),a.createElement(r,z({ref:y,className:j(`${h}${s?` ${s}`:""}`)},k),a.createElement(H.Provider,{value:w.current},W["container-start"],a.createElement(n,{className:(void 0===(i=C.wrapperClass)&&(i=""),i)?i.includes("swiper-wrapper")?i:`swiper-wrapper ${i}`:"swiper-wrapper"},W["wrapper-start"],C.virtual?function(e,t,i){if(!i)return null;let s=e=>{let i=e;return e<0?i=t.length+e:i>=t.length&&(i-=t.length),i},r=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:n,to:o}=i,l=e.params.loop?-t.length:0,d=e.params.loop?2*t.length:t.length,u=[];for(let e=l;e<d;e+=1)e>=n&&e<=o&&u.push(t[s(e)]);return u.map((t,i)=>a.cloneElement(t,{swiper:e,style:r,key:t.props.virtualIndex||t.key||`slide-${i}`}))}(w.current,G,p):G.map((e,t)=>a.cloneElement(e,{swiper:w.current,swiperSlideIndex:t})),W["wrapper-end"]),I(C)&&a.createElement(a.Fragment,null,a.createElement("div",{ref:T,className:"swiper-button-prev"}),a.createElement("div",{ref:S,className:"swiper-button-next"})),F(C)&&a.createElement("div",{ref:E,className:"swiper-scrollbar"}),_(C)&&a.createElement("div",{ref:P,className:"swiper-pagination"}),W["container-end"]))});W.displayName="Swiper";let U=(0,a.forwardRef)(function(e,t){let{tag:i="div",children:s,className:r="",swiper:n,zoom:o,lazy:l,virtualIndex:d,swiperSlideIndex:u,...h}=void 0===e?{}:e,c=(0,a.useRef)(null),[p,m]=(0,a.useState)("swiper-slide"),[f,v]=(0,a.useState)(!1);function g(e,t,i){t===c.current&&m(i)}N(()=>{if(void 0!==u&&(c.current.swiperSlideIndex=u),t&&(t.current=c.current),c.current&&n){if(n.destroyed){"swiper-slide"!==p&&m("swiper-slide");return}return n.on("_slideClass",g),()=>{n&&n.off("_slideClass",g)}}}),N(()=>{n&&c.current&&!n.destroyed&&m(n.getSlideClasses(c.current))},[n]);let y={isActive:p.indexOf("swiper-slide-active")>=0,isVisible:p.indexOf("swiper-slide-visible")>=0,isPrev:p.indexOf("swiper-slide-prev")>=0,isNext:p.indexOf("swiper-slide-next")>=0},w=()=>"function"==typeof s?s(y):s;return a.createElement(i,z({ref:c,className:j(`${p}${r?` ${r}`:""}`),"data-swiper-slide-index":d,onLoad:()=>{v(!0)}},h),o&&a.createElement(G.Provider,{value:y},a.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof o?o:void 0},w(),l&&!f&&a.createElement("div",{className:"swiper-lazy-preloader"}))),!o&&a.createElement(G.Provider,{value:y},w(),l&&!f&&a.createElement("div",{className:"swiper-lazy-preloader"})))});U.displayName="SwiperSlide"}}]);