'use client'

import { Monitor, Smartphone, Palette, Globe, Settings, Lightbulb, Search, Code, Rocket } from 'lucide-react'
import Header from '@/components/Header'

const services = [
  {
    icon: Monitor,
    title: "Développement Web",
    description: "Applications web modernes et performantes. React, Vue.js, Node.js - nous maîtrisons les technologies de pointe pour créer des expériences utilisateur exceptionnelles."
  },
  {
    icon: Smartphone,
    title: "Applications Mobile",
    description: "Applications natives et cross-platform qui captivent vos utilisateurs. iOS, Android, React Native, Flutter - nous donnons vie à vos idées sur tous les écrans."
  },
  {
    icon: Palette,
    title: "Design & UX/UI",
    description: "Designs qui convertissent et enchantent. De la recherche utilisateur aux prototypes interactifs, nous créons des interfaces intuitives."
  },
  {
    icon: Globe,
    title: "Branding Digital",
    description: "Identités visuelles mémorables qui marquent les esprits. Logo, charte graphique, guidelines - nous construisons l'ADN visuel de votre marque."
  },
  {
    icon: Settings,
    title: "Maintenance & Support",
    description: "Accompagnement technique continu pour faire évoluer vos projets. Monitoring, mises à jour, optimisations - nous veillons sur vos applications."
  },
  {
    icon: Lightbulb,
    title: "Consulting Technique",
    description: "Expertise stratégique pour orienter vos décisions technologiques. Architecture, choix techniques, roadmap - nous vous guidons vers les meilleures solutions."
  }
]

const processes = [
  {
    icon: Search,
    title: "Découverte",
    description: "Nous analysons vos besoins, votre marché et vos objectifs pour définir la stratégie digitale optimale. Ateliers collaboratifs, recherche utilisateur et définition du MVP."
  },
  {
    icon: Palette,
    title: "Conception",
    description: "Création de wireframes, maquettes et prototypes interactifs. Tests utilisateurs et itérations pour valider l'expérience avant le développement."
  },
  {
    icon: Code,
    title: "Développement",
    description: "Développement en sprints avec livraisons régulières. Code clean, tests automatisés et intégration continue pour une qualité irréprochable."
  },
  {
    icon: Rocket,
    title: "Lancement",
    description: "Déploiement sécurisé, formation de vos équipes et monitoring des performances. Optimisations continues basées sur les données d'usage."
  }
]

// Simple Circle Component for now
const CircleElement = ({ icon: Icon }: { icon: any }) => (
  <div className="w-full flex justify-center">
    <div className="w-64 h-64 rounded-full bg-gradient-to-br from-primary-green/20 to-primary-green/5 border border-primary-green/30 flex items-center justify-center">
      <Icon className="w-16 h-16 text-primary-green" />
    </div>
  </div>
)

export default function Home() {
  return (
    <div className="snap-y snap-mandatory overflow-y-scroll h-[100dvh] min-h-[100dvh] w-full overflow-x-hidden no-scrollbar">
      <Header />

      {/* Hero Section */}
      <div className="snap-always snap-center min-h-screen">
        <div className="relative h-[100dvh] min-h-[100dvh] w-full flex">
          <div className="w-full h-full">
            <div className="box-border w-full snap-always snap-center h-[100dvh] min-h-[100dvh] flex flex-col items-center justify-center bg-bg-primary">
              <div className="w-full flex flex-col items-center justify-center gap-12 px-4 md:px-8 lg:px-12 mx-auto">
                <div>
                  <p className="text-5xl md:text-7xl lg:text-8xl text-center text-text-primary">
                    Nous créons des{' '}
                    <span className="gradient-text">applications exceptionnelles</span>
                  </p>
                  <p className="text-5xl md:text-7xl lg:text-8xl w-full text-center text-text-primary mt-4">
                    qui transforment vos idées
                  </p>
                </div>
                <p className="text-2xl md:text-3xl lg:text-3xl tracking-normal text-text-secondary">
                  Design élégant, code robuste, résultats mesurables.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Services Introduction */}
      <div className="snap-always snap-center min-h-screen">
        <div className="relative h-[100dvh] min-h-[100dvh] w-full flex">
          <div className="w-full h-full">
            <div className="box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-secondary">
              <p className="text-2xl md:text-5xl w-full text-center leading-tight whitespace-pre-wrap px-4 md:px-8 lg:px-12">
                Nous façonnons l'excellence en{' '}
                <span className="gradient-text">développement web</span>,{' '}
                <span className="gradient-text">applications mobiles</span> et{' '}
                <span className="gradient-text">design digital</span>.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Individual Service Sections */}
      {services.map((service, index) => {
        const Icon = service.icon
        return (
          <div key={index} className="snap-always snap-center min-h-screen">
            <div className="relative h-[100dvh] min-h-[100dvh] w-full flex">
              <div className="w-full h-full">
                <div className="relative w-full h-[100dvh] min-h-[100dvh] flex items-center justify-center text-white" style={{ backgroundColor: '#1C1B1C' }}>
                  <CircleElement icon={Icon} />

                  {/* Content overlay */}
                  <div className="fixed w-11/12 lg:w-full bottom-5 md:bottom-10 lg:bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-4 text-center pointer-events-none z-50">
                    <p className="text-6xl lg:text-7xl font-light text-white">{service.title}</p>
                    <p className="text-base lg:text-lg lg:w-6/12 text-[#A0A4A1] px-1 lg:px-2">{service.description}</p>
                  </div>

                  {/* Side navigation */}
                  <div className="absolute left-1/2 lg:left-2 lg:top-1/2 bottom-[1vh] -translate-x-1/2 lg:-translate-x-0 lg:-translate-y-1/2 -translate-y-1/2 flex flex-row lg:flex-col lg:items-start items-center justify-center lg:gap-2 gap-6 px-4 md:px-8 lg:px-12 mx-auto pointer-events-auto z-[999]">
                    {services.map((_, i) => (
                      <button
                        key={i}
                        className={`text-left transition-colors duration-300 ${
                          i === index ? 'text-white' : 'text-gray-500'
                        }`}
                        style={{ fontFamily: 'monospace' }}
                      >
                        {service.title.toUpperCase().replace(/[^A-Z]/g, '').slice(0, 8)}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      })}

      {/* Process Introduction */}
      <div className="snap-always snap-center min-h-screen">
        <div className="relative h-[100dvh] min-h-[100dvh] w-full flex">
          <div className="w-full h-full">
            <div className="box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-primary">
              <p className="text-2xl md:text-5xl w-full text-center leading-tight whitespace-pre-wrap px-4 md:px-8 lg:px-12">
                Notre méthodologie éprouvée en{' '}
                <span className="gradient-text">4 étapes</span>{' '}
                pour transformer vos idées en{' '}
                <span className="gradient-text">succès digitaux</span>.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Individual Process Sections */}
      {processes.map((process, index) => {
        const Icon = process.icon
        return (
          <div key={index} className="snap-always snap-center min-h-screen">
            <div className="relative h-[100dvh] min-h-[100dvh] w-full flex">
              <div className="w-full h-full">
                <div className="relative w-full h-[100dvh] min-h-[100dvh] flex items-center justify-center text-white" style={{ backgroundColor: '#1C1B1C' }}>
                  <CircleElement icon={Icon} />

                  {/* Content overlay */}
                  <div className="fixed w-11/12 lg:w-full bottom-5 md:bottom-10 lg:bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-4 text-center pointer-events-none z-50">
                    <p className="text-6xl lg:text-7xl font-light text-white">{process.title}</p>
                    <p className="text-base lg:text-lg lg:w-6/12 text-[#A0A4A1] px-1 lg:px-2">{process.description}</p>
                  </div>

                  {/* Side navigation */}
                  <div className="absolute left-1/2 lg:left-2 lg:top-1/2 bottom-[1vh] -translate-x-1/2 lg:-translate-x-0 lg:-translate-y-1/2 -translate-y-1/2 flex flex-row lg:flex-col lg:items-start items-center justify-center lg:gap-2 gap-6 px-4 md:px-8 lg:px-12 mx-auto pointer-events-auto z-[999]">
                    {processes.map((_, i) => (
                      <button
                        key={i}
                        className={`text-left transition-colors duration-300 ${
                          i === index ? 'text-white' : 'text-gray-500'
                        }`}
                        style={{ fontFamily: 'monospace' }}
                      >
                        {process.title.toUpperCase()}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      })}

      {/* CTA Section */}
      <div className="snap-always snap-center min-h-screen">
        <div className="relative h-[100dvh] min-h-[100dvh] w-full flex">
          <div className="w-full h-full">
            <div className="box-border gap-8 w-full h-[100dvh] min-h-[100dvh] snap-always snap-center flex flex-col will-change-transform justify-center items-center text-text-primary leading-normal tracking-normal bg-bg-secondary">
              <div className="text-center">
                <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6">
                  Prêt à transformer{' '}
                  <span className="gradient-text">votre vision</span>{' '}
                  en réalité ?
                </h2>
                <p className="text-xl md:text-2xl text-text-secondary max-w-3xl mx-auto mb-12">
                  Discutons de votre projet et découvrons ensemble comment nous pouvons vous aider à atteindre vos objectifs.
                </p>
                <button className="shimmer-effect bg-gradient-to-r from-primary-green to-primary-green-dark text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary-green/25">
                  Démarrer votre projet
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
