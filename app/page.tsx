'use client'

import Header from '@/components/Header'
import Hero from '@/components/Hero'
import ServicesScroll from '@/components/ServicesScroll'
import ProcessScroll from '@/components/ProcessScroll'
import CTAFooter from '@/components/CTAFooter'

export default function Home() {
  return (
    <div className="snap-y snap-mandatory overflow-y-scroll h-screen w-full overflow-x-hidden">
      <Header />

      {/* Hero Section */}
      <section className="scroll-section relative flex items-center justify-center bg-bg-primary">
        <Hero />
      </section>

      {/* Services Sections */}
      <ServicesScroll />

      {/* Process Sections */}
      <ProcessScroll />

      {/* CTA Section */}
      <section className="scroll-section relative flex items-center justify-center bg-bg-secondary">
        <CTAFooter />
      </section>
    </div>
  )
}
